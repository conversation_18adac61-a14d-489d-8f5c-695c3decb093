<?php namespace api\apps\avanzamenti;

use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;

class Manager {

    /**
     * @var Repository
     */
    protected $avanzamenti;

    /**
     * @var Repository
     */
    protected $avanzamentiPolicies;

    public function getByAgenzia($agenziaId, $year) {
        try {
            $criteria = "agenzia_id,EQ,$agenziaId|year,EQ,$year";

            if (! $monthlyData = $this->avanzamenti->fetchAll(null, null, "month asc", $criteria)) {
                return null;
            }

            return $this->processData($monthlyData, $this->loadObjectives($agenziaId, $year));
        }

        catch (\Exception $ex) {
            // @TODO Log error
            throw $ex;
        }
    }

    protected function processData($monthlyData, $objectives = null) {
        $rp1 = $this->processRP1($monthlyData, $objectives);
        $rp2 = $this->processRP2($monthlyData, $objectives);
        $v1 = $this->processV1($monthlyData, $objectives);

        // Disabled by GA directive.
        $v2 = $this->processV2($monthlyData, $objectives);

        return array_merge($rp1, $rp2, $v1, $v2);
    }

    protected function processRP1($monthlyData, $objectives = null) {
        $result = [];
        $monthlyObjective = $objectives ? $objectives->ramiPrefAnnL1IncassiCur / 12 : null;

        foreach ($monthlyData as $data) {
            if ($objectives) {
                $result[] = (object) [
                    'year' => $data->year,
                    'month' => $data->month,
                    'agenzia_id' => trim($data->agenzia_id),
                    'level' => 1,
                    'type' => 'RP',
                    // Headings.
                    'consumptivePrevious' => $objectives->ramiPrefAnnL1IncassiPrev,
                    'objectiveYear' => $objectives->ramiPrefAnnL1IncassiCur,
                    'objectiveYearDelta' => $objectives->ramiPrefAnnL1IncassiCur - $objectives->ramiPrefAnnL1IncassiPrev,
                    //'objectiveYearIncrease_' => ($objectives->ramiPrefAnnL1IncassiCur - $objectives->ramiPrefAnnL1IncassiPrev) / $objectives->ramiPrefAnnL1IncassiPrev * 100,
                    'objectiveYearIncrease' => $this->ratio($objectives->ramiPrefAnnL1IncassiCur - $objectives->ramiPrefAnnL1IncassiPrev, $objectives->ramiPrefAnnL1IncassiPrev, 100),
                    'objectiveMonth' => $monthlyObjective,
                    'objectiveMonthCumulative' => $monthlyObjective * $data->month,
                    // Month data.
                    'consumptiveMonth' => $data->ramiPrefIncassi,
                    'consumptiveMonthDeltaYear' => $data->ramiPrefIncassi - $objectives->ramiPrefAnnL1IncassiCur,
                    //'consumptiveMonthIncreaseYear_' => $data->ramiPrefIncassi / $objectives->ramiPrefAnnL1IncassiCur * 100,
                    'consumptiveMonthIncreaseYear' => $this->ratio($data->ramiPrefIncassi, $objectives->ramiPrefAnnL1IncassiCur, 100),
                    'consumptiveMonthDeltaMonth' => $data->ramiPrefIncassi - $monthlyObjective * $data->month,
                    //'consumptiveMonthIncreaseMonth_' => $data->ramiPrefIncassi / ($monthlyObjective * $data->month) * 100,
                    'consumptiveMonthIncreaseMonth' => $this->ratio($data->ramiPrefIncassi, ($monthlyObjective * $data->month), 100),
                    'rappel' => $data->ramiPrefL1Rappel,
                ];

                continue;
            }

            $result[] = (object) [
                'year' => $data->year,
                'month' => $data->month,
                'agenzia_id' => trim($data->agenzia_id),
                'level' => 1,
                'type' => 'RP',
                'consumptiveMonth' => $data->ramiPrefIncassi,
                'rappel' => $data->ramiPrefL1Rappel,
            ];
        }

        return $result;
    }

    protected function processRP2($monthlyData, $objectives = null) {
        if ($objectives && $objectives->ramiPrefAnnL2IncassiCurPersonal > 0) {
            $yearlyObjective = $objectives->ramiPrefAnnL2IncassiCurPersonal;
        } elseif ($objectives) {
            $yearlyObjective = $objectives->ramiPrefAnnL2IncassiCur;
        }

        $result = [];
        $monthlyObjective = $objectives ? $yearlyObjective / 12 : null;

        foreach ($monthlyData as $data) {
            if ($objectives) {
                $result[] = (object) [
                    'year' => $data->year,
                    'month' => $data->month,
                    'agenzia_id' => trim($data->agenzia_id),
                    'level' => 2,
                    'type' => 'RP',
                    // Headings.
                    'consumptivePrevious' => $objectives->ramiPrefAnnL1IncassiPrev,
                    'objectiveYear' => $yearlyObjective,
                    'objectiveYearDelta' => $yearlyObjective - $objectives->ramiPrefAnnL1IncassiPrev,
                    //'objectiveYearIncrease_' => ($yearlyObjective - $objectives->ramiPrefAnnL1IncassiPrev) / $objectives->ramiPrefAnnL1IncassiPrev * 100,
                    'objectiveYearIncrease' => $this->ratio($yearlyObjective - $objectives->ramiPrefAnnL1IncassiPrev, $objectives->ramiPrefAnnL1IncassiPrev, 100),
                    'objectiveMonth' => $monthlyObjective,
                    'objectiveMonthCumulative' => $monthlyObjective * $data->month,
                    // Month data.
                    'consumptiveMonth' => $data->ramiPrefIncassi,
                    'consumptiveMonthDeltaYear' => $data->ramiPrefIncassi - $yearlyObjective,
                    //'consumptiveMonthIncreaseYear_' => $data->ramiPrefIncassi / $yearlyObjective * 100,
                    'consumptiveMonthIncreaseYear' => $this->ratio($data->ramiPrefIncassi, $yearlyObjective, 100),
                    'consumptiveMonthDeltaMonth' => $data->ramiPrefIncassi - $monthlyObjective * $data->month,
                    //'consumptiveMonthIncreaseMonth_' => $data->ramiPrefIncassi / ($monthlyObjective * $data->month) * 100,
                    'consumptiveMonthIncreaseMonth' => $this->ratio($data->ramiPrefIncassi, ($monthlyObjective * $data->month), 100),
                    'rappel' => $data->ramiPrefL2Rappel,
                ];

                continue;
            }

            $result[] = (object) [
                'year' => $data->year,
                'month' => $data->month,
                'agenzia_id' => trim($data->agenzia_id),
                'level' => 2,
                'type' => 'RP',
                // Month data.
                'consumptiveMonth' => $data->ramiPrefIncassi,
                'rappel' => $data->ramiPrefL2Rappel,
            ];
        }

        return $result;
    }

    protected function processV1($monthlyData, $objectives = null) {
        $result = [];
        $monthlyObjective = $objectives ? $objectives->vitaAnnL1IncassiCur / 12 : null;

        foreach ($monthlyData as $data) {
            if ($objectives) {
                $result[] = (object) [
                    'year' => $objectives->year,
                    'month' => $data->month,
                    'agenzia_id' => $data->agenzia_id,
                    'level' => 1,
                    'type' => 'V',
                    // Headings.
                    'consumptivePrevious' => $objectives->vitaBaseCalcoloObj,
                    'objectiveYear' => $objectives->vitaAnnL1IncassiCur,

                    // Cant perform increase vs BaseCalcolo:
                    // following cols are disabled with "-".
                    'objectiveYearDelta' => "-",
                    'objectiveYearIncrease' => "-",

                    'objectiveMonth' => $monthlyObjective,
                    'objectiveMonthCumulative' => $monthlyObjective * $data->month,
                    // Month data.
                    'consumptiveMonth' => $data->vitaIncassi,
                    'consumptiveMonthDeltaYear' => $data->vitaIncassi - $objectives->vitaAnnL1IncassiCur,
                    //'consumptiveMonthIncreaseYear_' => $data->vitaIncassi / $objectives->vitaAnnL1IncassiCur * 100,
                    'consumptiveMonthIncreaseYear' => $this->ratio($data->vitaIncassi, $objectives->vitaAnnL1IncassiCur, 100),
                    'consumptiveMonthDeltaMonth' => $data->vitaIncassi - $monthlyObjective * $data->month,
                    //'consumptiveMonthIncreaseMonth_' => $data->vitaIncassi / ($monthlyObjective * $data->month) * 100,
                    'consumptiveMonthIncreaseMonth' => $this->ratio($data->vitaIncassi, ($monthlyObjective * $data->month), 100),
                    'rappel' => $data->vitaL1Rappel,
                ];

                continue;
            }

            $result[] = (object) [
                'year' => $objectives->year,
                'month' => $data->month,
                'agenzia_id' => $data->agenzia_id,
                'level' => 1,
                'type' => 'V',
                'objectiveYearDelta' => "-",
                'objectiveYearIncrease' => "-",
                'consumptiveMonth' => $data->vitaIncassi,
                'rappel' => $data->vitaL1Rappel,
            ];
        }

        return $result;
    }

    protected function processV2($monthlyData, $objectives) {
        if ($objectives->vitaAnnL2IncassiCur > 0) {
            $yearlyObjective = $objectives->vitaAnnL2IncassiCur;
        } else {
            $yearlyObjective = $objectives->vitaAnnL1IncassiCur;
        }

        $monthlyObjective = $yearlyObjective / 12;

        $result = [];
        foreach ($monthlyData as $data) {
            $result[] = (object) [
                'year' => $data->year,
                'month' => $data->month,
                'agenzia_id' => $data->agenzia_id,
                'level' => 2,
                'type' => 'V',
                // Headings.
                'consumptivePrevious' => $objectives->vitaBaseCalcoloObj,
                'objectiveYear' => $yearlyObjective,

                // @See V1.
                'objectiveYearDelta' => "-",
                'objectiveYearIncrease_' => "-",

                'objectiveMonth' => $monthlyObjective,
                'objectiveMonthCumulative' => $monthlyObjective * $data->month,
                // Month data.
                'consumptiveMonth' => $data->vitaIncassi,
                'consumptiveMonthDeltaYear' => $data->vitaIncassi - $yearlyObjective,
                //'consumptiveMonthIncreaseYear_' => $data->vitaIncassi / $yearlyObjective * 100,
                'consumptiveMonthIncreaseYear' => $this->ratio($data->vitaIncassi, $yearlyObjective, 100),
                'consumptiveMonthDeltaMonth' => $data->vitaIncassi - $monthlyObjective * $data->month,
                //'consumptiveMonthIncreaseMonth_' => $data->vitaIncassi / ($monthlyObjective * $data->month) * 100,
                'consumptiveMonthIncreaseMonth' => $this->ratio($data->vitaIncassi, ($monthlyObjective * $data->month), 100),
                'rappel' => $data->vitaL2Rappel,
            ];
        }

        return $result;
    }

    protected function loadObjectives($agenziaId, $year) {
        // @TODO refactor to repositories.
        $pdo = Kernel::pdo();

        $query = "SELECT * FROM `pianiagz_obj_accordo` a inner join pianiagz_obj_accordo_rev r on a.id = r.accordo_id where a.year = :year and a.agenzia_id = :agenzia_id and r.isLatest = 1";
        $statement = $pdo->prepare($query);
        $statement->execute(['year' => $year, 'agenzia_id' => $agenziaId]);

        return $statement->fetchObject();
    }

    protected function ratio($a, $b, $multiply = 1) {
        if ($b == 0) {
            return 0;
        }

        return $a / $b * $multiply;
    }

    public function getAvanzamentiPoliciesByAgenzia($agenziaId, $year, $area)
    {
        try {
            $criteria = "agenzia_id,EQ,$agenziaId|year,EQ,$year|areaIncentivazione,EQ,$area";

            return $this->avanzamentiPolicies->fetchAll(null, null, "year desc", $criteria);

        }

        catch (\Exception $ex) {
            // @TODO Log error
            throw $ex;
        }
    }
}
