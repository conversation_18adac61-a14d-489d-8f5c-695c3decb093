<?php namespace api\apps\avanzamenti;

//use api\utils\GrowlResponse;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use metadigit\core\web\controller\ActionController;

class Controller extends ActionController {

    /**
     * @var Manager
     */
    protected $manager;

    /**
     * @routing(method="GET", pattern="/avanzamento/<agenziaId>/<year>")
     *
     * @param Request $request
     * @param Response $response
     * @param string $agenziaId
     * @param string $year
     * @throws \Exception
     */
    public function getAgenziaAction(Request $request, Response $response, $agenziaId, $year)
    {
        $data = $this->manager->getByAgenzia($agenziaId, $year);
        return $response->set("data", $data)->setView("json:");
    }

    /**
     * @routing(method="GET", pattern="/excel/<agenziaId>/<year>/<area>")
     *
     * @param Request $request
     * @param Response $response
     * @param string $agenziaId
     * @param string $year
     * @param string $area
     * @throws \Exception
     */
    public function getPoliciesExcelAction(Request $request, Response $response, $agenziaId, $year, $area)
    {
        $data = $this->manager->getAvanzamentiPoliciesByAgenzia($agenziaId, $year, $area);
        return $response
            ->set('data', $data)
            ->set('saveName', strtotime('now').'_avanzamenti_policies')
            ->setView('file-excel:/policies');
    }

    /**
     * @routing(method="GET", pattern="/pdf")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function pdfAction(Request $Req, Response $Res)
    {
        $Res->setView('xsendfile:/apps/accordo2/accordo-economico-2024.pdf');
    }

}
