<?php
header("Content-Type: application/octet-stream");
header("Content-Transfer-Encoding: Binary");
header("Content-disposition: attachment; filename=\"{$filename}\"");

function _v($value) {
	return number_format($value, 2, ',', '');
}

function _courseStatusFormat($value) {
    $string = '';
    switch ($value) {
        case '1':
            $string = 'ON';
            break;
        case '0':
            $string = 'OFF';
            break;
    }
    return $string;
}
function _formatDate($date) {
    $dateTime = date_create("$date");
    $formattedDate = date_format($dateTime,"d/m/Y");
    return $formattedDate;
}
function _classroomPercentage($seats, $occupied, $extra) {
    if (!$seats) return 0;
    $perc = ( ($occupied + $extra) / $seats) * 100;
    return round($perc);
}
?>
<table>
    <thead>
    <tr>
        <th style="border-bottom: 1px solid #000000">Nome evento</th>
        <th style="border-bottom: 1px solid #000000">Status</th>
        <th style="border-bottom: 1px solid #000000">Anno</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td valign="middle"><?php echo $event->title ?></td>
        <td valign="middle"><?php echo _courseStatusFormat($event->status) ?></td>
        <td valign="middle"><?php echo $event->year ?></td>
    </tr>
    <tr></tr>
    </tbody>
</table>
<table>
    <thead>
    <tr>
        <th style="border-bottom: 1px solid #000000">Data</th>
        <th style="border-bottom: 1px solid #000000">Citta</th>
        <th style="border-bottom: 1px solid #000000">Struttura</th>
        <?php if (in_array($_SESSION['AUTH']['UTYPE'], [ 'KA', 'AMMINISTRATORE', 'FORMAZIONE' ])) { ?>
            <th style="border-bottom: 1px solid #000000">Posti</th>
        <?php } ?>
        <th style="border-bottom: 1px solid #000000">Iscr. da portale</th>
        <?php if (in_array($_SESSION['AUTH']['UTYPE'], [ 'KA', 'AMMINISTRATORE', 'FORMAZIONE' ])) { ?>
            <th style="border-bottom: 1px solid #000000">Iscr. direzionale</th>
            <th style="border-bottom: 1px solid #000000">Iscr. totali</th>
            <th style="border-bottom: 1px solid #000000">% occupata</th>
        <?php } ?>
    </tr>
    </thead>
    <tbody>
    <?php foreach ($stages as $s):?>
    <tr>
        <td align="center"><?php echo _formatDate($s->day) ?></td>
        <td><?php echo $s->city ?></td>
        <td><?php echo $s->name ?></td>
        <?php if (in_array($_SESSION['AUTH']['UTYPE'], [ 'KA', 'AMMINISTRATORE', 'FORMAZIONE' ])) { ?>
            <td align="right"><?php echo $s->seats ? $s->seats : '-' ?></td>
        <?php } ?>
        <td align="right"><?php echo $s->enrolled ?></td>
        <?php if (in_array($_SESSION['AUTH']['UTYPE'], [ 'KA', 'AMMINISTRATORE', 'FORMAZIONE' ])) { ?>
            <td align="right"><?php echo $s->extra ?></td>
            <td align="right"><?php echo $s->enrolledTotal ?></td>
            <td align="right"><?php echo $s->seats ? _classroomPercentage($s->seats, $s->enrolled, $s->extra).'%' : '-' ?></td>
        <?php } ?>
    </tr>
    <?php endforeach;?>
    </tbody>
</table>
