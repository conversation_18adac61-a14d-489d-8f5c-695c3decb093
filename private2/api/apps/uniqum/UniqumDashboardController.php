<?php

namespace api\apps\uniqum;

use api\utils\MakePaginationData;
use data\apps\formazione\Managers\DashboardManager;
use data\apps\formazione\Repositories\CourseRepository;
use metadigit\core\db\PdoTrait;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class UniqumDashboardController extends \metadigit\core\web\controller\ActionController
{
    use MakePaginationData, PdoTrait;

    /**
     * @var CourseRepository
     */
    protected $courses;

    /**
     * @var DashboardManager
     */
    protected $manager;

    /**
     * @routing(method="GET", pattern="/active-courses")
     * @param Request $Req
     * @param Response $Res
     */
    public function activeCoursesAction(Request $Req, Response $Res)
    {
        $success = false;

        try {
            $userId         = $_SESSION['AUTH']['UID'];
            $agencyCode     = $_SESSION['AUTH']['AGENZIA'];
            $activeCourses  = $this->manager->uniqumActiveCourses($userId, $agencyCode);
            $success        = true;
            $Res->set('data', (array) $activeCourses);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error('ACTIVE COURSES EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

}
