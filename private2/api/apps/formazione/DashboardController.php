<?php

namespace api\apps\formazione;

use api\utils\MakePaginationData;
use data\apps\formazione\Managers\DashboardManager;
use data\apps\formazione\Utils\Json;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use service\StatsService;

class DashboardController extends BaseController
{
    use MakePaginationData, PdoTrait;

    /**
     * @var DashboardManager
     */
    protected $manager;

    /**
     * @var StatsService
     */
    protected $statsService;

    /**
     * @routing(method="GET", pattern="/charts/<year>")
     * @param Request $Req
     * @param Response $Res
     * @param int $year
     */
    public function chartsAction(Request $Req, Response $Res, $year)
    {
        $success = false;
        $this->statsService->add('formazione', 'access');

        try {
            $userId     = $_SESSION['AUTH']['UID'];
            $agencyCode = $_SESSION['AUTH']['AGENZIA'];
            $dashboard  = $this->manager->getDashboardCharts($userId, $agencyCode, $year);
            $success    = true;
            $Res->set('data', (array) $dashboard);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/courses/completed")
     * @param Request $Req
     * @param Response $Res
     */
    public function completedCoursesAction(Request $Req, Response $Res)
    {
        $success = false;

        try {
            $userId     = $_SESSION['AUTH']['UID'];
            $agencyCode = $_SESSION['AUTH']['AGENZIA'];
            $dashboard  = $this->manager->getDashboardCompletedCourses($userId, $agencyCode);
            $success    = true;
            $Res->set('data', (array) $dashboard);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/courses/active")
     * @param Request $Req
     * @param Response $Res
     */
    public function activeCoursesAction(Request $Req, Response $Res)
    {
        $success = false;

        try {
            $userId     = $_SESSION['AUTH']['UID'];
            $agencyCode = $_SESSION['AUTH']['AGENZIA'];
            $dashboard  = $this->manager->getDashboardActiveCourses($userId, $agencyCode);
            $success    = true;
            $Res->set('data', (array) $dashboard);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/status")
     * @param Request $Req
     * @param Response $Res
     */
    public function statusAction(Request $Req, Response $Res)
    {
        $success = false;

        /*echo '<pre>';
        print_r($status);
        echo '</pre>';
        return;*/

        try {
            $userId     = $_SESSION['AUTH']['UID'];
            $year       = date("Y");
            $status     = $this->manager->status($userId, $year);
            $success    = true;

            $Res->set('data', (array) $status);
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/tutorial")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function pdfAction(Request $Req, Response $Res)
    {
        $Res->setView('xsendfile:/apps/formazione/files/tutorial.pdf');
    }

    /**
     * @routing(method="GET", pattern="/credits-summary")
     * @param Request $Req
     * @param Response $Res
     */
    public function creditsSummaryAction(Request $Req, Response $Res)
    {
        $success = false;
        try {
            $credits = $this->getCreditsSummary($_SESSION['AUTH']['UID']);
            $success = true;
            $Res->set('data', ["credits" => (float)$credits]);
        } catch(\Exception $ex) {
            return $Res->set("success", $success)
                ->set("error", $ex->getMessage())
                ->setView("json:");
        }

        $Res->set('success', $success)->setView('json:');
    }

    protected function getCreditsSummary($userId)
    {
        return $this->pdoQuery("SELECT `somma crediti` FROM vw_tra_user_credits_summary WHERE id = $userId")->fetch(Repository::FETCH_JSON)[0];
    }

}
