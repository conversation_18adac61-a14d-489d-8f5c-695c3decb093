<?php namespace api\apps\incentive\raccoltaVincente2025;

use api\apps\incentive\supernova2024\Exception;
use api\apps\incentive\supernova2024\json;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class BackendController extends \metadigit\core\web\controller\ActionController {

    use PdoTrait;

    /**
     * @var Repository
     */
    protected $status;

    /**
     * @var Repository
     */
    protected $data;

    /**
     * Return agency status.
     *
     * @routing(method="GET", pattern="/status-checker")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    public function statusCheckerAction(Request $Req, Response $Res)
    {
        if (!$this->checkAuthorization()) {
            return;
        }

        $agencyId = $this->getAndValidateAgencyId($Req);
        if (!$agencyId) {
            return;
        }

        $status = $this->status->fetchOne(null, null, "agenzia_id,EQ,$agencyId", \PDO::FETCH_ASSOC);

        // Fetch policies with all new fields
        $policies = $this->pdoQuery("
            SELECT * FROM inc_raccolta_2025_data
            WHERE agenzia_id = '" . $agencyId . "'
            ORDER BY dataContabile ASC
        ")->fetchAll(\PDO::FETCH_ASSOC);

        // Fetch FPA policies
        $policiesFPA = $this->pdoQuery("
            SELECT * FROM inc_raccolta_2025_data_fpa
            WHERE agenzia_id = '" . $agencyId . "'
        ")->fetchAll(\PDO::FETCH_ASSOC);

        // Process policies and calculate incentives
        $calculationData = $this->calculateIncentives($policies, $policiesFPA);

        // Generate and output HTML
        $this->outputHtmlReport($agencyId, $calculationData);
    }

    /**
     * Check if user is authorized to access this action
     * @return bool
     */
    protected function checkAuthorization()
    {
        $userType = $_SESSION['AUTH']['UTYPE'];
        if ($userType !== 'KA') {
            http_response_code(401);
            echo 'Unauthorized';
            return false;
        }
        return true;
    }

    /**
     * Get and validate agency ID from request
     * @param Request $Req
     * @return string|null
     */
    protected function getAndValidateAgencyId(Request $Req)
    {
        $agencyId = $Req->get('agencyId');
        if (!$agencyId) {
            http_response_code(400);
            echo 'Missing agencyId parameter';
            return null;
        }
        return $agencyId;
    }

    /**
     * Check if a client is new based on business rules
     * @param array $policy
     * @return bool
     */
    protected function isNewClient($policy)
    {
        $newClientCheck = $this->pdoQuery("
                SELECT * FROM vw_inc_raccolta_2025_clients
                WHERE CF_PIVA = '" . $policy["CF_PIVA"] . "' AND flag_risparmio_202306 = 0"
        )->fetchAll(\PDO::FETCH_ASSOC);
        return !empty($newClientCheck);
    }

    /**
     * Check if the given policy qualifies as OTP by querying the internal OTP data table
     * @param array $policy
     * @return bool
     */
    protected function isOtp($policy)
    {
        $otpCheck = $this->pdoQuery("
                SELECT * FROM inc_raccolta_2025_data_otp
                WHERE polizza = '" . $policy["polizza"] . "'
                AND codiceProdotto = '" . $policy["codiceProdotto"] . "'"
        )->fetchAll(\PDO::FETCH_ASSOC);
        return !empty($otpCheck);
    }

    /**
     * Calculate incentives for all policies
     * @param array $policies
     * @param array $policiesFPA
     * @return array
     */
    protected function calculateIncentives($policies, $policiesFPA = [])
    {
        // Initialize calculation data
        $data = [
            'premiPPT' => 0,
            'premiClients' => 0,
            'nuoviClienti' => 0,
            'incentPPT' => 0,
            'incentClients' => 0,
            'pezziOpen' => 0,
            'incentOpen' => 0,
            'otp' => 0,
            'premiOtp' => 0,
            'incentOtp' => 0,
            'newClientsCount' => 0,
            'policiesDetails' => [],
            'rate' => 0.012 // Default rate
        ];

        // First pass - identify new clients and count them (only count unique clients)
        $uniqueNewClients = [];
        foreach ($policies as $policy) {
            $newClient = false;

            if ($policy['tipoPremioQuietanza'] === 'Perfezionamento') {
                $newClient = $this->isNewClient($policy);
                if ($newClient && !in_array($policy['CF_PIVA'], $uniqueNewClients)) {
                    $uniqueNewClients[] = $policy['CF_PIVA'];
                }
            }

            // Store the newClient status for later use
            $policy['isNewClient'] = $newClient;
            $data['policiesDetails'][] = $policy;
        }

        $data['newClientsCount'] = count($uniqueNewClients);

        // Determine rate based on new client count
        $data['rate'] = ($data['newClientsCount'] >= 5) ? 0.015 : 0.012;

        // Second pass - calculate incentives
        foreach ($data['policiesDetails'] as $key => $policy) {
            $this->calculatePolicyIncentive($data, $key, $policy);
        }

        // Process FPA policies
        foreach ($policiesFPA as $policyFPA) {
            $data['pezziOpen'] += $policyFPA['pezzi'];
        }
        $data['incentOpen'] = 25.82 * $data['pezziOpen'];

        // Calculate total incentive
        $data['totalIncentive'] = $data['incentPPT'] + $data['incentClients'] + $data['incentOpen'] + $data['incentOtp'];

        return $data;
    }

    /**
     * Calculate incentive for a single policy
     * @param array &$data
     * @param int $key
     * @param array $policy
     */
    protected function calculatePolicyIncentive(&$data, $key, $policy)
    {
        $newClient = $policy['isNewClient'];
        $originalPremio = $policy["premio"];

        // Check if policy is excluded
        if ($policy['esclusa'] == 1) {
            $data['policiesDetails'][$key]['criterio'] = 'Esclusa';
            $data['policiesDetails'][$key]['percentuale'] = '0%';
            $data['policiesDetails'][$key]['incentivo'] = 0;
            $data['policiesDetails'][$key]['isOtp'] = false;
            return;
        }

        // Handle Investment and PPT products
        if (in_array($policy["codiceProdotto"], RaccoltaVincente2025::INVESTMENT_AND_PPT)) {
            $premio = $originalPremio;

            if (!$newClient) {
                // Check for rate reduction (aliqRidotta = 1 or parziale = '04')
                $isReducedRate = ($policy['aliqRidotta'] == 1 || $policy['parziale'] === '04');
                $rate = $isReducedRate ? 0.004 : 0.008;

                $data['premiPPT'] += $premio;
                $data['incentPPT'] += $premio * $rate;

                $criterio = 'PPT/Investment - Cliente Esistente';
                if ($isReducedRate) {
                    $criterio .= ' (Aliquota Ridotta)';
                }

                $data['policiesDetails'][$key]['criterio'] = $criterio;
                $data['policiesDetails'][$key]['percentuale'] = $isReducedRate ? '0,4%' : '0,8%';
                $data['policiesDetails'][$key]['incentivo'] = $premio * $rate;
            } else {
                $data['nuoviClienti']++;
                $data['premiClients'] += $premio;
                $data['incentClients'] += $premio * $data['rate'];

                $data['policiesDetails'][$key]['criterio'] = 'PPT/Investment - Nuovo Cliente';
                $data['policiesDetails'][$key]['percentuale'] = ($data['newClientsCount'] >= 5) ? '1,5%' : '1,2%';
                $data['policiesDetails'][$key]['incentivo'] = $premio * $data['rate'];
            }

            // Check for OTP bonus
            $isOtp = $this->isOtp($policy);
            if ($isOtp) {
                $data['otp']++;
                $data['premiOtp'] += $premio;
                $data['incentOtp'] += 15;
                $data['policiesDetails'][$key]['criterio'] .= ' + OTP';
                $data['policiesDetails'][$key]['incentivo'] += 15;
            }
            $data['policiesDetails'][$key]['isOtp'] = $isOtp;

        }
        // Handle non-incentivized products
        else {
            $data['policiesDetails'][$key]['criterio'] = 'Non incentivabile';
            $data['policiesDetails'][$key]['percentuale'] = '0%';
            $data['policiesDetails'][$key]['incentivo'] = 0;
            $data['policiesDetails'][$key]['isOtp'] = false;
        }
    }

    /**
     * Generate and output HTML report
     * @param string $agencyId
     * @param array $data
     */
    protected function outputHtmlReport($agencyId, $data)
    {
        echo '<html><head><style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            table { border-collapse: collapse; width: 100%; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            td.number { text-align: right; }
            .calculation-box { 
                background-color: #f8f9fa; 
                border: 1px solid #ddd; 
                padding: 15px; 
                margin: 20px 0;
                border-radius: 4px;
            }
            .calculation-item {
                margin: 10px 0;
                padding: 5px 0;
                border-bottom: 1px solid #eee;
            }
            .total { 
                font-weight: bold; 
                margin-top: 15px; 
                padding-top: 15px;
                border-top: 2px solid #ddd;
            }
            .filters {
                margin: 20px 0;
                padding: 10px;
                background: #f5f5f5;
                border-radius: 4px;
            }
            .filters button {
                margin: 0 5px;
                padding: 5px 10px;
                border: 1px solid #ddd;
                border-radius: 3px;
                cursor: pointer;
            }
            #searchInput {
                padding: 5px;
                margin: 0 5px;
                width: 200px;
            }
            .new-client { background-color: #fff3e0 !important; }
            .excluded { background-color: #ffebee !important; }
            .premium-reduced { background-color: #fce4ec !important; }
            .reduced-rate { background-color: #e8f5e8 !important; }
            .otp { background-color: #e3f2fd !important; }
        </style></head><body>';
        
        // Summary section
        echo '<h2>Riepilogo Calcoli per Agenzia ' . htmlspecialchars($agencyId) . '</h2>';
        echo '<div class="calculation-box">';
        echo '<div class="calculation-item">Percentuale applicata: ' . ($data['rate'] * 100) . '% (' . $data['newClientsCount'] . ' nuovi clienti)</div>';
        echo '<div class="calculation-item">Premi PPT: €' . number_format($data['premiPPT'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Incentivo PPT (0,8%/0,4%): €' . number_format($data['incentPPT'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Premi Nuovi Clienti: €' . number_format($data['premiClients'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Incentivo Nuovi Clienti (' . ($data['rate'] * 100) . '%): €' . number_format($data['incentClients'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Pezzi OPEN: ' . $data['pezziOpen'] . '</div>';
        echo '<div class="calculation-item">Incentivo OPEN: €' . number_format($data['incentOpen'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Pezzi OTP: ' . $data['otp'] . '</div>';
        echo '<div class="calculation-item">Premi OTP: €' . number_format($data['premiOtp'], 2, ',', '.') . '</div>';
        echo '<div class="calculation-item">Incentivo OTP: €' . number_format($data['incentOtp'], 2, ',', '.') . '</div>';
        echo '<div class="total">Incentivo Totale: €' . number_format($data['totalIncentive'], 2, ',', '.') . '</div>';
        echo '</div>';
        
        // Policies detail section
        echo '<h2>Dettaglio Polizze</h2>';
        
        // Add filters
        echo '<div class="filters">';
        echo '<button onclick="filterTable(\'all\')">Tutte le polizze</button>';
        echo '<button onclick="filterTable(\'new\')">Solo Nuovi Clienti</button>';
        echo '<button onclick="filterTable(\'existing\')">Solo Clienti Esistenti</button>';
        echo '<button onclick="filterTable(\'ppt\')">Solo PPT/Investment</button>';
        echo '<button onclick="filterTable(\'perfezionamento\')">Solo Perfezionamento</button>';
        echo '<button onclick="filterTable(\'versamenti\')">Solo Versamenti</button>';
        echo '<button onclick="filterTable(\'excluded\')">Solo Escluse</button>';
        echo '<button onclick="filterTable(\'premiumReduced\')">Solo Premio Ridotto</button>';
        echo '<button onclick="filterTable(\'reduced\')">Solo Aliquota Ridotta</button>';
        echo '<button onclick="filterTable(\'otp\')">Solo OTP</button>';
        echo '<input type="text" id="searchInput" placeholder="Cerca..." onkeyup="searchTable()">';
        echo '</div>';
        
        $this->outputPoliciesTable($data['policiesDetails']);
        $this->outputJavaScript();
        
        echo '</body></html>';
    }

    /**
     * Output the policies detail table
     * @param array $policiesDetails
     */
    protected function outputPoliciesTable($policiesDetails)
    {
        if (empty($policiesDetails)) {
            echo '<p>Nessuna polizza trovata per questa agenzia.</p>';
            return;
        }
        
        echo '<table id="policiesTable">';
        echo '<tr>
            <th>N. Polizza</th>
            <th>Codice Prodotto</th>
            <th>CF/P.IVA</th>
            <th>Tipo Premio/Quietanza</th>
            <th>Data Contabile</th>
            <th>Premio</th>
            <th>Tipo Cliente</th>
            <th>Esclusa</th>
            <th>Premio Ridotto</th>
            <th>Aliq. Ridotta</th>
            <th>Parziale</th>
            <th>OTP</th>
            <th>Criterio</th>
            <th>% Incentivo</th>
            <th>Incentivo</th>
        </tr>';

        foreach ($policiesDetails as $policy) {
            $rowClasses = [];
            if ($policy['isNewClient']) $rowClasses[] = 'new-client';
            if ($policy['esclusa'] == 1) $rowClasses[] = 'excluded';
            if ($policy['premioRidotto'] == 1) $rowClasses[] = 'premium-reduced';
            if ($policy['aliqRidotta'] == 1) $rowClasses[] = 'reduced-rate';
            if (isset($policy['isOtp']) && $policy['isOtp']) $rowClasses[] = 'otp';

            echo '<tr class="' . implode(' ', $rowClasses) . '">';
            echo '<td>' . htmlspecialchars($policy['polizza']) . '</td>';
            echo '<td>' . htmlspecialchars($policy['codiceProdotto']) . '</td>';
            echo '<td>' . htmlspecialchars($policy['CF_PIVA']) . '</td>';
            echo '<td>' . htmlspecialchars($policy['tipoPremioQuietanza']) . '</td>';
            echo '<td>' . htmlspecialchars($policy['dataContabile']) . '</td>';
            echo '<td class="number">' . number_format($policy['premio'], 2, ',', '.') . ' €</td>';
            echo '<td>' . ($policy['isNewClient'] ? 'Nuovo' : 'Esistente') . '</td>';
            echo '<td>' . ($policy['esclusa'] == 1 ? 'Sì' : 'No') . '</td>';
            echo '<td>' . ($policy['premioRidotto'] == 1 ? 'Sì' : 'No') . '</td>';
            echo '<td>' . ($policy['aliqRidotta'] == 1 ? 'Sì' : 'No') . '</td>';
            echo '<td>' . ($policy['parziale'] ? $policy['parziale'] : '-') . '</td>';
            echo '<td>' . (isset($policy['isOtp']) && $policy['isOtp'] ? 'Sì' : 'No') . '</td>';
            echo '<td>' . htmlspecialchars($policy['criterio']) . '</td>';
            echo '<td>' . htmlspecialchars($policy['percentuale']) . '</td>';
            echo '<td class="number">' . number_format($policy['incentivo'], 2, ',', '.') . ' €</td>';
            echo '</tr>';
        }
        echo '</table>';
    }

    /**
     * Output JavaScript for table filtering and search
     */
    protected function outputJavaScript()
    {
        echo '<script>
        function filterTable(filter) {
            const rows = document.querySelectorAll("#policiesTable tr:not(:first-child)");
            rows.forEach(row => {
                const productCode = row.cells[1].textContent;
                const tipoPremioQuietanza = row.cells[3].textContent;
                const clientType = row.cells[6].textContent;
                const esclusa = row.cells[7].textContent;
                const premioRidotto = row.cells[8].textContent;
                const aliqRidotta = row.cells[9].textContent;
                const otp = row.cells[11].textContent;
                const criterio = row.cells[12].textContent;

                switch(filter) {
                    case "all":
                        row.style.display = "";
                        break;
                    case "new":
                        row.style.display = clientType === "Nuovo" ? "" : "none";
                        break;
                    case "existing":
                        row.style.display = clientType === "Esistente" ? "" : "none";
                        break;
                    case "ppt":
                        row.style.display = criterio.includes("PPT/Investment") ? "" : "none";
                        break;
                    case "perfezionamento":
                        row.style.display = tipoPremioQuietanza === "Perfezionamento" ? "" : "none";
                        break;
                    case "versamenti":
                        row.style.display = tipoPremioQuietanza.includes("Versamenti") ? "" : "none";
                        break;
                    case "excluded":
                        row.style.display = esclusa === "Sì" ? "" : "none";
                        break;
                    case "premiumReduced":
                        row.style.display = premioRidotto === "Sì" ? "" : "none";
                        break;
                    case "reduced":
                        row.style.display = aliqRidotta === "Sì" ? "" : "none";
                        break;
                    case "otp":
                        row.style.display = otp === "Sì" ? "" : "none";
                        break;
                }
            });
        }

        function searchTable() {
            const input = document.getElementById("searchInput");
            const filter = input.value.toUpperCase();
            const rows = document.querySelectorAll("#policiesTable tr:not(:first-child)");
            
            rows.forEach(row => {
                let found = false;
                for (let i = 0; i < row.cells.length; i++) {
                    const cell = row.cells[i];
                    if (cell.textContent.toUpperCase().indexOf(filter) > -1) {
                        found = true;
                        break;
                    }
                }
                row.style.display = found ? "" : "none";
            });
        }
        </script>';
    }

}
