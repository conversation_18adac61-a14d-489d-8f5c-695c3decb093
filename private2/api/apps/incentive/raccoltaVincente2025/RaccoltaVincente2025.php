<?php

namespace api\apps\incentive\raccoltaVincente2025;

use api\apps\incentive\AbstractIncentive;
use api\apps\incentive\focus2024\obj;
use api\apps\incentive\IncentiveLifeInterface;
use metadigit\core\CoreTrait;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchTrait;

class RaccoltaVincente2025 implements IncentiveLifeInterface
{
    use CoreTrait, PdoTrait, BatchTrait;

    /**
     * @var Repository
     */
    protected $data;

    /**
     * @var Repository
     */
    protected $static;

    /**
     * @var Repository
     */
    protected $status;

    /**
     * @var Repository
     */
    protected $clients;

    const INVESTMENT_AND_PPT = [
        "MU18", "MU19", "MU20", "MU21", "MU22", "MU23", "UL10", "PU08", "VC15",
        "MU30",
        "PI03"
    ];

    public function getIds()
    {
        return [331];
    }

    public function parseDataRow(array $row, $incentiveId)
    {
        // Trim columns.
        $row = array_map(function ($item) {
            return trim($item);
        }, $row);
        $agenzia_id = $this->convertAgency($row[6], $row[8]);

        $trasformazionePartenza = $this->parseTranformation($row[29]);
        $trasformazioneArrivo = $this->parseTranformation($row[25]);

        return [
            "iniziativa_id" => $row[1],
            "agenzia_id" => $agenzia_id,
            "polizza" => $row[12],
            "data" => $row[2],
            "danni_vita" => $row[7],
            "prodotto" => $row[17],
            "data_effetto" => $row[5],
            "codice_cliente" => $row[15],
            "nominativo" => $row[14],
            "trasformazione_partenza" => $trasformazionePartenza,
            "trasformazione_arrivo" => $trasformazioneArrivo,
            "premio" => $row[24],
        ];
    }

    public function getDataInitStatement()
    {
        return "DELETE FROM inc_focus2024_data";
    }

    public function getDataStatement()
    {
        return "INSERT INTO inc_focus2024_data (
            iniziativa_id,
            agenzia_id,
            polizza,
            data,
            data_effetto,
            danni_vita,
            prodotto,
            codice_cliente,
            nominativo,
            trasformazione_partenza,
            trasformazione_arrivo,
            premio
        ) values (
            :iniziativa_id,
            :agenzia_id,
            :polizza,
            :data,
            :data_effetto,
            :danni_vita,
            :prodotto,
            :codice_cliente,
            :nominativo,
            :trasformazione_partenza,
            :trasformazione_arrivo,
            :premio
        )";
    }

    public function getStaticPath()
    {
        return \metadigit\core\UPLOAD_DIR . "RACCOLTAVINCENTE_321_STATICO_*.csv";
    }

    public function parseStaticRow(array $row, $incentiveId)
    {
        return [
            "agenzia_id" => $row[1],
            "active" => $row[2],
        ];
    }

    public function getStaticInitStatement()
    {
        return "DELETE FROM inc_raccolta_2025_static";
    }

    public function getStaticStatement()
    {
        return "INSERT INTO inc_raccolta_2025_static (
            agenzia_id,
            active
        ) values (
            :agenzia_id,
            :active
        )";
    }

    public function parseClientsRow(array $row)
    {
        $clientArray = [
            "iniziativa_id" => $row[0],
            "agenzia_id" => $row[1],
            "codiceCliente" => $row[9],
            "nominativo" => $row[2],
            "partenza" => $row[4],
            "arrivo" => $row[5],
            "acquisito" => $row[7],
            "conRelazioni" => $row[8],
            "boostVita" => $row[10],
            "boostCasa" => $row[11],
            "boostInfortuni" => $row[12]
        ];

        return $clientArray;
    }

    /**
     * Post data insert event handler.
     *
     * @param obj $parsed[id, date]
     *
     * @return mixed
     */
    public function postInsert($parsed)
    {
        $this->status->deleteAll(null);

        $static = $this->pdoQuery("SELECT * FROM inc_raccolta_2025_static")->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($static as $row) {
            $agenzia =  $this->pdoQuery("
            SELECT agenzie.id
            FROM agenzie
            WHERE id = '" . $row["agenzia_id"] . "'
            ")->fetch();
            if (!$agenzia) {
                //$this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "AGENCY NOT FOUND : " . $row["agenzia_id"]);
                $this->log("Agenzia non trovata: " . $row["agenzia_id"]);
                continue;
            }

            $this->status->insert(null, $this->calculateStatus('331', $row["agenzia_id"], $row));
        }
    }

    public function calculateStatus($initiativeId, $agencyId, $static)
    {

        $policies = $this->pdoQuery("SELECT * FROM inc_raccolta_2025_data WHERE agenzia_id = '" . $agencyId . "'")->fetchAll(\PDO::FETCH_ASSOC);
        $policiesFPA = $this->pdoQuery("SELECT * FROM inc_raccolta_2025_data_fpa WHERE agenzia_id = '" . $agencyId . "'")->fetchAll(\PDO::FETCH_ASSOC);
        $rate = $this->checkIncreasedRate($policies) ? 0.015 : 0.012;

        $status = [
            "agenzia_id" => $agencyId,
            "premiPPT" => 0,
            "premiClients" => 0,
            "pezziOpen" => 0,
            "nuoviClienti" => 0,
            "incentPPT" => 0,
            "incentClients" => 0,
            "incentOpen" => 0,
            "otp" => 0,
            "premiOtp" => 0,
            "incentOtp" => 0,
            "incentTotal" => 0,
        ];

        // Array to collect policy processing details for final trace log
        $policyTraceData = [];

        // Prima faccio le modifiche sul dato (esclusioni e riduzioni)
        foreach ($policies as $policy) {
            $originalPremium = $policy['premio'];
            $policyId = $policy["polizza"] . '|' . $policy["codiceProdotto"] . '|' . $policy['tipoPremioQuietanza'];

            // Initialize policy trace data
            $policyTrace = [
                'polizza' => $policy["polizza"],
                'codiceProdotto' => $policy["codiceProdotto"],
                'tipoPremioQuietanza' => $policy['tipoPremioQuietanza'],
                'CF_PIVA' => isset($policy['CF_PIVA']) ? $policy['CF_PIVA'] : 'N/A',
                'parziale' => isset($policy['parziale']) ? $policy['parziale'] : 'N/A',
                'originalPremium' => $originalPremium,
                'finalPremium' => $originalPremium,
                'reductions' => [],
                'clientType' => 'N/A',
                'incentives' => [],
                'totalIncentive' => 0,
                'excluded' => false,
                'eligible' => false
            ];

            if ( $this->checkExclusion($policy) ) {
                $policyTrace['excluded'] = true;
                $policyTrace['reductions'][] = 'EXCLUDED - Premium set to €0';
                $policyTrace['finalPremium'] = 0;
                $this->applyExclusion($policy);
                $policyTraceData[] = $policyTrace;
                continue;
            }

            if (in_array($policy["codiceProdotto"], self::INVESTMENT_AND_PPT)) {
                $policyTrace['eligible'] = true;
                $newClient = false;

                // Prima di controllare se il cliente è nuovo, controlliamo che la polizza abbia almeno un "Perfezionamento"
                if ( $policy['tipoPremioQuietanza'] === 'Perfezionamento' ) {
                    $newClient = $this->isNewClient($policy);
                    $policyTrace['clientType'] = $newClient ? 'NEW' : 'EXISTING';
                } else {
                    $policyTrace['clientType'] = 'EXISTING (Non-Perfezionamento)';
                }

                if (!$newClient) {

                    $isReducedRate = $this->checkReducedRate($policy);
                    if ($isReducedRate) {
                        $policyTrace['reductions'][] = 'RATE REDUCTION - Original: €' . number_format($originalPremium, 2) . ' → €' . number_format($isReducedRate, 2);
                        $this->applyRateReduction($policy, $isReducedRate);
                    }

                    $isReducedPremium = $this->checkReducedPremium($policy);
                    if ($isReducedPremium) {
                        $policyTrace['reductions'][] = 'PREMIUM REDUCTION - Split premium: €' . number_format($isReducedPremium, 2);
                        $this->applyPremiumReduction($policy, $isReducedPremium);
                    }

                }

            }

            $policyTraceData[] = $policyTrace;
        }

        // Riprendo le polizze dopo aver modificato i premi, adesso posso calcolare lo status
        $policies = $this->pdoQuery("SELECT * FROM inc_raccolta_2025_data WHERE agenzia_id = '" . $agencyId . "'")->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($policies as $policy) {
            $policyId = $policy["polizza"] . '|' . $policy["codiceProdotto"] . '|' . $policy['tipoPremioQuietanza'];
            $currentPremium = $policy['premio'];

            // Find the corresponding trace data entry
            $traceIndex = -1;
            for ($i = 0; $i < count($policyTraceData); $i++) {
                if ($policyTraceData[$i]['polizza'] === $policy["polizza"] &&
                    $policyTraceData[$i]['codiceProdotto'] === $policy["codiceProdotto"] &&
                    $policyTraceData[$i]['tipoPremioQuietanza'] === $policy['tipoPremioQuietanza']) {
                    $traceIndex = $i;
                    break;
                }
            }

            if ($traceIndex >= 0) {
                $policyTraceData[$traceIndex]['finalPremium'] = $currentPremium;
            }

            if (in_array($policy["codiceProdotto"], self::INVESTMENT_AND_PPT)) {

                $newClient = false;
                $incentiveAmount = 0;

                // Prima di controllare se il cliente è nuovo, controlliamo che la polizza abbia almeno un "Perfezionamento"
                if ( $policy['tipoPremioQuietanza'] === 'Perfezionamento' ) {
                    $newClient = $this->isNewClient($policy);
                }

                if (!$newClient) {
                    // PPT Incentive calculation
                    $pptRate = ($policy['parziale'] === '04' || $policy['aliqRidotta'] ? 0.004 : 0.008);
                    $incentiveAmount = $currentPremium * $pptRate;

                    $status["premiPPT"] += $currentPremium;
                    $status["incentPPT"] += $incentiveAmount;

                    if ($traceIndex >= 0) {
                        $policyTraceData[$traceIndex]['incentives'][] = 'PPT: €' . number_format($currentPremium, 2) . ' × ' . ($pptRate * 100) . '% = €' . number_format($incentiveAmount, 2);
                        $policyTraceData[$traceIndex]['totalIncentive'] += $incentiveAmount;
                    }

                } else {
                    // New Client Incentive calculation
                    $incentiveAmount = $currentPremium * $rate;

                    $status["nuoviClienti"]++;
                    $status["premiClients"] += $currentPremium;
                    $status["incentClients"] += $incentiveAmount;

                    if ($traceIndex >= 0) {
                        $policyTraceData[$traceIndex]['incentives'][] = 'New Client: €' . number_format($currentPremium, 2) . ' × ' . ($rate * 100) . '% = €' . number_format($incentiveAmount, 2);
                        $policyTraceData[$traceIndex]['totalIncentive'] += $incentiveAmount;
                    }
                }

                // Bonus OTP
                if ( $this->isOtp($policy) ) {
                    $status["otp"]++;
                    $status["premiOtp"] += $currentPremium;
                    $status["incentOtp"] += 15;

                    if ($traceIndex >= 0) {
                        $policyTraceData[$traceIndex]['incentives'][] = 'OTP Bonus: €15.00';
                        $policyTraceData[$traceIndex]['totalIncentive'] += 15;
                    }
                }

            }
        }

        // Print detailed trace for all G435 policies
        //TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__, 'G435 POLICIES - Total: ' . count($testerArray), print_r($testerArray, 1));

        // Polizze FPA
        $fpaTraceData = [];
        foreach ($policiesFPA as $policy) {
            $pieces = $policy["pezzi"];
            $premium = $policy["premio"];
            $incentiveAmount = $pieces * 25.82;

            $status["pezziOpen"] += $pieces;

            $fpaTraceData[] = [
                'numModuloAdesione' => $policy["numModuloAdesione"],
                'pieces' => $pieces,
                'premium' => $premium,
                'incentive' => $incentiveAmount
            ];
        }

        $status["incentOpen"] = 25.82 * $status["pezziOpen"];

        // Round values to avoid precision issues
        $status["premiPPT"] = round($status["premiPPT"], 2);
        $status["premiClients"] = round($status["premiClients"], 2);
        $status["incentPPT"] = round($status["incentPPT"], 2);
        $status["incentClients"] = round($status["incentClients"], 2);
        $status["incentOpen"] = round($status["incentOpen"], 2);
        $status["incentTotal"] = round($status["incentPPT"] + $status["incentClients"] + $status["incentOpen"] + $status["incentOtp"], 2);

        // ========== COMPREHENSIVE POLICY TRACE LOG ==========
        //$this->logComprehensivePolicyTrace($agencyId, $policyTraceData, $fpaTraceData, $status, $rate);

        return $status;
    }

    /**
     * Log comprehensive trace of all policies processed with complete data and incentives
     *
     * @param string $agencyId
     * @param array $policyTraceData
     * @param array $fpaTraceData
     * @param array $status
     * @param float $rate
     */
    protected function logComprehensivePolicyTrace($agencyId, $policyTraceData, $fpaTraceData, $status, $rate)
    {
        // Build comprehensive trace log
        $traceLog = "\n========== COMPREHENSIVE POLICY TRACE - Agency: " . $agencyId . " ==========\n";
        $traceLog .= "Rate Applied: " . ($rate * 100) . "% for new clients\n";
        $traceLog .= "Total Policies Processed: " . count($policyTraceData) . " | FPA Policies: " . count($fpaTraceData) . "\n\n";

        // Regular Policies
        $traceLog .= "--- REGULAR POLICIES ---\n";
        foreach ($policyTraceData as $policy) {
            $traceLog .= "Policy: " . $policy['polizza'] . " | Product: " . $policy['codiceProdotto'] . " | Type: " . $policy['tipoPremioQuietanza'] . "\n";
            $traceLog .= "  CF_PIVA: " . $policy['CF_PIVA'] . " | Parziale: " . $policy['parziale'] . " | Client: " . $policy['clientType'] . "\n";
            $traceLog .= "  Premium: €" . number_format($policy['originalPremium'], 2);

            if ($policy['originalPremium'] != $policy['finalPremium']) {
                $traceLog .= " → €" . number_format($policy['finalPremium'], 2);
            }
            $traceLog .= "\n";

            if ($policy['excluded']) {
                $traceLog .= "  STATUS: EXCLUDED\n";
            } elseif (!$policy['eligible']) {
                $traceLog .= "  STATUS: NOT ELIGIBLE (Product not in Investment/PPT list)\n";
            } else {
                $traceLog .= "  STATUS: ELIGIBLE\n";
            }

            if (!empty($policy['reductions'])) {
                $traceLog .= "  Reductions Applied:\n";
                foreach ($policy['reductions'] as $reduction) {
                    $traceLog .= "    - " . $reduction . "\n";
                }
            }

            if (!empty($policy['incentives'])) {
                $traceLog .= "  Incentives Applied:\n";
                foreach ($policy['incentives'] as $incentive) {
                    $traceLog .= "    - " . $incentive . "\n";
                }
                $traceLog .= "  TOTAL INCENTIVE: €" . number_format($policy['totalIncentive'], 2) . "\n";
            } else {
                $traceLog .= "  TOTAL INCENTIVE: €0.00\n";
            }
            $traceLog .= "\n";
        }

        // FPA Policies
        if (!empty($fpaTraceData)) {
            $traceLog .= "--- FPA POLICIES ---\n";
            foreach ($fpaTraceData as $fpa) {
                $traceLog .= "FPA Module: " . $fpa['numModuloAdesione'] . "\n";
                $traceLog .= "  Pieces: " . $fpa['pieces'] . " | Premium: €" . number_format($fpa['premium'], 2) . "\n";
                $traceLog .= "  Incentive: " . $fpa['pieces'] . " × €25.82 = €" . number_format($fpa['incentive'], 2) . "\n\n";
            }
        }

        // Final Summary
        $traceLog .= "--- FINAL SUMMARY ---\n";
        $traceLog .= "PPT Incentive: €" . number_format($status["incentPPT"], 2) . " (from €" . number_format($status["premiPPT"], 2) . " premium)\n";
        $traceLog .= "New Client Incentive: €" . number_format($status["incentClients"], 2) . " (from €" . number_format($status["premiClients"], 2) . " premium, " . $status["nuoviClienti"] . " clients)\n";
        $traceLog .= "FPA Incentive: €" . number_format($status["incentOpen"], 2) . " (from " . $status["pezziOpen"] . " pieces)\n";
        $traceLog .= "OTP Bonus: €" . number_format($status["incentOtp"], 2) . " (from " . $status["otp"] . " policies, €" . number_format($status["premiOtp"], 2) . " premium)\n";
        $traceLog .= "TOTAL INCENTIVE: €" . number_format($status["incentTotal"], 2) . "\n";
        $traceLog .= "========== END TRACE ==========";

        TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__,
            'COMPREHENSIVE POLICY TRACE - Agency: ' . $agencyId,
            $traceLog
        );
    }

    public function computeRuleset($parsed, $data)
    {
    }

    /**
     * Checks if there are at least 5 new clients with 'Perfezionamento' type in the given policies.
     *
     * Queries the internal clients view to count new clients based on CF_PIVA and flag_risparmio_202306.
     *
     * @param array $policies List of policy data arrays.
     * @return bool True if at least 5 new clients are found, false otherwise.
     */
    protected function checkIncreasedRate($policies)
    {
        $newClients = 0;
        foreach ($policies as $policy) {

            if ( $policy['tipoPremioQuietanza'] !== 'Perfezionamento' ) {
                continue;
            }

            $newClientCheck = $this->pdoQuery("
                    SELECT * FROM vw_inc_raccolta_2025_clients 
                    WHERE CF_PIVA = '" . $policy["CF_PIVA"] . "' AND flag_risparmio_202306 = 0"
            )->fetchAll(\PDO::FETCH_ASSOC);
            if (!empty($newClientCheck)) {
                $newClients++;
            }
        }
        return $newClients >= 5;
    }

    /**
     * Determines if the given policy belongs to a new client by querying the internal clients view.
     *
     * @param array $policy Policy data array.
     * @return bool True if the client is new, false otherwise.
     */
    protected function isNewClient($policy)
    {

        $newClientCheck = $this->pdoQuery("
                SELECT * FROM vw_inc_raccolta_2025_clients 
                WHERE CF_PIVA = '" . $policy["CF_PIVA"] . "' AND flag_risparmio_202306 = 0"
        )->fetchAll(\PDO::FETCH_ASSOC);
        return !empty($newClientCheck);
    }

    /**
     * Checks if the given policy qualifies as OTP by querying the internal OTP data table.
     *
     * @param array $policy Policy data array.
     * @return bool True if the policy is found in the OTP table, false otherwise.
     */
    protected function isOtp($policy)
    {
        $otpCheck = $this->pdoQuery("
                SELECT * FROM inc_raccolta_2025_data_otp 
                WHERE polizza = '" . $policy["polizza"] . "' 
                AND codiceProdotto = '" . $policy["codiceProdotto"] . "'"
        )->fetchAll(\PDO::FETCH_ASSOC);
        return !empty($otpCheck);
    }

    protected function getTPDoc($policy)
    {
        // Determine tpDoc value based on tipoPremioQuietanza
        if (isset($policy["tipoPremioQuietanza"])) {
            if ($policy["tipoPremioQuietanza"] === "Perfezionamento") {
                return 'POL';
            } elseif ($policy["tipoPremioQuietanza"] === "Versamenti aggiuntivo PUR/Q") {
                return 'VS';
            }
        }
        return null;
    }

    /**
     * Determines if the given policy is excluded based on exclusion/reduction criteria.
     *
     * Queries the inc_raccolta_2025_data_exclusion_reduction table for a matching policy,
     * agency, product code, and tpDoc, where both premio04 and premio08 are zero.
     *
     * @param array $policy Policy data array.
     * @return bool True if the policy is excluded, false otherwise.
     */
    protected function checkExclusion($policy)
    {
        $tpDoc = $this->getTPDoc($policy);

        $exclusionCheck = $this->pdoQuery("
        SELECT * FROM inc_raccolta_2025_data_exclusion_reduction 
        WHERE polizza = '" . $policy["polizza"] . "' 
        AND agenzia_id = '" . $policy["agenzia_id"] . "' 
        AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
        AND tpDoc = '" . $tpDoc . "'
        AND premio04 = 0 AND premio08 = 0"
        )->fetchAll(\PDO::FETCH_ASSOC);

        return !empty($exclusionCheck);
    }

    /**
     * Checks if the given policy qualifies for a reduced rate by querying the exclusion/reduction table.
     *
     * @param array $policy Policy data array.
     * @return float|false Returns the reduced rate value (premio04) if applicable, or false otherwise.
     */
    protected function checkReducedRate($policy)
    {
        $tpDoc = $this->getTPDoc($policy);

        $stmt = $this->pdoQuery("
            SELECT premio04 FROM inc_raccolta_2025_data_exclusion_reduction 
            WHERE polizza = '" . $policy["polizza"] . "' 
            AND agenzia_id = '" . $policy["agenzia_id"] . "' 
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
              AND tpDoc = '" . $tpDoc . "'
              AND premio04 > 0 AND premio08 = 0
            LIMIT 1
        ");

        $row = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($row && isset($row['premio04'])) {
            return $row['premio04'];
        }
        return false;
    }

    protected function checkReducedPremium($policy)
    {
        $tpDoc = $this->getTPDoc($policy);

        $stmt = $this->pdoQuery("
            SELECT * FROM inc_raccolta_2025_data_exclusion_reduction
            WHERE polizza = '" . $policy["polizza"] . "'
            AND agenzia_id = '" . $policy["agenzia_id"] . "'
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
              AND tpDoc = '" . $tpDoc . "'
              AND premio04 = 0 AND premio08 > 0
            LIMIT 1
        ");

        $row = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($row && isset($row['premio08'])) {
            return $row['premio08'];
        }
        return false;
    }

    protected function applyExclusion($policy)
    {
        // Log the exclusion before applying it
        TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__,
            'EXCLUSION APPLIED - Agency: ' . $policy["agenzia_id"] .
            ', Policy: ' . $policy["polizza"] .
            ', Product: ' . $policy["codiceProdotto"] .
            ', Type: ' . $policy["tipoPremioQuietanza"] .
            ', Original Premium: ' . $policy["premio"],
            'Policy excluded - premium set to 0, esclusa flag set to 1'
        );

        $stmt = $this->pdoQuery("
            UPDATE inc_raccolta_2025_data
            SET premio = 0, esclusa = 1
            WHERE polizza = '" . $policy["polizza"] . "'
            AND agenzia_id = '" . $policy["agenzia_id"] . "'
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
            AND tipoPremioQuietanza = '" . $policy["tipoPremioQuietanza"] . "'
        ");

        $stmt->execute();
    }

    protected function applyRateReduction($policy, $premium)
    {
        if ($policy["tipoPremioQuietanza"] === 'Perfezionamento') {
            // Log the rate reduction before applying it
            TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__,
                'RATE REDUCTION APPLIED - Agency: ' . $policy["agenzia_id"] .
                ', Policy: ' . $policy["polizza"] .
                ', Product: ' . $policy["codiceProdotto"] .
                ', Type: ' . $policy["tipoPremioQuietanza"] .
                ', Original Premium: ' . $policy["premio"] .
                ', New Premium: ' . $premium,
                'Rate reduction applied - aliqRidotta set to 1, premium updated'
            );

            // For 'Perfezionamento', update premio to provided premium
            $stmt = $this->pdoQuery("
                UPDATE inc_raccolta_2025_data
                SET aliqRidotta = 1, premio = " . $premium . "
                WHERE polizza = '" . $policy["polizza"] . "'
                AND agenzia_id = '" . $policy["agenzia_id"] . "'
                AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
                AND tipoPremioQuietanza = '" . $policy["tipoPremioQuietanza"] . "'
            ");
            $stmt->execute();

        } elseif ($policy["tipoPremioQuietanza"] === 'Versamenti aggiuntivo PUR/Q') {
            // For 'Versamenti aggiuntivo PUR/Q', find all policies with same polizza and divide premium
            $countStmt = $this->pdoQuery("
                SELECT COUNT(*) as policy_count
                FROM inc_raccolta_2025_data
                WHERE polizza = '" . $policy["polizza"] . "'
                AND tipoPremioQuietanza = 'Versamenti aggiuntivo PUR/Q'
            ");
            $countResult = $countStmt->fetch(\PDO::FETCH_ASSOC);
            $policyCount = $countResult['policy_count'];

            if ($policyCount > 0) {
                $dividedPremium = $premium / $policyCount;

                // Log the rate reduction for PUR/Q policies
                TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__,
                    'RATE REDUCTION APPLIED (PUR/Q) - Agency: ' . $policy["agenzia_id"] .
                    ', Policy: ' . $policy["polizza"] .
                    ', Product: ' . $policy["codiceProdotto"] .
                    ', Type: ' . $policy["tipoPremioQuietanza"] .
                    ', Original Premium: ' . $policy["premio"] .
                    ', Total Premium: ' . $premium .
                    ', Policy Count: ' . $policyCount .
                    ', Divided Premium: ' . number_format($dividedPremium, 2, '.', ''),
                    'Rate reduction applied to all PUR/Q policies - aliqRidotta set to 1, premium divided equally'
                );

                // Update all policies with the same polizza and tipoPremioQuietanza
                $updateStmt = $this->pdoQuery("
                    UPDATE inc_raccolta_2025_data
                    SET aliqRidotta = 1, premio = " . number_format($dividedPremium, 2, '.', '') . "
                    WHERE polizza = '" . $policy["polizza"] . "'
                    AND tipoPremioQuietanza = 'Versamenti aggiuntivo PUR/Q'
                ");
                $updateStmt->execute();
            }

        }
    }

    /**
     * Applies a premium reduction to a policy, setting the reduced premium only for the last policy in the agency group.
     *
     * Updates the database to set 'aliqRidotta' and the calculated premium value.
     *
     * @param array $policy  Current policy data.
     * @param float $premium Reduced premium value to apply.
     * @return float The premium value set (0 or $premium).
     */
    protected function applyPremiumReduction($policy, $premium)
    {

        // Recupera tutte le polizze per la stessa agenzia, ordinate per data contabile crescente
        $allPoliciesQuery = $this->pdoQuery("
            SELECT * FROM inc_raccolta_2025_data
            WHERE agenzia_id = '" . $policy["agenzia_id"] . "'
            AND polizza = '" . $policy["polizza"] . "'
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
            ORDER BY dataContabile ASC
        ");
        $policies = $allPoliciesQuery->fetchAll(\PDO::FETCH_ASSOC);

        // Verifica se la polizza corrente è l'ultima del gruppo ordinato
        $isLastPolicy = false;
        if (!empty($policies)) {
            $lastPolicy = end($policies);
            $isLastPolicy = (
                $lastPolicy['agenzia_id'] === $policy['agenzia_id'] &&
                $lastPolicy['polizza'] === $policy['polizza'] &&
                $lastPolicy['codiceProdotto'] === $policy['codiceProdotto'] &&
                $lastPolicy['dataContabile'] === $policy['dataContabile']
            );
        }

        //TRACE and $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "POLIZZE " . $policy['polizza'] ." : " . count($policies));

        // Se è l'ultima polizza, assegna il premio ridotto, altrimenti imposta a zero
        if ( $isLastPolicy || count($policies) === 1 ) {
            $premio = $premium;
        } else {
            $premio = 0;
        }

        // Log the premium reduction before applying it
        TRACE and $this->trace(LOG_INFO, 0, __FUNCTION__,
            'PREMIUM REDUCTION APPLIED - Agency: ' . $policy["agenzia_id"] .
            ', Policy: ' . $policy["polizza"] .
            ', Product: ' . $policy["codiceProdotto"] .
            ', Type: ' . $policy["tipoPremioQuietanza"] .
            ', Original Premium: ' . $policy["premio"] .
            ', New Premium: ' . $premio .
            ', Is Last Policy: ' . ($isLastPolicy ? 'Yes' : 'No') .
            ', Policy Count: ' . count($policies),
            'Premium reduction applied - premioRidotto set to 1, premium ' .
            ($premio > 0 ? 'set to ' . $premio : 'set to 0 (not the last policy)')
        );

        // Aggiorna la polizza nel database impostando premioRidotto a 1 e il premio calcolato
        $stmt = $this->pdoQuery("
            UPDATE inc_raccolta_2025_data
            SET premioRidotto = 1, premio = " . $premio . "
            WHERE polizza = '" . $policy["polizza"] . "'
            AND agenzia_id = '" . $policy["agenzia_id"] . "'
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
            AND tipoPremioQuietanza = '" . $policy["tipoPremioQuietanza"] . "'
            AND dataContabile = '" . $policy["dataContabile"] . "'
        ");

        $stmt->execute();

        // Restituisce il valore del premio impostato
        return $premio;
    }

}
