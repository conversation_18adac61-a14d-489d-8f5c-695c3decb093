<?php namespace api\apps\incentive;

use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class IncentiveController extends \metadigit\core\web\controller\ActionController {

    /**
     * @var Repository
     */
    protected $incentive;

    /**
     * @var Repository
     */
    protected $incentiveData;

    /**
     * @var Repository
     */
    protected $restart;

    /**
     * @var Repository
     */
    protected $besmart;

    protected $config = [284, 283, 289, 290, 291, 293, 294, 295, 300, 304, 305, 311, 324, 325, 326, 329, 331, 333, 337];

    /**
     * Return data.
     *
     * @routing(method="GET", pattern="/")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function indexAction(Request $Req, Response $Res)
    {
        // @TODO escludere agenzie non partecipanti

        if (! in_array($_SESSION['AUTH']['UTYPE'], ['KA', 'AMMINISTRATORE', 'AGENTE', 'AREAMGR', 'DISTRICTMGR'])) {
            return http_response_code(401);
        }

        if (! $year = $Req->get('year')) {
            http_response_code(400);
            return $Res->set('success', false)->setView('json:');
        }

        $data = $this->incentive->fetchAll(
            null,
            null,
            "id",
            "year,EQ,$year",
            Repository::FETCH_ARRAY
        );

        $incentives = $this->format($data);

        if ($year == 2022) {
            $restart = $this->format($this->restart());
            $besmart = $this->format($this->besmart());
            $result = array_merge($restart, $besmart, $incentives);
            return $Res->set('data', array_values($result))->setView('json:');
        }

        return $Res->set('data', array_values($incentives))->setView('json:');
    }

    public function restart()
    {
        $data = $this->restart->fetchAll(
            null,
            null,
            "id",
            null,
            Repository::FETCH_ARRAY
        );

        return array_map(function($item){
            $item['program'] = 'restart';
            $item['protection'] = 6;
            return $item;
        }, $data);
    }

    public function besmart()
    {
        $data = $this->besmart->fetchAll(
            null,
            null,
            "id",
            null,
            Repository::FETCH_ARRAY
        );

        return array_map(function($item){
            $item['program'] = 'besmart';
            $item['protection'] = 7;
            return $item;
        }, $data);
    }

    protected function format($data)
    {
        foreach($data as $row) {
            if (! isset($result[$row['program']])) {
                $result[$row['program']] = [
                    'program' => $row['program'],
                    'status' => null,
                    'protection' => $row['protection'],
                    'start' => $row['start'],
                    'end' => $row['end'],
                    'lastUpdate' => $row['lastUpdateLabel'],
                    'id' => $row['id'],
                    'hasStatusDownload' => $this->checkIncentiveHasDownload($row['id']),
                ];
            }

            $result[$row['program']]['status'] = in_array($row['status'], ['inactive', 'active', 'started']);

            if ($row['lastUpdateLabel'] > $result[$row['program']]['lastUpdate']) {
                $result[$row['program']]['lastUpdate'] = $row['lastUpdateLabel'];
            }

            if ($row['start'] < $result[$row['program']]['start']) {
                $result[$row['program']]['start'] = $row['start'];
            }

            if ($row['end'] > $result[$row['program']]['end']) {
                $result[$row['program']]['end'] = $row['end'];
            }

            $result[$row['program']]['periods'][] = $row;
        }

        return $result;
    }

    protected function checkIncentiveHasDownload($id)
    {
        return in_array($id, $this->config);
    }

    /**
     * Get relevant policies on specific incentive
     * @routing(pattern="/policies-detail/<incentive_id>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $incentive_id
     * @throws \metadigit\core\context\ContextException
     */
    public function policiesDetailDownloadAction(Request $Req, Response $Res, $incentive_id)
    {

        if (! $agenzia_id = $_SESSION['AUTH']['AGENZIA']) {
            return http_response_code(400);
        }

        try {

            $incentive = $this->incentive->fetch($incentive_id, Repository::FETCH_ARRAY);
            $data = $this->incentiveData->fetchAll(null, null, null, "incentive_id,EQ,$incentive_id|agenzia_id,EQ,$agenzia_id", Repository::FETCH_ARRAY);

            return $Res
                ->set('data', $data)
                ->set('saveName', strtoupper($incentive['program']) . " - Dettaglio Polizze $agenzia_id")
                ->setView('file-excel:/policies-detail');
        } catch(\Exception $Ex) {
            http_response_code(500);
            TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
        }

    }

}
