<? require_once("libs.php");

$lite = false;
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<link rel="stylesheet" type="text/css" href="css/site.css"/>
	<link rel="stylesheet" type="text/css" href="plugins/flexcroll/flexcrollstyles.css"/>
	<script type="text/javascript" src="/Agente/js/site.js"></script>
	<script type='text/javascript' src="plugins/flexcroll/flexcroll.js"></script>
	<title>Gara Speciale Vita 2012</title>
</head>
<body>
	<div id="center_frame_container">
		<div id="content_container">

			<div id="background1"></div>

			<div id="title1">Gare Speciali 2012 >>></div>
			<div id="btn1_active" onclick="location.href='/Agente/Iniziative/56-SpecialeVita/'"></div>
			<div id="btn2" onclick="location.href='/Agente/Iniziative/57-SpecialePrivati/'"></div>
			<div id="btn3" onclick="location.href='/Agente/Iniziative/54-SpecialeAziendeRischiAgricoli/'"></div>
			<div id="btn5" onclick="location.href='/Agente/Iniziative/55-GaraGlobale/'"></div>

			<div id="btn6"  onclick="location.href='RegolamSpecialeVita2012.pdf'"></div>

			<div id="infos">
<span class="black_16_bold">
<?

switch($Status->gruppo)
{
	case '1':
			$gruppo_name = "Gruppo 1";

			$viaggi = 8;

			break;

	case '2':
			$gruppo_name = "Gruppo 2";

			$viaggi = 7;

			break;

	case '3':
			$gruppo_name = "Gruppo 3";

			$viaggi = 6;

			break;

	case '4':
			$gruppo_name = "Gruppo 4";

			$viaggi = 5;

			break;

	case '5':
			$gruppo_name = "Gruppo 5";

			$viaggi = 4;

			break;

	case '6':
			$gruppo_name = "Gruppo 6";

			$viaggi = 3;

			break;

	case '7':
			$gruppo_name = "Gruppo 7";

			$viaggi = 2;

			break;

	case '8':
			$gruppo_name = "Gruppo 8";

			$viaggi = 1;

			break;

	default:
		$gruppo_name = "Gruppo non identificato";
		$viaggi = 0;

}
print $gruppo_name;

?>
</span><br/>
Obiettivi minimi:<br/>
Nuova Produzione <b>€ <?=number_format($Status->obiettivoPremi,0,',','.')?></b><br/>
Contratti Vita <b><?=number_format($Status->obiettivoPezzi,0,',','.')?></b><br/>

			</div>

			<div id="table_info1">
<?= $lite ? 'Composizione' : 'Classifica'?>
<br/>
<?=$gruppo_name?>
			</div>

			<div id="table_info2">

In palio <span class="blue_text"><b><?=$viaggi == 1 ? $viaggi.' viaggio' : $viaggi.' viaggi'?></b></span> (per 2 persone)<br/>
della durata di un weekend lungo

			</div>

			<div id="table_header">
				<div class="dp_grid_1"><br/>Posizione
				</div>
				<div class="dp_grid_2"><br/>Agenzia
				</div>
				<div class="dp_grid_3"><br/>Località
				</div>
				<div class="dp_grid_4"><br/>N. Polizze
				</div>
				<div class="dp_grid_5">Premio<br/>Computabile
				</div>
			</div>

			<div id="table_info3">

				<?= $lite ? '' : 'DATI AGGIORNATI AL '.$dataAgg ?>

			</div>


			<div id="fullpage_grid" class='flexcroll'>
			<?

			if($classifica)
			{
				$i = 0;

				foreach($classifica as $key => $value)
				{

					if($Agente->agenzia_id == $value->id)
						$row_class_type = '_red';
					else if($i < $viaggi && !$lite)
						$row_class_type = '_blue';
					else
						$row_class_type = '';

					$i++;

					if($lite)
					{
					?>
						<div class="fullpage_grid_row<?=$row_class_type?>">
							<div class="dp_grid_field_1">&nbsp;
							</div>
							<div class="dp_grid_field_2"><?=check_var($value->id) ? truncateString($value->id, 8):"&nbsp;" ?>
							</div>
							<div class="dp_grid_field_3"><?=check_var($value->localita) ? truncateString($value->localita, 28):"&nbsp;" ?>
							</div>
							<div class="dp_grid_field_4">&nbsp;
							</div>
							<div class="dp_grid_field_5">&nbsp;
							</div>
						</div>

					<?
					}
					else
					{
					?>
						<div class="fullpage_grid_row<?=$row_class_type?>">
							<div class="dp_grid_field_1"><?=($value->posizione)?$value->posizione:'-'?>
							</div>
							<div class="dp_grid_field_2"><?=check_var($value->id) ? truncateString($value->id, 8):"&nbsp;" ?>
							</div>
							<div class="dp_grid_field_3"><?=check_var($value->localita) ? truncateString($value->localita, 28):"&nbsp;" ?>
							</div>
							<div class="dp_grid_field_4"><?=$value->numeroPolizze?>
							</div>
							<div class="dp_grid_field_5"><?=check_var($value->premioComputabile) ? number_format($value->premioComputabile,2,',','.'):"&nbsp;" ?>
							</div>
						</div>

					<?
					}
				}
			}

			?>

			</div>
		</div>
	</div>
</body>
</html><? echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>