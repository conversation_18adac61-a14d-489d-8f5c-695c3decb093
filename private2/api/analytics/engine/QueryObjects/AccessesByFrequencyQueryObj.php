<?php

namespace api\analytics\engine\QueryObjects;

use api\analytics\engine\Engine;

class AccessesByFrequencyQueryObj extends Engine
{

  protected function getMonthDenominator($filters)
  {
    $filteredYear = empty($filters['year']) ? date('Y') : $filters['year'];
    $currentYear = date('Y');

    $monthDenominator = 12;
    if ($filteredYear == $currentYear) {
      $monthDenominator = intval(date('m'));
    }

    $quarterFilteredNo = $monthDenominator;
    if (!empty($filters['timePeriod'])) {
      $quarterFilteredNo = 0;
      foreach ($filters['timePeriod'] as $period) {
        if (strpos($period, 'Q') !== false) {
          $quarterFilteredNo += 3;
        }
        if(strpos($period, 'Y') !== false) {
          $quarterFilteredNo = 12;
          break;
        }
      }
    }

    return min($monthDenominator, $quarterFilteredNo);
  }

  public function build($filters)
  {
    $this->disableAutoBuilder();
    // INFO: per conteggiare tutti gli accessi
    //->addWhere("l1 = 'login'")
    $this->addFilters($filters);
    $this->addACL();
    $filtersSql = $this->getFilterQueryString();
    $monthDenominator = $this->getMonthDenominator($filters);

    $this->where = [];

    if (!empty($filters['year'])) {
      unset($filters['year']);
    }
    if (!empty($filters['timePeriod'])) {
      unset($filters['timePeriod']);
    }
    $this->generateStdFilters(self::TABLE, $filters, 'vwu');

    $this->addWhere("vwu.active = 1");
    //$this->addWhere("vwu.type = 'AGENTE'");
    $this->addWhere("vwu.id NOT IN ( SELECT DISTINCT user_id FROM UserCategories)");
    $filterUserSql = $this->getFilterQueryString();

    $this->addCommand("
      WITH UserAccessCounts AS (
          SELECT
              user_id,
              COUNT(*) AS access_count
          FROM
                vw_stats_users_actions AS vsua
          $filtersSql
          GROUP BY
              user_id
      ),
      UserCategories AS (
          SELECT
              user_id,
              CASE
                  WHEN access_count/$monthDenominator >= 10 THEN '10+'
                  WHEN access_count/$monthDenominator >= 4 AND access_count/$monthDenominator < 10 THEN '4-10'
                  WHEN access_count/$monthDenominator > 0 AND access_count/$monthDenominator < 4 THEN '1-3'
                  ELSE '0'
              END AS access_category
          FROM
              UserAccessCounts
          GROUP BY
              user_id
      )
      SELECT
          access_category,
          COUNT(DISTINCT user_id) AS user_count
      FROM
          UserCategories
      GROUP BY
          access_category
      UNION ALL
      SELECT
          '0' AS access_category,
          COUNT(*) AS user_count
      FROM
          vw_users vwu
        $filterUserSql
    ");
  }
}
