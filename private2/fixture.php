<?php

namespace pagendo\model;

class User extends \data\User {

	protected $_data_db = array(
		'id'=>null,
		'type'=>null,
		'active'=>0,
		'quarantena'=>0,
		'nome'=>null,
		'cognome'=>null,
		'email'=>'',
		'cellulare'=>'',
		'area'=>null,
		'district'=>null,
		'agenzia_id'=>null,
		'codEsazione'=>null,
		'rui'=>null,
		'piva'=>null,
		'ruolo'=>null,
		'acl_elearning'=>null,
		'updatedAt'=>0
	);

	/**
	 * @param array $attrs attributes to inject into new object
	 * @param boolean $isNewRecord TRUE if object is not yet persisted into database
	 */
	final function __construct(array $attrs=null, $isNewRecord=true) {
		if(is_array($attrs)) foreach($attrs as $k=>$v){ if(array_key_exists($k,$this->_data_db)) $this->_data_db[$k]=$v; }
	}

}
class Agenzia {

}


namespace pagendo\util\date;

class YearManager  {

}
