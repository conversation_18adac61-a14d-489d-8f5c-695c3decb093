<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data">
	<includes>
		<include namespace="system" />
		<include namespace="data.apps.formazione" />
		<include namespace="data.apps.cmsAgenzie" />
	</includes>
	<objects>
		<object id="data.ACL" class="data\ACL"/>
		<object id="data.AgenzieRepository" class="data\AgenzieRepository">
			<constructor>
				<arg name="class">data\Agenzia</arg>
			</constructor>
		</object>
		<object id="data.AppAuthRepository" class="metadigit\core\db\orm\Repository">
			<constructor>
				<arg name="class">data\AppAuth</arg>
			</constructor>
		</object>
		<object id="data.UsersRepository" class="data\UsersRepository">
			<constructor>
				<arg name="class">data\User</arg>
			</constructor>
		</object>
		<object id="data.UsersAuthRepository" class="metadigit\core\db\orm\Repository">
			<constructor>
				<arg name="class">data\UserAuth</arg>
			</constructor>
		</object>
		<object id="data.UsersIceboxRepository" class="metadigit\core\db\orm\Repository">
			<constructor>
				<arg name="class">data\UserIcebox</arg>
			</constructor>
		</object>
		<object id="data.SigningUp" class="data\SigningUp">
			<properties>
				<property name="signingUp" type="object">data.apps.formazione.Utils.SigningUp</property>
			</properties>
		</object>
		<object id="data.MyPage" class="data\MyPage">
			<properties>
				<property name="mypage" type="object">data.apps.cmsAgenzie.CreateMyPageEntity</property>
			</properties>
		</object>
	</objects>
	<events>
		<event name="orm:pre-count">
			<listeners>
				<listener>data.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:pre-fetch">
			<listeners>
				<listener>data.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:pre-fetch-all">
			<listeners>
				<listener>data.ACL->onFetch</listener>
			</listeners>
		</event>
		<event name="orm:post-insert">
			<listeners>
				<listener>data.SigningUp->onPostSave</listener>
				<listener>data.MyPage->onPostSave</listener>
			</listeners>
		</event>
		<event name="orm:post-update">
			<listeners>
				<listener>data.SigningUp->onPostSave</listener>
			</listeners>
		</event>
	</events>
</context>
