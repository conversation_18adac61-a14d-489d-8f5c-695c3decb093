<?php
namespace data;
use metadigit\core\db\Query,
    metadigit\core\db\orm\Metadata,
    metadigit\core\db\orm\OrmEvent;

class UsersRepository extends \metadigit\core\db\orm\Repository {

    function fetchArray($fields, $criteriaExp = '') {
        $OrmEvent = new OrmEvent($this);
        $this->Context->trigger(OrmEvent::EVENT_PRE_FETCH_ALL, null, null, $OrmEvent);
        $Query = (new Query($this->pdo))->setCriteriaDictionary(Metadata::get($this->class)->criteria())->on('users', $fields)
            ->orderBy('id ASC')->criteriaExp($OrmEvent->getCriteriaExp());
        if ($criteriaExp) {
            $Query = $Query->criteriaExp($criteriaExp);
        }

        return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
    }

	/**
	 * @param User $User
	 * @param string|null $validateMode
	 * @return array
	 */
	function validate($User, $validateMode=null) {
		$errors = [];
		$idCriteria = ($User->id==null) ? 'id IS NOT NULL' : 'id != :id';

		// ID agenzia
		$User->agenzia_id = strtoupper($User->agenzia_id);

		// isAzienda, nome, cognome
		if($User->isAzienda) {
			$User->nome = null;
			if($User->cognome == '') $errors['cognome'] = 'required';
			// @NOTE: excluded on 2013-12-05 // if(!preg_match('/^[0-9]{11}$/',$User->piva)) { $errors['piva'] = true; $codes[] = '6'; }
		} else {
			if($User->nome == '') $errors['nome'] = 'required';
			if($User->cognome == '') $errors['cognome'] = 'required';
			if($User->piva != '' && !preg_match('/^[0-9]{11}$/',$User->piva)) $errors['piva'] = 'regex';
		}

		// email
		$User->email = strtolower($User->email);
		$params = ['email'=>$User->email];
		if($User->id != null) $params['id'] = $User->id;
		if((int)(new Query($this->pdo))->on('users')->criteria($idCriteria.' AND email = :email')->execCount($params) > 0) $errors['email'] = 'unique';

		// codEsazione
		$codEsazioneQuery = (new Query($this->pdo))->on('users')->criteria($idCriteria.' AND agenzia_id = :agenzia_id AND codEsazione = :codEsazione');
		$codEsazione = function($required=false) use ($User, &$errors, $codEsazioneQuery) {
			// build query params
			$params = ['codEsazione'=>$User->codEsazione, 'agenzia_id'=>$User->agenzia_id];
			if($User->id != null) $params['id'] = $User->id;
			// check
			if($User->codEsazione=='') $User->codEsazione = null;
			if(!$required && is_null($User->codEsazione)) return;
			if(is_null($User->codEsazione)) $errors['codEsazione'] = 'REQUIRED';
			elseif(!preg_match('/^[a-zA-Z0-9]{6}$/',$User->codEsazione)) $errors['codEsazione'] = 'REGEX';
			elseif($codEsazioneQuery->execCount($params) > 0) $errors['codEsazione'] = 'UNIQUE';
		};

		// RUI
		$ruiQuery = (new Query($this->pdo))->on('users')->criteria($idCriteria.' AND agenzia_id = :agenzia_id AND rui = :rui');
		$rui = function($required=false) use ($User, &$errors, $ruiQuery) {
			// build query params
			$params = ['rui'=>$User->rui, 'agenzia_id'=>$User->agenzia_id];
			if($User->id != null) $params['id'] = $User->id;
			// check
			if($User->rui=='') $User->rui = null;
			if(!$required && is_null($User->rui)) return;
			if(is_null($User->rui)) $errors['rui'] = 'REQUIRED';
			elseif(!preg_match('/^[E|B][0-9]{9}$/',$User->rui)) $errors['rui'] = 'REGEX';
			elseif($User->acl_elearning && $User->rui[0] == 'B') $errors['rui'] = 'BROKER';
			elseif((int)$ruiQuery->execCount($params) > 0) $errors['rui'] = 'UNIQUE';
		};

		// ruolo
		$ruolo = function() use ($User, &$errors, $codEsazione, $rui) {
			switch($User->ruolo) {
				case 'COLL_AGZ': // COLLABORATORE AGENZIA
				case 'SUBAGZ': // TITOLARE SUBAGENZIA
				case 'COLL_SUBAGZ': // COLLABORATORE SUBAGENZIA
					$codEsazione(true);
					$rui(true);
					break;
				case 'DIP_AGZ': // DIPENDENTE AGENZIA
				case 'DIP_SUBAGZ': // DIPENDENTE SUBAGENZIA
					$codEsazione();
					$rui();
					break;
				case 'NEO': // NEO INTERMEDIARIO
					$User->codEsazione = null;
					$User->rui = null;
					break;
				default:
					$errors['ruolo'] = 'required';
			}
		};

		// type
		switch($User->type) {
			case 'INTERMEDIARIO':
				$ruolo();
				break;
		}

		// acl_elearning
		if($User->acl_elearning) {
			// 2020-04-20 NEO abilitati su ABI // if($User->type == 'INTERMEDIARIO' && $User->ruolo == 'NEO') $errors['acl_elearning'] = 'NEO';
			if($User->isAzienda || is_null($User->cognome)) $errors['acl_elearning'] = 'AZIENDA';
			if(strtoupper($User->rui[0]) == 'B') $errors['acl_elearning'] = 'BROKER';
		}

		return $errors;
	}

    public function map($login) {

    }
}
