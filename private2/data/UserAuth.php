<?php
namespace data;
/**
 * @orm(source="vw_users_auth", target="users_auth")
 * @orm-criteria(area="area = ?1")
 * @orm-criteria(district="district = ?1")
 * @orm-criteria(agenzia="agenzia_id = ?1")
 * @orm-criteria(nomecognome="( nome LIKE ?1 OR cognome LIKE ?2 )")
 * @orm-criteria(login="( login LIKE ?1 OR login1 LIKE ?2 OR login2 LIKE ?3 )")
 */
class UserAuth extends User {
	use \metadigit\core\db\orm\EntityTrait;

	// auth data

	/** @orm(null) */
	protected $login;
	/** @orm(null) */
	protected $login1;
	/** @orm(null) */
	protected $login2;


	/** @orm(type="boolean", readonly) */
	protected $loginOK;
	/** @orm(type="boolean", readonly) */
	protected $passwordOK;
}
