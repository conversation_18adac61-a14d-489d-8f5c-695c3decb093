<?php
namespace data;
/**
 * @orm(source="vw_agenzie", target="agenzie")
 * @orm-criteria(area="area = ?1")
 * @orm-criteria(district="district = ?1")
 * @orm-criteria(agenzia="id = ?1")
 * @orm-criteria(query="( id LIKE ?1 OR nome LIKE ?2 )")
 * @orm-fetch-subset(nome="id, nome")
 * @orm-fetch-subset(anagrafica="id, area, district, localita, nome, email, status, telefono, fax, indirizzo, cap, citta, comune_id, provincia_id, regione_id, comune, provincia, regione, agVinc")
 * @orm-fetch-subset(API_contapunti="id, area, district, localita, nome, status, indirizzo, cap, citta")
 * @orm-fetch-subset(API_onteam="id, area, district, localita, nome, email, status, indirizzo, cap, citta")

 */
class Agenzia {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(primarykey)
	 * @validate(regex="/^[GND]{1}[A-Z0-9]{3}$/") */
	protected $id;
	/** @orm(type="integer") */
	protected $area;
	/** @orm(type="integer") */
	protected $district;
	/** @orm */
	protected $localita;
	/** @orm */
	protected $nome;
	/** @orm
	 * @validate(empty, email) */
	protected $email;
	/** @orm
	 * @validate(regex="/^(ON|OFF|DIREZ|INTER|NEW|RIORG)$/") */
	protected $status;

	// dati amagrafici

	/** @orm */
	protected $telefono;
	/** @orm(type="string", null) */
	protected $fax;
	/** @orm */
	protected $indirizzo;
	/** @orm
	 * @validate(regex="/^[0-9]{5}$/") */
	protected $cap;
	/** @orm */
	protected $citta;

	// GEO

	/** @orm(readonly) */
	protected $comune;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(readonly) */
	protected $regione;
	/** @orm(type="integer", null) */
	protected $comune_id;
	/** @orm(type="integer", null) */
	protected $provincia_id;
	/** @orm(type="integer", null) */
	protected $regione_id;

	// dati aziendali

	/** @orm */
	protected $modello;
	/** @orm(type="integer") */
	protected $modelloX = 0;
	/** @orm(type="integer") */
	protected $modelloY = 0;
	/** @orm(type="boolean") */
	protected $agVinc = false;
	/** @orm(type="string", null) */
	protected $mandato;
	/** @orm(type="date")
	 * @validate(regex="/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/") */
	protected $dataMandato;
	/** @orm(type="date", null) */
	protected $dataRiorg;
	/** @orm(type="integer") */
	protected $numAgenti = 0;
	/** @orm(type="integer") */
	protected $numDipAgz = 0;
	/** @orm(type="integer") */
	protected $numProdAgz = 0;
	/** @orm(type="integer") */
	protected $numSubagz = 0;

	// system data

	/** @orm(readonly) */
	protected $updatedAt;

	function nome() {
		return ('G'==substr($this->id,0,1)) ? $this->nome : $this->localita;
	}

	protected function onSave() {
		$this->id = strtoupper($this->id);
		$this->email = strtolower($this->email);
	}

	static function codeGAtoPA ($agenziaID) {
		return substr($agenziaID,0,1).substr($agenziaID,3,3);
	}
	static function codePAtoGA ($agenziaID) {
		return substr($agenziaID,0,1).'00'.substr($agenziaID,1,3);
	}
}
