<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.supernovaRF2024">

    <objects>
        <object id="data.incentive.supernovaRF2024.StatusRepository" class="data\incentive\supernovaRF2024\StatusRepository">
            <constructor>
                <arg name="class">data\incentive\supernovaRF2024\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.supernovaRF2024.StaticRepository" class="data\incentive\supernovaRF2024\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\supernovaRF2024\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.supernovaRF2024.DataRepository" class="data\incentive\supernovaRF2024\DataRepository">
            <constructor>
                <arg name="class">data\incentive\supernovaRF2024\Data</arg>
            </constructor>
        </object>
        <!--<object id="data.incentive.supernovaRF2024.MonitoringRepository" class="data\incentive\supernovaRF2024\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\supernovaRF2024\Monitoring</arg>
            </constructor>
        </object>-->
        <object id="data.incentive.supernovaRF2024.ACL" class="data\incentive\supernovaRF2024\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.supernovaRF2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.supernovaRF2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.supernovaRF2024.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
