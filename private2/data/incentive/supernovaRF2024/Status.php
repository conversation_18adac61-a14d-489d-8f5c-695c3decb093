<?php

namespace data\incentive\supernovaRF2024;

/**
 * @orm(target="inc_supernova_rf_2024_status", source="vw_inc_supernova_rf_2024_status")
 */

class Status
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm */
    protected $agenzia_id;

    /** @orm (type="float") */
    protected $premioIPPT;

    /** @orm (type="float") */
    protected $premioNC;

    /** @orm (type="integer") */
    protected $pezziOpen;

    /** @orm (type="float") */
    protected $incentIPPT;

    /** @orm (type="float") */
    protected $incentNC;

    /** @orm (type="float") */
    protected $incentOpen;

    /** @orm (type="integer") */
    protected $pezziOTP;

    /** @orm (type="float") */
    protected $incentOTP;


    /* MySql view data */

    /** @orm (readonly) */
    protected $localita;

    /** @orm (readonly) */
    protected $nome;

    /** @orm (type="integer", readonly) */
    protected $area;

    /** @orm (readonly) */
    protected $areaName;

    /** @orm (type="integer", readonly) */
    protected $district;

    /** @orm (readonly) */
    protected $districtName;

    /** @orm (type="float", readonly) */
    protected $incentTot;

    /** @orm (type="integer", readonly) */
    protected $objReached;
}
