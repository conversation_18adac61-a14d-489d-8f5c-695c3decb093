<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.rinnoviamociFase2">

    <objects>
        <object id="data.incentive.rinnoviamociFase2.AggregateRepository" class="data\incentive\rinnoviamociFase2\AggregateRepository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\Aggregate</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.StatusRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.DummyStatusRepository" class="data\incentive\rinnoviamociFase2\DummyStatusRepository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.PeriodsRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\Period</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.MonitoringAreaRepository" class="data\incentive\rinnoviamociFase2\MonitoringAreaRepository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\MonitoringArea</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.MonitoringDistrictRepository" class="data\incentive\rinnoviamociFase2\MonitoringDistrictRepository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\MonitoringDistrict</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.DataRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\rinnoviamociFase2\Data</arg>
            </constructor>
        </object>
        <object id="data.incentive.rinnoviamociFase2.ACL" class="data\incentive\rinnoviamociFase2\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.rinnoviamociFase2.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.rinnoviamociFase2.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.rinnoviamociFase2.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>
</context>
