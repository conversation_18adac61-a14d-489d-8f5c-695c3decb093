<?php namespace data\incentive\rinnoviamociFase2;

use metadigit\core\db\orm\OrmEvent;

class ACL
{
	use \metadigit\core\CoreTrait;

	function onFetch(OrmEvent $Event)
    {
        $criteriaExp = null;

        $repository = $Event->getRepository();

        if ($repository instanceof AggregateRepository || $repository instanceof DummyStatusRepository) {
            // Skip as it's only used in batch.
            return;
        }

		switch($_SESSION['AUTH']['UTYPE']) {
			case 'KA':
            case 'AMMINISTRATORE':
                $criteriaExp = "";
                break;
			case 'AREAMGR':

                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}";
                break;
			case 'DISTRICTMGR':
                if ($repository instanceof MonitoringAreaRepository) {
                    $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}";
                    break;
                }
                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}|district,EQ,{$_SESSION['AUTH']['DISTRICT']}";
                break;
            case 'AGENTE':
                $criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'];
                break;
			default:
				throw new \Exception('ACCESS DENIED');
		}

		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'].': '.$criteriaExp);

		$Event->criteriaExp($criteriaExp);
	}
}
