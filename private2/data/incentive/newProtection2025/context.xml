<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.newProtection2025">

    <objects>
        <object id="data.incentive.newProtection2025.StaticRepository" class="data\incentive\newProtection2025\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\newProtection2025\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.newProtection2025.DataRepository" class="data\incentive\newProtection2025\DataRepository">
            <constructor>
                <arg name="class">data\incentive\newProtection2025\Data</arg>
            </constructor>
        </object>
        <object id="data.incentive.newProtection2025.StatusRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\newProtection2025\Status</arg>
            </constructor>
        </object>
        <!--<object id="data.incentive.newProtection2025.MonitoringRepository" class="data\incentive\newProtection2025\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\newProtection2025\Monitoring</arg>
            </constructor>
        </object>-->
        <object id="data.incentive.newProtection2025.ACL" class="data\incentive\newProtection2025\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.newProtection2025.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.newProtection2025.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.newProtection2025.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
