<?php

namespace data\incentive\newProtection2025;

/**
 * @orm(target="inc_newpro_2025_status", source="vw_inc_newpro_2025_status")
 */

class Status
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm */
    protected $agenzia_id;

    /** @orm (type="integer")*/
    protected $pezziInfortuni;

    /** @orm (type="integer")*/
    protected $pezziSalute;

    /** @orm (type="float")*/
    protected $premioInfortuni;

    /** @orm (type="float")*/
    protected $premioSalute;

    /** @orm (type="float")*/
    protected $incentInfortuni;

    /** @orm (type="float")*/
    protected $incentSalute;

    /** @orm (readonly) */
    protected $localita;

    /** @orm (readonly) */
    protected $nome;

    /** @orm (readonly) */
    protected $fascia;

    /** @orm (type="integer", readonly) */
    protected $area;

    /** @orm (readonly) */
    protected $areaName;

    /** @orm (type="integer", readonly) */
    protected $district;

    /** @orm (readonly) */
    protected $districtName;
}
