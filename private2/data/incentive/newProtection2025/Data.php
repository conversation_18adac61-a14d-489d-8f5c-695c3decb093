<?php

namespace data\incentive\newProtection2025;

/**
 * @orm(source="vw_inc_newpro_2025_data",target="inc_newpro_2025_data")
 */

class Data
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm (readonly) */
    protected $id;

    /** @orm */
    protected $agenzia_id;

    /** @orm (readonly) */
    protected $canale;

    /** @orm */
    protected $polizza;

    /** @orm (readonly) */
    protected $tipoEsitoOperazione;

    /** @orm (type="date", readonly) */
    protected $dataEffettoContratto;

    /** @orm (type="date", readonly) */
    protected $dataScadenzaContratto;

    /** @orm (type="date", readonly) */
    protected $dataEffettoOperazione;

    /** @orm (type="date", readonly) */
    protected $dataContabileEmissione;

    /** @orm (type="date", readonly) */
    protected $dataContabileIncasso;

    /** @orm (type="date", readonly) */
    protected $dataEffettivoIncasso;

    /** @orm (readonly) */
    protected $frazionamento;

    /** @orm (readonly) */
    protected $numeroCampagna;

    /** @orm (readonly) */
    protected $nomeCampagna;

    /** @orm (readonly) */
    protected $codiceConvenzione;

    /** @orm (readonly) */
    protected $nomeConvenzione;

    /** @orm (readonly) */
    protected $tipoProdotto;

    /** @orm */
    protected $codiceProdotto;

    /** @orm (readonly) */
    protected $nomeProdotto;

    /** @orm (readonly) */
    protected $flagCollettiva;

    /** @orm (readonly) */
    protected $tipoCollettiva;

    /** @orm (type="float", readonly) */
    protected $premioNetto;

    /** @orm (type="float", readonly) */
    protected $premioAccessori;

    /** @orm (type="float")*/
    protected $premio;

}
