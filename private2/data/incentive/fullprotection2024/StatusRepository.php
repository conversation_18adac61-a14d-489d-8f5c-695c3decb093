<?php namespace data\incentive\fullprotection2024;

use metadigit\core\db\orm\Repository;

/**
 * @orm(target="inc_fullprotection_2024_status", source="vw_inc_fullprotection_2024_status")
 */

class StatusRepository extends Repository {
    use \metadigit\core\db\orm\EntityTrait;

    /**
     * @orm(primarykey, type="string")
     */
    protected $agenzia_id;

    /**
     * @orm(type="integer")
     */
    protected $pezziTot;

    /**
     * @orm(type="integer")
     */
    protected $pezziINF;

    /**
     * @orm(type="float")
     */
    protected $premiumFascia1;

    /**
     * @orm(type="float")
     */
    protected $premiumFascia2;

    /**
     * @orm(type="float")
     */
    protected $incentFascia1;

    /**
     * @orm(type="float")
     */
    protected $incentFascia2;

    /**
     * @orm(type="float")
     */
    protected $incentTotal;

    /**
     * @orm(readonly)
     */
    protected $localita;

    /**
     * @orm(readonly)
     */
    protected $nome;

    /**
     * @orm(readonly)
     */
    protected $areaName;

    /**
     * @orm(readonly)
     */
    protected $districtName;

}
