<?php namespace data\incentive\fullprotection;

use metadigit\core\db\orm\OrmEvent;

class ACL
{
    use \metadigit\core\CoreTrait;

    function onFetch(OrmEvent $Event)
    {
        $criteriaExp = null;

        switch ($_SESSION['AUTH']['UTYPE']) {
            case 'KA':
            case 'AMMINISTRATORE':
            case 'AGENTE':
                $criteriaExp = "";
                break;
            case 'AREAMGR':
                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}";
                break;
            case 'DISTRICTMGR':
                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}|district,EQ,{$_SESSION['AUTH']['DISTRICT']}";
                break;
            default:
                throw new \Exception('ACCESS DENIED');
        }

        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'] . ': ' . $criteriaExp);

        $Event->criteriaExp($criteriaExp);
    }
}
