<?php namespace data\incentive\fullprotection;

/**
 * @orm(source="vw_inc_fullprotection_status")
 */

class StatusRepository {
    use \metadigit\core\db\orm\EntityTrait;

    /**
     * @orm(readonly)
     */
    protected $agenzia_id;

    /**
     * @orm(readonly, type="integer")
     */
    protected $classification;

    /**
     * @orm(readonly, type="integer")
     */
    protected $prodPrevYear;

    /**
     * @orm(readonly, type="integer")
     */
    protected $targetIncrease;

    /**
     * @orm(readonly, type="integer")
     */
    protected $prod;

    /**
     * @orm(readonly, type="integer")
     */
    protected $target;

}
