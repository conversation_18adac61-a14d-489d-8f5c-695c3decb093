<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.fullprotection">

    <objects>
        <object id="data.incentive.fullprotection.StatusRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullprotection\StatusRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullprotection.StaticRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullprotection\StaticRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullprotection.ACL" class="data\incentive\fullprotection\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.fullprotection.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.fullprotection.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.fullprotection.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
