<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.focusFase22024">

    <objects>
        <object id="data.incentive.focusFase22024.ClientRepository" class="data\incentive\focusFase22024\ClientRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22024\Client</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22024.StatusRepository" class="data\incentive\focusFase22024\StatusRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22024\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22024.DataRepository" class="data\incentive\focusFase22024\DataRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22024\Data</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22024.StaticRepository" class="data\incentive\focusFase22024\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22024\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22024.MonitoringRepository" class="data\incentive\focusFase22024\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22024\Monitoring</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22024.ACL" class="data\incentive\focusFase22024\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.focusFase22024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.focusFase22024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.focusFase22024.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>
</context>
