<?php namespace data\incentive\focusFase22024;

use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;

class MonitoringRepository extends Repository
{

    public function getAggregate ($areaId = null, $districtId = null)
    {
        $sql = "
            SELECT district, districtName, area, areaName,
            SUM(obiettivo_clienti_trasformati) as obiettivo_clienti_trasformati,
            SUM(pezzi_trasformati) as pezzi_trasformati,
            SUM(10) as obiettivo_clienti_acquisiti, 
            SUM(pezzi_acquisiti) as pezzi_acquisiti,
            SUM(clienti_trasformati) as clienti_trasformati,
            SUM(clienti_trasformati_rel_nexus) as clienti_trasformati_rel_nexus,
            SUM(clienti_trasformati_OP_doppio) as clienti_trasformati_OP_doppio,
            SUM(clienti_trasformati_OP_doppio_rel_nexus) as clienti_trasformati_OP_doppio_rel_nexus,
            SUM(clienti_acquisiti_rel_nexus_sez2) as clienti_acquisiti_rel_nexus_sez2,
            SUM(clienti_acquisiti_rel_nexus_sez1) as clienti_acquisiti_rel_nexus_sez1,
            SUM(clienti_acquisiti_rel_AO_fine_inc) as clienti_acquisiti_rel_AO_fine_inc,
            SUM(incentMaturato) as incentMaturato
            FROM `vw_inc_focus2024_fase2_monitoring`";

        if ($areaId) {
            $sql .= " where area = $areaId group by district ORDER BY districtName ASC";
        }
        if (!$areaId && !$districtId) {
            $sql .= " where area != 99 group by area ORDER BY areaName ASC";
        }

        return Kernel::pdo()->query($sql)->fetchAll(Repository::FETCH_ARRAY);

    }

}

