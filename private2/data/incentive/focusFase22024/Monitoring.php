<?php

namespace data\incentive\focusFase22024;

/**
 * @orm(source="vw_inc_focus2024_fase2_monitoring")
 */

class Monitoring
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm (type="string", readonly) */
    protected $agenzia_id;

    /** @orm (type="string", readonly) */
    protected $localita;

    /** @orm (type="string", readonly) */
    protected $nome;

    /** @orm (type="integer", readonly) */
    protected $area;

    /** @orm (type="string", readonly) */
    protected $areaName;

    /** @orm (type="integer", readonly) */
    protected $district;

    /** @orm (type="string", readonly) */
    protected $districtName;

    /** @orm (type="integer", readonly) */
    protected $obiettivo_clienti_trasformati;

    /** @orm (type="integer", readonly) */
    protected $pezzi_trasformati;

    /** @orm (type="integer", readonly) */
    protected $obiettivo_clienti_acquisiti;

    /** @orm (type="integer", readonly) */
    protected $pezzi_acquisiti;

    /** @orm (type="integer", readonly) */
    protected $clienti_trasformati;

    /** @orm (type="integer", readonly) */
    protected $clienti_trasformati_rel_nexus;

    /** @orm (type="integer", readonly) */
    protected $clienti_trasformati_OP_doppio;

    /** @orm (type="integer", readonly) */
    protected $clienti_trasformati_OP_doppio_rel_nexus;

    /** @orm (type="integer", readonly) */
    protected $clienti_acquisiti_rel_nexus_sez1;

    /** @orm (type="integer", readonly) */
    protected $clienti_acquisiti_rel_nexus_sez2;

    /** @orm (type="integer", readonly) */
    protected $clienti_acquisiti_rel_AO_fine_inc;

    /** @orm (type="integer", readonly) */
    protected $boost_malattia_trasformazioni;

    /** @orm (type="integer", readonly) */
    protected $boost_malattia_bonus;

    /** @orm (type="integer", readonly) */
    protected $boost_infortuni_trasformazioni;

    /** @orm (type="integer", readonly) */
    protected $boost_infortuni_bonus;

    /** @orm (type="integer", readonly) */
    protected $boost_maxi_trasformazioni;

    /** @orm (type="integer", readonly) */
    protected $boost_maxi_bonus;

}
