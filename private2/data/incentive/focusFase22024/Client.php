<?php namespace data\incentive\focusFase22024;

/**
 * @orm(source="inc_focus2024_fase2_clients")
 */

class Client {
	use \metadigit\core\db\orm\EntityTrait;

    /** @orm(primarykey) */
    protected $iniziativa_id;

    /** @orm(primarykey) */
    protected $agenzia_id;

    /** @orm(primarykey) */
    protected $codiceCliente;

    /** @orm */
    protected $nominativo;

    /** @orm */
    protected $partenza;

    /** @orm */
    protected $arrivo;

    /** @orm (type="integer") */
    protected $acquisito;

    /** @orm (type="integer") */
    protected $conRelazioni;

    /** @orm (type="integer") */
    protected $boostInfortuni;

    /** @orm (type="integer") */
    protected $boostMalattia;

    /** @orm (type="integer") */
    protected $boostMaxi;
}
