<?php
namespace data\incentive;
/**
 * @orm(source="incentive_data")
 */
class IncentiveData {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(type="integer", primarykey) */
	protected $incentive_id;

	/** @orm */
	protected $ptf;

    /** @orm */
	protected $dataEstrazione;

    /** @orm */
	protected $dataEmissione;

    /** @orm */
	protected $dataIncasso;

    /** @orm */
	protected $dataEffetto;

    /** @orm */
	protected $agenzia_id;

    /** @orm */
	protected $ramo;

    /** @orm */
	protected $intermediario;

    /** @orm */
	protected $delega;

    /** @orm */
	protected $sezione;

    /** @orm */
	protected $polizza;

    /** @orm */
	protected $codiceIdBene;

    /** @orm */
	protected $nomeCliente;

    /** @orm */
	protected $codiceCliente;

    /** @orm */
	protected $CF_PIVA;

    /** @orm */
	protected $codiceProdotto;

    /** @orm */
	protected $motivo;

    /** @orm */
	protected $famigliaProdotto;

    /** @orm */
	protected $premioAnnuo;

    /** @orm */
	protected $premioUnico;

    /** @orm */
	protected $versamAgg;

    /** @orm */
	protected $deltaPremio;

    /** @orm */
	protected $premioComputabile;

    /** @orm */
	protected $codiceCampagna;

    /** @orm */
	protected $codiceConvenzione;

    /** @orm */
	protected $codicePartnership;

    /** @orm */
	protected $tipoCollettiva;

    /** @orm */
	protected $numPolizzaMadre;

    /** @orm */
	protected $modulareVita;

    /** @orm */
	protected $OTP;

    /** @orm */
	protected $FEA;

    /** @orm */
	protected $trasmissioneElettr;

    /** @orm */
	protected $polizzaDigitale;

}
