<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.focusFase22023">

    <objects>
        <object id="data.incentive.focusFase22023.ClientRepository" class="data\incentive\focusFase22023\ClientRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22023\Client</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22023.StatusRepository" class="data\incentive\focusFase22023\StatusRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22023\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22023.DataRepository" class="data\incentive\focusFase22023\DataRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22023\Data</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22023.StaticRepository" class="data\incentive\focusFase22023\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22023\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22023.MonitoringRepository" class="data\incentive\focusFase22023\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\focusFase22023\Monitoring</arg>
            </constructor>
        </object>
        <object id="data.incentive.focusFase22023.ACL" class="data\incentive\focusFase22023\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.focusFase22023.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.focusFase22023.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.focusFase22023.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>
</context>
