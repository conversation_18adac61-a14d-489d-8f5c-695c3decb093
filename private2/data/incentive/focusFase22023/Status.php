<?php namespace data\incentive\focusFase22023;

/**
 * @orm(source="inc_focus2023_fase2_status")
 */

class Status {
	use \metadigit\core\db\orm\EntityTrait;

    /** @orm(primarykey) */
    protected $agenzia_id;

    /** @orm(type="integer") */
    protected $obiettivo_clienti_trasformati;

    /** @orm(type="integer") */
    protected $clienti_trasformati;

    /** @orm(type="integer") */
    protected $clienti_trasformati_rel_nexus;

    /** @orm(type="integer") */
    protected $clienti_trasformati_OP_doppio;

    /** @orm(type="integer") */
    protected $clienti_trasformati_OP_doppio_rel_nexus;

    /** @orm(type="float") */
    protected $incent_trasformati;

    /** @orm(type="float") */
    protected $incent_trasformati_rel_nexus;

    /** @orm(type="float") */
    protected $incent_trasformati_OP_doppio;

    /** @orm(type="float") */
    protected $incent_trasformati_OP_doppio_rel_nexus;

    /** @orm(type="float") */
    protected $incent_trasformati_total;

    /** @orm(type="integer") */
    protected $pezzi_trasformati;

    /** @orm(type="integer") */
    protected $clienti_acquisiti_rel_nexus_sez1;

    /** @orm(type="integer") */
    protected $clienti_acquisiti_rel_nexus_sez2;

    /** @orm(type="integer") */
    protected $clienti_acquisiti_rel_AO_fine_inc;

    /** @orm(type="float") */
    protected $incent_acquisiti_rel_nexus_sez2;

    /** @orm(type="float") */
    protected $incent_acquisiti_rel_nexus_sez1;

    /** @orm(type="float") */
    protected $incent_acquisiti_rel_AO_fine_inc;

    /** @orm(type="float") */
    protected $incent_acquisiti_total;

    /** @orm(type="integer") */
    protected $pezzi_acquisiti;
}

