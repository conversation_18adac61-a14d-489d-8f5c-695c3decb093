<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.supernova2024">

    <objects>
        <object id="data.incentive.supernova2024.StatusRepository" class="data\incentive\supernova2024\StatusRepository">
            <constructor>
                <arg name="class">data\incentive\supernova2024\Status</arg>
            </constructor>
        </object>
        <object id="data.incentive.supernova2024.StaticRepository" class="data\incentive\supernova2024\StaticRepository">
            <constructor>
                <arg name="class">data\incentive\supernova2024\StaticModel</arg>
            </constructor>
        </object>
        <object id="data.incentive.supernova2024.DataRepository" class="data\incentive\supernova2024\DataRepository">
            <constructor>
                <arg name="class">data\incentive\supernova2024\Data</arg>
            </constructor>
        </object>
        <!--<object id="data.incentive.supernova2024.MonitoringRepository" class="data\incentive\supernova2024\MonitoringRepository">
            <constructor>
                <arg name="class">data\incentive\supernova2024\Monitoring</arg>
            </constructor>
        </object>-->
        <object id="data.incentive.supernova2024.ACL" class="data\incentive\supernova2024\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.supernova2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.supernova2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.supernova2024.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
