<?php

namespace data\incentive\supernova2024;

use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;

class StatusRepository extends Repository
{
    public function getAggregate ($areaId = null, $districtId = null)
    {
        if ($districtId) {
            $sql = "
            SELECT * FROM `vw_inc_supernova_2024_status`";
        }
        else {

            $sql = "
            SELECT district, districtName, area, areaName, agenzia_id, localita,
                SUM(incentIPPT) as incentIPPT,
                SUM(incentNC) as incentNC,
                SUM(incentOpen) as incentOpen,
                SUM(incentTot) as incentTot,
                sum(objReached = 1) as objReached,
                count(agenzia_id) as agenciesTot,
                (sum(objReached = 1) / count(agenzia_id) * 100) as objReachedPerc
            FROM `vw_inc_supernova_2024_status`";

        }

        if ($districtId) {
            $sql .= " where district = $districtId ORDER BY agenzia_id ASC";
        }
        else if ($areaId) {
            $sql .= " where area = $areaId group by district ORDER BY districtName ASC";
        }
        else {
            $sql .= " where area != 99 group by area ORDER BY areaName ASC";
        }

        return Kernel::pdo()->query($sql)->fetchAll(Repository::FETCH_ARRAY);

    }

    public function getPercentage ($areaId = null, $districtId = null)
    {
        if ($districtId) {
            $sql = "
            SELECT * FROM `vw_inc_supernova_2024_status`";
        }
        else {

            $sql = "
            SELECT district, districtName, area, areaName, agenzia_id, localita,
                SUM(incentIPPT) as incentIPPT,
                SUM(incentNC) as incentNC,
                SUM(incentOpen) as incentOpen,
                SUM(incentTot) as incentTot,
                objReached
            FROM `vw_inc_supernova_2024_status`";

        }

        if ($districtId) {
            $sql .= " where district = $districtId ORDER BY agenzia_id ASC";
        }
        else if ($areaId) {
            $sql .= " where area = $areaId group by district ORDER BY districtName ASC";
        }
        else {
            $sql .= " where area != 99 group by area ORDER BY areaName ASC";
        }

        return Kernel::pdo()->query($sql)->fetchAll(Repository::FETCH_ARRAY);

    }

}
