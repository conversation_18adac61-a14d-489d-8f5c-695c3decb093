<?php namespace data\incentive\fullprotection2023;

use metadigit\core\db\orm\Repository;

/**
 * @orm(source="inc_fullprotection_2023_status")
 */

class StatusRepository extends Repository {
    use \metadigit\core\db\orm\EntityTrait;

    /**
     * @orm(primarykey, type="string")
     */
    protected $agenzia_id;

    /**
     * @orm(type="integer")
     */
    protected $piecesTCM;

    /**
     * @orm(type="integer")
     */
    protected $piecesINF;

    /**
     * @orm(type="float")
     */
    protected $premiumTCMFascia1;

    /**
     * @orm(type="float")
     */
    protected $premiumTCMFascia2;

    /**
     * @orm(type="float")
     */
    protected $premiumINFFascia1;

    /**
     * @orm(type="float")
     */
    protected $premiumINFFascia2;

    /**
     * @orm(type="float")
     */
    protected $incentTCMFascia1;

    /**
     * @orm(type="float")
     */
    protected $incentTCMFascia2;

    /**
     * @orm(type="float")
     */
    protected $incentINFFascia1;

    /**
     * @orm(type="float")
     */
    protected $incentINFFascia2;

    /**
     * @orm(type="float")
     */
    protected $incentTotal;

}
