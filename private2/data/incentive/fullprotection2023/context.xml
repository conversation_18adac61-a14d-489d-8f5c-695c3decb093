<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.fullprotection2023">

    <objects>
        <object id="data.incentive.fullprotection2023.StaticRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullprotection2023\StaticRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullprotection2023.DataRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullprotection2023\DataRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullprotection2023.StatusRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullprotection2023\StatusRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullprotection2023.ACL" class="data\incentive\fullprotection2023\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.fullprotection2023.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.fullprotection2023.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.fullprotection2023.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
