<?php

namespace data\incentive\raccoltaVincente2025;

/**
 * @orm(source="inc_raccolta_2025_data")
 */

class Data
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm */
    protected $agenzia_id;

    /** @orm */
    protected $polizza;

    /** @orm */
    protected $codiceProdotto;

    /** @orm */
    protected $CF_PIVA;

    /** @orm (type="float")*/
    protected $premio;

    /** @orm */
    protected $tipoPremioQuietanza;

    /** @orm */
    protected $dataContabile;

    /** @orm */
    protected $dataIncasso_check;

    /** @orm (type="integer") */
    protected $esclusa;

    /** @orm (type="integer") */
    protected $premioRidotto;

    /** @orm (type="integer") */
    protected $aliqRidotta;

    /** @orm  (type="string")
     * @validate(regex="/^(04|08)$/")
     */
    protected $parziale;

}
