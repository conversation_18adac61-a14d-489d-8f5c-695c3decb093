<?php

namespace data\incentive\raccoltaVincente2025;

use data\incentive\supernovaRF2024\DataRepository;
use data\incentive\supernovaRF2024\StaticRepository;
use metadigit\core\db\orm\OrmEvent;

class ACL
{
    use \metadigit\core\CoreTrait;

    function onFetch(OrmEvent $Event)
    {
        $criteriaExp = null;

        $repository = $Event->getRepository();

        switch ($_SESSION['AUTH']['UTYPE']) {
            case 'KA':
            case 'AMMINISTRATORE':
                $criteriaExp = "";
                break;
            case 'AREAMGR':
                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}";
                break;
            case 'DISTRICTMGR':
                $criteriaExp = "area,EQ,{$_SESSION['AUTH']['AREA']}|district,EQ,{$_SESSION['AUTH']['DISTRICT']}";
                break;
            case 'AGENTE':
                $criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'];
                break;
            default:
                throw new \Exception('ACCESS DENIED');
        }

        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'] . ': ' . $criteriaExp);

        $Event->criteriaExp($criteriaExp);
    }
}
