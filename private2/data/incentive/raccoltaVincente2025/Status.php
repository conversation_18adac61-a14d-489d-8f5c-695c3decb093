<?php

namespace data\incentive\raccoltaVincente2025;

/**
 * @orm(target="inc_raccolta_2025_status", source="vw_inc_raccolta_2025_status")
 */

class Status
{
    use \metadigit\core\db\orm\EntityTrait;

    /** @orm */
    protected $agenzia_id;

    /** @orm (type="float")*/
    protected $premiPPT;

    /** @orm (type="float")*/
    protected $premiClients;

    /** @orm (type="integer")*/
    protected $pezziOpen;

    /** @orm (type="integer")*/
    protected $nuoviClienti;

    /** @orm (type="float")*/
    protected $incentPPT;

    /** @orm (type="float")*/
    protected $incentClients;

    /** @orm (type="float")*/
    protected $incentOpen;

    /** @orm (type="integer")*/
    protected $otp;

    /** @orm (type="float")*/
    protected $premiOtp;

    /** @orm (type="float")*/
    protected $incentOtp;

    /** @orm (type="float")*/
    protected $incentTotal;

    /** @orm (readonly) */
    protected $localita;

    /** @orm (readonly) */
    protected $nome;

    /** @orm (type="integer", readonly) */
    protected $area;

    /** @orm (readonly) */
    protected $areaName;

    /** @orm (type="integer", readonly) */
    protected $district;

    /** @orm (readonly) */
    protected $districtName;
}
