<?xml version="1.0" encoding="UTF-8"?>
<context namespace="data.incentive.fullbenessere2024">

    <objects>
        <object id="data.incentive.fullbenessere2024.StaticRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullbenessere2024\StaticRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullbenessere2024.DataRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullbenessere2024\DataRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullbenessere2024.StatusRepository" class="metadigit\core\db\orm\Repository">
            <constructor>
                <arg name="class">data\incentive\fullbenessere2024\StatusRepository</arg>
            </constructor>
        </object>
        <object id="data.incentive.fullbenessere2024.ACL" class="data\incentive\fullbenessere2024\ACL"/>
    </objects>

    <events>
        <event name="orm:pre-count">
            <listeners>
                <listener>data.incentive.fullbenessere2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch">
            <listeners>
                <listener>data.incentive.fullbenessere2024.ACL->onFetch</listener>
            </listeners>
        </event>
        <event name="orm:pre-fetch-all">
            <listeners>
                <listener>data.incentive.fullbenessere2024.ACL->onFetch</listener>
            </listeners>
        </event>
    </events>

</context>
