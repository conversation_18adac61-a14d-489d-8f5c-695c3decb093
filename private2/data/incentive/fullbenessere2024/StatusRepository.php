<?php namespace data\incentive\fullbenessere2024;

use metadigit\core\db\orm\Repository;

/**
 * @orm(source="vw_inc_fullbenessere_2024_status", target="inc_fullbenessere_2024_status")
 */

class StatusRepository extends Repository {
    use \metadigit\core\db\orm\EntityTrait;

    /**
     * @orm(primarykey, type="string")
     */
    protected $agenzia_id;

    /**
     * @orm(type="integer")
     */
    protected $pezzi;

    /**
     * @orm(type="float")
     */
    protected $premio;

    /**
     * @orm(type="float")
     */
    protected $incent;

    /**
     * @orm(type="integer", null, readonly)
     */
    protected $etaMedia;

    /**
     * @orm(type="integer", readonly)
     */
    protected $obiettivo;

}
