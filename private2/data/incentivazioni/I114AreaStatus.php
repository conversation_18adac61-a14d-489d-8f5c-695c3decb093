<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_114_aree", target="iniz_114_aree")
 */
class I114AreaStatus {
	use \metadigit\core\db\orm\EntityTrait;

	// District

	/** @orm(type="integer", primarykey) */
	protected $area_id;
	/** @orm(readonly) */
	protected $codice;
	/** @orm(readonly) */
	protected $nome;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivoAuto;
	/** @orm(type="integer") */
	protected $obiettivoHome;

	/** @orm(type="integer") */
	protected $pezziAuto;
	/** @orm(type="integer") */
	protected $pezziHome;

	/** @orm(type="boolean") */
	protected $obiettivoAutoOK;
	/** @orm(type="boolean") */
	protected $obiettivoHomeOK;
	/** @orm(type="float") */
	protected $punteggio;
}
