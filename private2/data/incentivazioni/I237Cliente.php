<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_237_clienti")
 */
class I237Cliente {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;

	// Dati cliente

	/** @orm(primarykey) */
	protected $codiceCliente;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $codFisc;
	/** @orm */
	protected $segmentoPrecedente;
	/** @orm */
	protected $segmento;
	/** @orm */
	protected $sottosegmento;
	/** @orm(type="integer") */
	protected $punteggio;

	/** @orm */
	protected $trasformazione;
}
