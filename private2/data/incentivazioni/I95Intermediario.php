<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_95_intermediari", target="iniz_95_intermediari")
 */
class I95Intermediario {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(readonly) */
	protected $agenzia_id;
	/** @orm(readonly) */
	protected $area;
	/** @orm(readonly) */
	protected $district;

	// User

	/** @orm(primarykey) */
	protected $user_id;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $cognome;
	/** @orm(readonly) */
	protected $codEsazione;

	// status

	/** @orm(type="integer") */
	protected $clienti;
	/** @orm(readonly) */
	protected $createdAt;
}
