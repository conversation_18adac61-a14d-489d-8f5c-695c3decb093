<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_114_districts", target="iniz_114_districts")
 */
class I114DistrictStatus {
	use \metadigit\core\db\orm\EntityTrait;

	// District

	/** @orm(type="integer", primarykey) */
	protected $district_id;
	/** @orm(type="integer", readonly) */
	protected $area_id;
	/** @orm(readonly) */
	protected $nome;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivoAuto;
	/** @orm(type="integer") */
	protected $obiettivoHome;

	/** @orm(type="integer") */
	protected $pezziAuto;
	/** @orm(type="integer") */
	protected $pezziHome;

	/** @orm(type="boolean") */
	protected $obiettivoAutoOK;
	/** @orm(type="boolean") */
	protected $obiettivoHomeOK;
	/** @orm(type="float") */
	protected $punteggio;
}
