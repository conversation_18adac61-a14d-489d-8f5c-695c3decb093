<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_115", target="iniz_115")
 */
class I115Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-06-15';
	const LAST_DAY	= '2015-10-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziPli`) AS pezziPli,
		SUM(`pezziGa150`) AS pezziGa150,
		SUM(`pezziFi4`) AS pezziFi4,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompPli`) AS premiCompPli,
		SUM(`premiCompGa150`) AS premiCompGa150,
		SUM(`premiCompFi4`) AS premiCompFi4,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`obiettivoPliGa150OK`) AS obiettivoPliGa150OK,
		SUM(`obiettivoFi4OK`) AS obiettivoFi4OK,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoPliGa150;
	/** @orm(type="integer") */
	protected $obiettivoFi4;

	/** @orm(type="float") */
	protected $premiPli;
	/** @orm(type="float") */
	protected $premiGa150;
	/** @orm(type="float") */
	protected $premiFi4;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziPli;
	/** @orm(type="integer") */
	protected $pezziGa150;
	/** @orm(type="integer") */
	protected $pezziFi4;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompPli;
	/** @orm(type="float") */
	protected $premiCompGa150;
	/** @orm(type="float") */
	protected $premiCompFi4;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoPliGa150OK;
	/** @orm(type="boolean") */
	protected $obiettivoFi4OK;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
