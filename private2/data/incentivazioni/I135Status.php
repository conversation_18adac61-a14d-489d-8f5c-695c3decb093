<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_135", target="iniz_135")
 */
class I135Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-04-01';
	const LAST_DAY	= '2016-07-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="float") */
	protected $obiettivo1;
	/** @orm(type="float") */
	protected $obiettivo2;

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="float") */
	protected $fraz30giugno;

	/** @orm(type="boolean") */
	protected $obiettivo1OK;
	/** @orm(type="boolean") */
	protected $obiettivo2OK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoBonus;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
