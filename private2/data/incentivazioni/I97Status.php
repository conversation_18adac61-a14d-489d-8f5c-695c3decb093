<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_97", target="iniz_97")
 */
class I97Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-01-07';
	const LAST_DAY	= '2015-02-06';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMulti`) AS premiCompDimMulti,
		SUM(`premiDimMultiUL`) AS premiDimMultiUL,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompDimInv`) AS premiCompDimInv,
		SUM(`premiCompDimFree`) AS premiCompDimFree,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogBonus`) AS importoErogBonus,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDimMulti;
	/** @orm(type="float") */
	protected $premiDimMultiUL;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiDimInv;
	/** @orm(type="float") */
	protected $premiDimFree;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimMulti;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziDimInv;
	/** @orm(type="integer") */
	protected $pezziDimFree;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimMulti;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompDimInv;
	/** @orm(type="float") */
	protected $premiCompDimFree;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonus;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
