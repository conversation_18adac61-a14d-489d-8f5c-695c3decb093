<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_223", target="iniz_223")
 */
class I223Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2019-05-15';
	const LAST_DAY	= '2019-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		
		SUM(`ptfInizioPLINF`) AS ptfInizioPLINF,
		SUM(`ptfFinePLINF`) AS ptfFinePLINF,
		SUM(`premiPLINF`) AS premiPLINF,
		SUM(`pezziPLINF`) AS pezziPLINF,
		SUM(`importoErogPLINF`) AS importoErogPLINF,
		
		SUM(`ptfInizioTCM`) AS ptfInizioTCM,
		SUM(`ptfFineTCM`) AS ptfFineTCM,
		SUM(`premiTCM`) AS premiTCM,
		SUM(`pezziTCM`) AS pezziTCM,
		SUM(`importoErogTCM`) AS importoErogTCM,
		
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $ptfInizioPLINF;
	/** @orm(type="float") */
	protected $ptfInizioTCM;
	/** @orm(type="float") */
	protected $ptfFinePLINF;
	/** @orm(type="float") */
	protected $ptfFineTCM;
	/** @orm(type="integer") */
	protected $obiettivoPezzi;
	/** @orm(type="float") */
	protected $premiPLINF;
	/** @orm(type="float") */
	protected $premiTCM;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziPLINF;
	/** @orm(type="integer") */
	protected $pezziPLINF250;
	/** @orm(type="integer") */
	protected $pezziPLINF350;
	/** @orm(type="integer") */
	protected $pezziPLINF500;
	/** @orm(type="integer") */
	protected $pezziTCM;
	/** @orm(type="integer") */
	protected $pezziTCM250;
	/** @orm(type="integer") */
	protected $pezziTCM300;
	/** @orm(type="integer") */
	protected $pezziTCM500;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoPezziPLINFOK;
	/** @orm(type="boolean") */
	protected $obiettivoPezziTCMOK;
	/** @orm(type="boolean") */
	protected $obiettivoPtfPLINFOK;
	/** @orm(type="boolean") */
	protected $obiettivoPtfTCMOK;

	/** @orm(type="float") */
	protected $importoErogPLINF;
	/** @orm(type="float") */
	protected $importoErogTCM;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
