<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_188", target="iniz_188")
 */
class I188Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-01-01';
	const LAST_DAY	= '2018-01-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(contrattiRossi) AS contrattiRossi,
		SUM(contrattiGialli) AS contrattiGialli,
		SUM(contrattiVerdi) AS contrattiVerdi,
		SUM(contrattiRossiOK) AS contrattiRossiOK,
		SUM(contrattiGialliOK) AS contrattiGialliOK,
		SUM(contrattiVerdiOK) AS contrattiVerdiOK,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $contrattiRossi;
	/** @orm(type="integer") */
	protected $contrattiGialli;
	/** @orm(type="integer") */
	protected $contrattiVerdi;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="integer") */
	protected $contrattiRossiOK;
	/** @orm(type="integer") */
	protected $contrattiGialliOK;
	/** @orm(type="integer") */
	protected $contrattiVerdiOK;

	/** @orm(type="float") */
	protected $premiGialliRecupero;
	/** @orm(type="integer") */
	protected $obiettivoVerdi;
	/** @orm(type="boolean") */
	protected $obiettivoVerdiOK;

	/** @orm(type="float") */
	protected $importoErogRossi;
	/** @orm(type="float") */
	protected $importoErogGialli;
	/** @orm(type="float") */
	protected $importoErogVerdi;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
