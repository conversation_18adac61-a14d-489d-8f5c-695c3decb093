<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_59", target="iniz_59")
 */
class I59Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-05-14';
	const LAST_DAY	= '2012-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`polizzeQAC`) AS polizzeQAC,
		SUM(`premiQAC`) AS premiQAC,
		SUM(`polizzeDOI`) AS polizzeDOI,
		SUM(`premiDOI`) AS premiDOI,
		SUM(`polizzeTOT`) AS polizzeTOT,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="float") */
	protected $premiDOI;
	/** @orm(type="float") */
	protected $premiQAC;
	/** @orm(type="float") */
	protected $premiTOT;

	/** @orm(type="integer") */
	protected $polizzeDOI;
	/** @orm(type="integer") */
	protected $polizzeQAC;
	/** @orm(type="integer") */
	protected $polizzeTOT;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
