<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_234", target="iniz_234")
 */
class I234Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2020-10-01';
	const LAST_DAY	= '2021-01-29';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMultiInv`) AS premiCompDimMultiInv,
		SUM(`premiCompDimMultiFree`) AS premiCompDimMultiFree,
		SUM(`premiCompDimMultiTarget`) AS premiCompDimMultiTarget,
		SUM(`premiCompDimMultiDouble`) AS premiCompDimMultiDouble,
		SUM(`premiCompDimMultiItalia`) AS premiCompDimMultiItalia,
		SUM(`premiCompDimMultiFuturo`) AS premiCompDimMultiFuturo,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompDimSilver`) AS premiCompDimSilver,
		SUM(`premiCompProgOpen`) AS premiCompProgOpen,
		SUM(`premiCompMSpremium`) AS premiCompMSpremium,
		SUM(`premiCompMSdomus`) AS premiCompMSdomus,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogPremiUnici`) AS importoErogPremiUnici,
		SUM(`importoErogTcm`) AS importoErogTcm,
		SUM(`importoErogFondoPensione`) AS importoErogFondoPensione,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDimMultiInv;
	/** @orm(type="float") */
	protected $premiDimMultiFree;
	/** @orm(type="float") */
	protected $premiDimMultiTarget;
	/** @orm(type="float") */
	protected $premiDimMultiDouble;
	/** @orm(type="float") */
	protected $premiDimMultiItalia;
	/** @orm(type="float") */
	protected $premiDimMultiFuturo;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiDimSilver;
	/** @orm(type="float") */
	protected $premiProgOpen;
	/** @orm(type="float") */
	protected $premiMSpremium;
	/** @orm(type="float") */
	protected $premiMSdomus;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimMultiInv;
	/** @orm(type="integer") */
	protected $pezziDimMultiFree;
	/** @orm(type="integer") */
	protected $pezziDimMultiTarget;
	/** @orm(type="integer") */
	protected $pezziDimMultiDouble;
	/** @orm(type="integer") */
	protected $pezziDimMultiItalia;
	/** @orm(type="integer") */
	protected $pezziDimMultiFuturo;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziDimSilver;
	/** @orm(type="integer") */
	protected $pezziProgOpen;
	/** @orm(type="integer") */
	protected $pezziMSpremium;
	/** @orm(type="integer") */
	protected $pezziMSdomus;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimMultiInv;
	/** @orm(type="float") */
	protected $premiCompDimMultiFree;
	/** @orm(type="float") */
	protected $premiCompDimMultiTarget;
	/** @orm(type="float") */
	protected $premiCompDimMultiDouble;
	/** @orm(type="float") */
	protected $premiCompDimMultiItalia;
	/** @orm(type="float") */
	protected $premiCompDimMultiFuturo;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompDimSilver;
	/** @orm(type="float") */
	protected $premiCompProgOpen;
	/** @orm(type="float") */
	protected $premiCompMSpremium;
	/** @orm(type="float") */
	protected $premiCompMSdomus;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoPremiUniciOK;
	/** @orm(type="boolean") */
	protected $obiettivoTcmOK;
	/** @orm(type="boolean") */
	protected $obiettivoFondoPensioneOK;
	/** @orm(type="float") */
	protected $importoErogPremiUnici;
	/** @orm(type="float") */
	protected $importoErogTcm;
	/** @orm(type="float") */
	protected $importoErogFondoPensione;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;

	function notifyMessage() {
		return
			'Hai maturato un incentivo di '.$this->importoErogPremiUnici.' euro per i Premi Unici,'.
			' di '.$this->importoErogFondoPensione.' euro per il Fondo Pensione Aperto'.
			' e di '.$this->importoErogTcm.' per le TCM.'.
			' L’incentivo totale è di '.$this->importoErogTOTALE.' euro.';
	}
}
