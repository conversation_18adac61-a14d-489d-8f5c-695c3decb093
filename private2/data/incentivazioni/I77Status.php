<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_77", target="iniz_77")
 */
class I77Status {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="boolean") */
	protected $active;
	/** @orm(type="integer") */
	protected $soglia1;
	/** @orm(type="integer") */
	protected $soglia2;
	/** @orm(type="integer") */
	protected $pezzi1;
	/** @orm(type="integer") */
	protected $pezzi2;
	/** @orm(type="integer") */
	protected $pezziTOT;
	/** @orm(type="float") */
	protected $importoErog1;
	/** @orm(type="float") */
	protected $importoErog2;
	/** @orm(type="float") */
	protected $importoErogTOTALE;
	/** @orm(type="boolean") */
	protected $fascia1;
	/** @orm(type="boolean") */
	protected $fascia2;
	/** @orm(type="boolean") */
	protected $fascia3;

	// anagrafica

	/** @orm */
//	protected $nome;
	/** @orm */
	protected $cognome;
	/** @orm */
	protected $telefono;
	/** @orm */
	protected $cellulare;
	/** @orm */
	protected $email;

	// system data

	/** @orm(readonly) */
	protected $updatedAt;
}
