<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_71", target="iniz_71")
 */
class I71Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-05-02';
	const LAST_DAY	= '2013-07-26';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDim`) AS premiCompDim,
		SUM(`premiCompDimFree`) AS premiCompDimFree,
		SUM(`premiCompDimPlus`) AS premiCompDimPlus,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDim;
	/** @orm(type="float") */
	protected $premiDimFree;
	/** @orm(type="float") */
	protected $premiDimPlus;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDim;
	/** @orm(type="integer") */
	protected $pezziDimFree;
	/** @orm(type="integer") */
	protected $pezziDimPlus;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDim;
	/** @orm(type="float") */
	protected $premiCompDimFree;
	/** @orm(type="float") */
	protected $premiCompDimPlus;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="float") */
	protected $bonus;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
