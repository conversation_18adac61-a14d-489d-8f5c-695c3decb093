<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_221_clienti")
 */
class I221Cliente {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;

	// Dati cliente

	/** @orm(primarykey) */
	protected $codiceCliente;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $codFisc;
	/** @orm */
	protected $segmento;
	/** @orm */
	protected $sottosegmento;
	/** @orm(type="integer") */
	protected $punteggio;
	/** @orm(type="integer") */
	protected $mese;
}
