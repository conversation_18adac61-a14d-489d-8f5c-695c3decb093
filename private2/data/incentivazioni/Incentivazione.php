<?php
namespace data\incentivazioni;
use	metadigit\core\context\Context;
/**
 * @orm(source="iniz")
 * @orm-criteria(anno="(YEAR(dataInizio) = ?1 OR YEAR(dataFine) = ?2)")
 * @orm-fetch-subset(dashboard="id, url")
 * @orm-fetch-subset(prodotti="id, nome, prodotti")
 */
class Incentivazione {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(type="integer", primarykey) */
	protected $id;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $url;
	/** @orm */
	protected $descriz;
	/** @orm */
	protected $statusAMM;
	/** @orm */
	protected $statusAGT;
	/** @orm */
	protected $statusINT;
	/** @orm */
	protected $prodotti;
	/** @orm(type="boolean") */
	protected $tutelaAuto;
	/** @orm(type="boolean") */
	protected $tutelaBeni;
	/** @orm(type="boolean") */
	protected $tutelaPersona;
	/** @orm */
	protected $gareSpeciali;
	/** @orm */
	protected $c1;
	/** @orm */
	protected $c2;
	/** @orm(type="boolean") */
	protected $datiFinali;
	/** @orm(type="date", readonly) */
	protected $dataInizio;
	/** @orm(type="date", readonly) */
	protected $dataFine;
	/** @orm(type="date", null)
	 * @validate(null, date) */
	protected $dataLiquid;
	/** @orm(type="date", null, readonly) */
	protected $dataUpdate;

	protected function onSave() {
		$this->gareSpeciali = str_replace(' ','', $this->gareSpeciali);
	}

	/**
	 * Return Status class for Iniziativa
	 * @param integer $id Iniziativa ID
	 * @return string class name
	 */
	static function getStatusClass($id) {
		if(in_array($id, array(7, 33, 49)))
			return 'data\incentivazioni\I'.$id.'StatusIntermediario';
		else
			return 'data\incentivazioni\I'.$id.'Status';
	}

	/**
	 * Return Status Repository for Iniziativa
	 * @param integer $id Iniziativa ID
	 * @return \data\incentivazioni\StatusRepository
	 * @throws \metadigit\core\context\ContextException
	 */
	static function getStatusRepository($id) {
		$repositoryID = 'data.incentivazioni.I'.$id.'StatusRepository';
		/** @var  \data\incentivazioni\StatusRepository $Repository */
		$Repository = Context::factory('data.incentivazioni')->get($repositoryID);
		return $Repository;
	}

	static protected $holidaysArray = ['2011-12-08'];

	/**
	 * @param integer $id Iniziativa ID
	 * @param string $date
	 * @return array
	 */
	static function calculateDays($id, $date) {
		$firstDay = constant(self::getStatusClass($id).'::FIRST_DAY');
		$lastDay = constant(self::getStatusClass($id).'::LAST_DAY');
		if($date <  $firstDay) $date = $firstDay;
		$totalDays = self::workingDays(strtotime($firstDay), strtotime($lastDay));
		$currentDay = 1 + $totalDays - self::workingDays(strtotime($date), strtotime($lastDay));
		if($currentDay > $totalDays) $currentDay = $totalDays;
		return array($currentDay, $totalDays);
	}

	static private function workingDays($date2, $date1) {
		$days = 1;
		for ($day = $date2; $day <= $date1; $day += 24 * 3600) {
			$day_of_week = date('N', $day);
			if(!in_array(date('Y-m-d', $day), self::$holidaysArray) && $day_of_week <= 5)
				$days++;
		}
		return $days;
	}
}
