<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_105", target="iniz_105")
 */
class I105Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-01-01';
	const LAST_DAY	= '2015-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo`) AS obiettivo,
		SUM(`clientiArgento`) AS clientiArgento,
		SUM(`clientiOro`) AS clientiOro,
		SUM(`clientiPlatino`) AS clientiPlatino,
		SUM(`clientiTOTALI`) AS clientiTOTALI,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="integer") */
	protected $clientiArgento;
	/** @orm(type="integer") */
	protected $clientiOro;
	/** @orm(type="integer") */
	protected $clientiPlatino;
	/** @orm(type="integer") */
	protected $clientiTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusCompagniaOK;

	/** @orm(type="float") */
	protected $importoErog2015Base;
	/** @orm(type="float") */
	protected $importoErog2015Totale;
	/** @orm(type="float") */
	protected $importoErog2016;
	/** @orm(type="float") */
	protected $importoErog2017;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
