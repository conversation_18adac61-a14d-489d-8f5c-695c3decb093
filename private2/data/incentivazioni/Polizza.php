<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_polizze")
 * @orm-criteria(area="")
 * @orm-criteria(district="")
 * @orm-criteria(agenzia="")
 */
class Polizza {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(type="integer", primarykey) */
	protected $iniziativa_id;

	/** @orm */
	protected $ptf;
	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm */
	protected $codEsazione;
	/** @orm(primarykey) */
	protected $polizza;
	/** @orm */
	protected $delega;
	/** @orm(primarykey) */
	protected $ramo;
	/** @orm(primarykey) */
	protected $garanzia;
	/** @orm */
	protected $codiceProdotto;
	/** @orm */
	protected $famigliaProdotto;
	/** @orm(primarykey) */
	protected $motivo;
	/** @orm */
	protected $nomeCliente;
	/** @orm */
	protected $codiceCliente;
	/** @orm */
	protected $CF_PIVA;

	/** @orm(type="date") */
	protected $dataEstrazione;
	/** @orm(type="date", primarykey) */
	protected $dataEmissione;
	/** @orm(type="date") */
	protected $dataIncasso;
	/** @orm(type="date") */
	protected $dataEffetto;

	/** @orm(type="float") */
	protected $premioAnnuo;
	/** @orm(type="float") */
	protected $premioUnico;
	/** @orm(type="float") */
	protected $versamAgg;
	/** @orm(type="float") */
	protected $deltaPremio;
	/** @orm(type="float") */
	protected $premioComputabile;
	/** @orm */
	protected $codiceCampagna;
	/** @orm */
	protected $codiceConvenzione;
	/** @orm */
	protected $codicePartnership;
	/** @orm */
	protected $tipoCollettiva;
	/** @orm */
	protected $numPolizzaMadre;
	/** @orm */
	protected $modulareVita;
}
