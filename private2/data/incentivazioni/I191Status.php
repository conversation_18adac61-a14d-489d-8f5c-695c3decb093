<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_191", target="iniz_191")
 */
class I191Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-02-01';
	const LAST_DAY	= '2018-05-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo1`) AS obiettivo1,
		SUM(`pezziPlInf`) AS pezziPlInf,
		SUM(`pezziMsPremium`) AS pezziMsPremium,
		SUM(`pezziMsDomus`) AS pezziMsDomus,
		SUM(`pezziMsEasy`) AS pezziMsEasy,
		SUM(`pezziMsElisir`) AS pezziMsElisir,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiTOTALI`) AS premiTOTALI,
		SUM(`premiCompPlInf`) AS premiCompPlInf,
		SUM(`premiCompMsPemium`) AS premiCompMsPemium,
		SUM(`premiCompMsDomus`) AS premiCompMsDomus,
		SUM(`premiCompMsEasy`) AS premiCompMsEasy,
		SUM(`premiCompMsElisir`) AS premiCompMsElisir,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="float") */
	protected $ptfInizio;
	/** @orm(type="float") */
	protected $ptfFine;
	/** @orm(type="integer") */
	protected $obiettivoPezzi;
	/** @orm(type="integer") */
	protected $obiettivo1;
	/** @orm(type="integer") */
	protected $obiettivo2;

	/** @orm(type="integer") */
	protected $pezziPlInf;
	/** @orm(type="integer") */
	protected $pezziMsPremium;
	/** @orm(type="integer") */
	protected $pezziMsDomus;
	/** @orm(type="integer") */
	protected $pezziMsEasy;
	/** @orm(type="integer") */
	protected $pezziMsElisir;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiPlInf;
	/** @orm(type="float") */
	protected $premiMsPremium;
	/** @orm(type="float") */
	protected $premiMsDomus;
	/** @orm(type="float") */
	protected $premiMsEasy;
	/** @orm(type="float") */
	protected $premiMsElisir;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="float") */
	protected $premiCompPlInf;
	/** @orm(type="float") */
	protected $premiCompMsPemium;
	/** @orm(type="float") */
	protected $premiCompMsDomus;
	/** @orm(type="float") */
	protected $premiCompMsEasy;
	/** @orm(type="float") */
	protected $premiCompMsElisir;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivo1OK;
	/** @orm(type="boolean") */
	protected $obiettivo2OK;
	/** @orm(type="integer") */
	protected $perc;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
