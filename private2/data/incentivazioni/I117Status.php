<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_117", target="iniz_117")
 */
class I117Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-09-16';
	const LAST_DAY	= '2015-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMulti`) AS premiCompDimMulti,
		SUM(`premiDimMultiUL`) AS premiDimMultiUL,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`premiCompTcm`) AS premiCompTcm,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogBonusUnitLinked`) AS importoErogBonusUnitLinked,
		SUM(`importoErogBonusTcm`) AS importoErogBonusTcm,
		SUM(`importoErogUnitLinkedProQuota`) AS importoErogUnitLinkedProQuota,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;

	/** @orm(type="float") */
	protected $premiDimMulti;
	/** @orm(type="float") */
	protected $premiDimMultiUL;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="float") */
	protected $premiTcm;

	/** @orm(type="integer") */
	protected $pezziDimMulti;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="integer") */
	protected $pezziTcm;

	/** @orm(type="float") */
	protected $premiCompDimMulti;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="float") */
	protected $premiCompTcm;

	/** @orm(type="integer") */
	protected $numQuietanzeTcm;
	/** @orm(type="float") */
	protected $percQuietanzeTcm;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusUnitLinkedOK;
	/** @orm(type="boolean") */
	protected $bonusTcmOK;
	/** @orm(type="boolean") */
	protected $obiettivoUnitLinkedOK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonusUnitLinked;
	/** @orm(type="float") */
	protected $importoErogBonusTcm;
	/** @orm(type="float") */
	protected $importoErogUnitLinkedProQuota;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
