<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_93", target="iniz_93")
 */
class I93Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-10-01';
	const LAST_DAY	= '2014-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziDIM`) AS pezziDIM,
		SUM(`pezziTCM`) AS pezziTCM,
		SUM(`pezziTUT`) AS pezziTUT,
		SUM(`pezziLTC`) AS pezziLTC,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiDIM`) AS premiDIM,
		SUM(`premiTCM`) AS premiTCM,
		SUM(`premiTUT`) AS premiTUT,
		SUM(`premiLTC`) AS premiLTC,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDIM;
	/** @orm(type="float") */
	protected $premiTCM;
	/** @orm(type="float") */
	protected $premiTUT;
	/** @orm(type="float") */
	protected $premiLTC;
	/** @orm(type="float") */
	protected $premiBonus;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDIM;
	/** @orm(type="integer") */
	protected $pezziTCM;
	/** @orm(type="integer") */
	protected $pezziTUT;
	/** @orm(type="integer") */
	protected $pezziLTC;
	/** @orm(type="integer") */
	protected $pezziBonus;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompBonus;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;
	/** @orm(type="integer") */
	protected $bonusPerc;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonus;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
