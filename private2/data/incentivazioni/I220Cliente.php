<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_220_clienti")
 */
class I220Cliente {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;

	// Dati cliente

	/** @orm(primarykey) */
	protected $codiceCliente;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $codFisc;
	/** @orm */
	protected $segmentoPrecedente;
	/** @orm */
	protected $segmento;
	/** @orm */
	protected $sottosegmento;
	/** @orm(type="integer") */
	protected $punteggio;

	/** @orm(type="integer") */
	protected $pezzi;

	/** @orm(type="integer") */
	protected $pezziF1_MyP;
	/** @orm(type="integer") */
	protected $pezziF1_CSC;
	/** @orm(type="integer") */
	protected $pezziF1_MS;

	/** @orm(type="integer") */
	protected $pezziF2_CSC;
	/** @orm(type="integer") */
	protected $pezziF2_MyP;
	/** @orm(type="integer") */
	protected $pezziF2_MS;

	/** @orm(type="integer") */
	protected $pezziF3_CSC;
	/** @orm(type="integer") */
	protected $pezziF3_PLI;
	/** @orm(type="integer") */
	protected $pezziF3_MS;

	/** @orm(type="integer") */
	protected $bonusPG;
}
