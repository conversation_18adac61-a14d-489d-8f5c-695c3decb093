<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_62", target="iniz_62")
 */
class I62Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-05-28';
	const LAST_DAY	= '2012-07-27';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziMSa`) AS pezziMSa,
		SUM(`pezziMSu`) AS pezziMSu,
		SUM(`pezziMS`) AS pezziMS,
		SUM(`pezziDIMPT`) AS pezziDIMPT,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompMS`) AS premiCompMS,
		SUM(`premiCompDIMPT`) AS premiCompDIMPT,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiMSa;
	/** @orm(type="float") */
	protected $premiMSu;
	/** @orm(type="float") */
	protected $premiMS;
	/** @orm(type="float") */
	protected $premiDIMPT;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziMSa;
	/** @orm(type="integer") */
	protected $pezziMSu;
	/** @orm(type="integer") */
	protected $pezziMS;
	/** @orm(type="integer") */
	protected $pezziDIMPT;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompMS;
	/** @orm(type="float") */
	protected $premiCompDIMPT;
	/** @orm(type="float") */
	protected $premiCompTOTALI;


	/** @orm(type="boolean") */
	protected $obiettivoPremi;
	/** @orm(type="boolean") */
	protected $obiettivoPezzi;
	/** @orm(type="boolean") */
	protected $obiettivo;
	/** @orm(type="boolean") */
	protected $bonus;

	/** @orm(type="float") */
	protected $importoErogabileMS;
	/** @orm(type="float") */
	protected $importoErogabileDIMPT;
	/** @orm(type="float") */
	protected $importoErogabileBonus;
	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
