<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_212", target="iniz_212")
 */
class I212Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-07-16';
	const LAST_DAY	= '2018-09-28';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompMSpremium`) AS premiCompMSpremium,
		SUM(`premiCompMSdomus`) AS premiCompMSdomus,
		SUM(`premiCompMSeasy`) AS premiCompMSeasy,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiMSpremium;
	/** @orm(type="float") */
	protected $premiMSdomus;
	/** @orm(type="float") */
	protected $premiMSeasy;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziMSpremium;
	/** @orm(type="integer") */
	protected $pezziMSdomus;
	/** @orm(type="integer") */
	protected $pezziMSeasy;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompMSpremium;
	/** @orm(type="float") */
	protected $premiCompMSdomus;
	/** @orm(type="float") */
	protected $premiCompMSeasy;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="integer") */
	protected $pezziMSpd300;
	/** @orm(type="integer") */
	protected $pezziMSpd500;
	/** @orm(type="integer") */
	protected $pezziMSpd750;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
