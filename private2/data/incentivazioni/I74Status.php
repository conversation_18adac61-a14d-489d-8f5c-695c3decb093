<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_74", target="iniz_74")
 */
class I74Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-09-01';
	const LAST_DAY	= '2013-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompMS`) AS premiCompMS,
		SUM(`premiCompDimStu`) AS premiCompDimStu,
		SUM(`premiCompDimRis`) AS premiCompDimRis,
		SUM(`premiCompDimPro`) AS premiCompDimPro,
		SUM(`premiCompDimTut`) AS premiCompDimTut,
		SUM(`premiCompDIM`) AS premiCompDIM,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiMS;
	/** @orm(type="float") */
	protected $premiDimStu;
	/** @orm(type="float") */
	protected $premiDimRis;
	/** @orm(type="float") */
	protected $premiDimPro;
	/** @orm(type="float") */
	protected $premiDimTut;
	/** @orm(type="float") */
	protected $premiDIM;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziMS;
	/** @orm(type="integer") */
	protected $pezziDimStu;
	/** @orm(type="integer") */
	protected $pezziDimRis;
	/** @orm(type="integer") */
	protected $pezziDimPro;
	/** @orm(type="integer") */
	protected $pezziDimTut;
	/** @orm(type="integer") */
	protected $pezziDIM;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompMS;
	/** @orm(type="float") */
	protected $premiCompDimStu;
	/** @orm(type="float") */
	protected $premiCompDimRis;
	/** @orm(type="float") */
	protected $premiCompDimPro;
	/** @orm(type="float") */
	protected $premiCompDimTut;
	/** @orm(type="float") */
	protected $premiCompDIM;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;
	/** @orm(type="integer") */
	protected $bonusPerc;

	/** @orm(type="float") */
	protected $importoErogMS;
	/** @orm(type="float") */
	protected $importoErogDIM;
	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
