<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_179_clienti")
 */
class I179Cliente {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm */
	protected $codEsazione;

	// Dati cliente

	/** @orm */
	protected $nome;
	/** @orm(primarykey) */
	protected $codFisc;
	/** @orm */
	protected $segmento;
	/** @orm */
	protected $sottosegmento;
	/** @orm(type="integer") */
	protected $punteggio;
	/** @orm(type="integer") */
	protected $mese;
}
