<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_118", target="iniz_118")
 */
class I118Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-10-01';
	const LAST_DAY	= '2015-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompFullAttivita`) AS premiCompFullAttivita,
		SUM(`premiCompPlusImpresa`) AS premiCompPlusImpresa,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`sostituzFullAttivita`) AS sostituzFullAttivita,
		SUM(`sostituzPlusImpresa`) AS sostituzPlusImpresa,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogSostituz`) AS importoErogSostituz,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;

	/** @orm(type="float") */
	protected $premiFullAttivita;
	/** @orm(type="float") */
	protected $premiPlusImpresa;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziFullAttivita;
	/** @orm(type="integer") */
	protected $pezziPlusImpresa;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompFullAttivita;
	/** @orm(type="float") */
	protected $premiCompPlusImpresa;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(type="float") */
	protected $importoErogFullAttivita;
	/** @orm(type="float") */
	protected $importoErogPlusImpresa;
	/** @orm(type="float") */
	protected $importoErog;

	/** @orm(type="float") */
	protected $sostituzFullAttivita;
	/** @orm(type="float") */
	protected $sostituzPlusImpresa;
	/** @orm(type="float") */
	protected $importoErogSostituz;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
