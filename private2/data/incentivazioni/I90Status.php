<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_90", target="iniz_90")
 */
class I90Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-05-12';
	const LAST_DAY	= '2014-07-11';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimCap`) AS premiCompDimCap,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompDimInv`) AS premiCompDimInv,
		SUM(`premiCompDimFreeInv`) AS premiCompDimFreeInv,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogBonus`) AS importoErogBonus,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDimCap;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiDimInv;
	/** @orm(type="float") */
	protected $premiDimFreeInv;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimCap;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziDimInv;
	/** @orm(type="integer") */
	protected $pezziDimFreeInv;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimCap;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompDimInv;
	/** @orm(type="float") */
	protected $premiCompDimFreeInv;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonus;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
