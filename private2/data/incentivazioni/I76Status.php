<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_76", target="iniz_76")
 */
class I76Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-09-20';
	const LAST_DAY	= '2013-10-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompAN`) AS premiCompAN,
		SUM(`premiCompVA`) AS premiCompVA,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiAN;
	/** @orm(type="float") */
	protected $premiVA;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziAN;
	/** @orm(type="integer") */
	protected $pezziVA;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompAN;
	/** @orm(type="float") */
	protected $premiCompVA;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="float") */
	protected $bonusPerc;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
