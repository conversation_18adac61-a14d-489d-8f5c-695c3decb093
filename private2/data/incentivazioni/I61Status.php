<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_61", target="iniz_61")
 */
class I61Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-10-29';
	const LAST_DAY	= '2012-12-18';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`premiCompDIMINV`) AS premiCompDIMINV,
		SUM(`premiCompCAPITAL`) AS premiCompCAPITAL,
		SUM(`premiCompDIMPQ`) AS premiCompDIMPQ,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogDIMINV`) AS importoErogDIMINV,
		SUM(`importoErogCAPITAL`) AS importoErogCAPITAL,
		SUM(`importoErogDIMPQ`) AS importoErogDIMPQ,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDIMINV;
	/** @orm(type="float") */
	protected $premiCAPITAL;
	/** @orm(type="float") */
	protected $premiDIMPQ;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDIMINV;
	/** @orm(type="integer") */
	protected $pezziCAPITAL;
	/** @orm(type="integer") */
	protected $pezziDIMPQ;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDIMINV;
	/** @orm(type="float") */
	protected $premiCompCAPITAL;
	/** @orm(type="float") */
	protected $premiCompDIMPQ;
	/** @orm(type="float") */
	protected $premiCompTOTALI;


	/** @orm(type="integer") */
	protected $obiettivo;
	/** @orm(type="integer") */
	protected $percObiettivo;
	/** @orm(type="float") */
	protected $bonusDIMINV;
	/** @orm(type="float") */
	protected $bonusCAPITAL;
	/** @orm(type="float") */
	protected $bonusDIMPQ;
	/** @orm(type="float") */
	protected $superBonus;

	/** @orm(type="float") */
	protected $importoErogDIMINV;
	/** @orm(type="float") */
	protected $importoErogCAPITAL;
	/** @orm(type="float") */
	protected $importoErogDIMPQ;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
