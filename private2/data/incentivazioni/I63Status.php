<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_63", target="iniz_63")
 */
class I63Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-11-12';
	const LAST_DAY	= '2013-03-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziMS`) AS pezziMS,
		SUM(`premiMS`) AS premiMS,
		SUM(`premiCompMS`) AS premiCompMS,
		SUM(`pezziPLINF`) AS pezziPLINF,
		SUM(`premiPLINF`) AS premiPLINF,
		SUM(`premiCompPLINF`) AS premiCompPLINF,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiTOTALI`) AS premiTOTALI,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`numPacchetti`) AS numPacchetti,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="float") */
	protected $premiMS;
	/** @orm(type="float") */
	protected $premiPLINF;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziMS;
	/** @orm(type="integer") */
	protected $pezziPLINF;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompMS;
	/** @orm(type="float") */
	protected $premiCompPLINF;
	/** @orm(type="float") */
	protected $premiCompTOTALI;


	/** @orm(type="integer") */
	protected $numPacchetti;
	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
