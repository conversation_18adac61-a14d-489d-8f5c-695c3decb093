<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_cache")
 * @orm-criteria(anno="(YEAR(dataInizio) = ?1 OR YEAR(dataFine) = ?2)")
 * @orm-fetch-subset(dashboard="id, url, progress1, progress2, progress3")
 */
class IncentivAgzCache {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(type="integer", primarykey) */
	protected $id;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $url;
	/** @orm */
	protected $descriz;
	/** @orm */
	protected $statusAMM;
	/** @orm */
	protected $statusAGT;
	/** @orm */
	protected $statusINT;
	/** @orm */
	protected $prodotti;
	/** @orm(type="boolean") */
	protected $tutelaAuto;
	/** @orm(type="boolean") */
	protected $tutelaBeni;
	/** @orm(type="boolean") */
	protected $tutelaPersona;
	/** @orm */
	protected $gareSpeciali;
	/** @orm */
	protected $c1;
	/** @orm */
	protected $c2;
	/** @orm(type="boolean") */
	protected $datiFinali;
	/** @orm(type="date", readonly) */
	protected $dataInizio;
	/** @orm(type="date", readonly) */
	protected $dataFine;
	/** @orm(type="date", null)
	 * @validate(null, date) */
	protected $dataLiquid;
	/** @orm(type="date", null, readonly) */
	protected $dataUpdate;


	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", null) */
	protected $progress1;
	/** @orm(type="integer", null) */
	protected $progress2;
	/** @orm(type="integer", null) */
	protected $progress3;
}
