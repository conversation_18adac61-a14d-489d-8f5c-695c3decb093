<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_209", target="iniz_209")
 */
class I209Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-09-01';
	const LAST_DAY	= '2018-09-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(contrattiRossi) AS contrattiRossi,
		SUM(contrattiGialli) AS contrattiGialli,
		SUM(contrattiVerdi) AS contrattiVerdi,
		SUM(contrattiRossiOK) AS contrattiRossiOK,
		SUM(contrattiGialli2OK) AS contrattiGialli2OK,
		SUM(contrattiGialli4OK) AS contrattiGialli4OK,
		SUM(contrattiVerdiOK) AS contrattiVerdiOK,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $contrattiRossi;
	/** @orm(type="integer") */
	protected $contrattiGialli;
	/** @orm(type="integer") */
	protected $contrattiVerdi;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="integer") */
	protected $contrattiRossiOK;
	/** @orm(type="integer") */
	protected $contrattiGialli2OK;
	/** @orm(type="integer") */
	protected $contrattiGialli4OK;
	/** @orm(type="integer") */
	protected $contrattiVerdiOK;

	/** @orm(type="float") */
	protected $premiGialliRecupero2;
	/** @orm(type="float") */
	protected $premiGialliRecupero4;
	/** @orm(type="boolean") */
	protected $obiettivoVerdiOK;

	/** @orm(type="float") */
	protected $importoErogRossi;
	/** @orm(type="float") */
	protected $importoErogGialli;
	/** @orm(type="float") */
	protected $importoErogVerdi;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
