<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_70", target="iniz_70")
 */
class I70Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-01-01';
	const LAST_DAY	= '2013-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo`) AS obiettivo,
		SUM(`clientiARGENTO`) AS clientiARGENTO,
		SUM(`clientiORO`) AS clientiORO,
		SUM(`clientiPLATINO`) AS clientiPLATINO,
		SUM(`clientiTotali`) AS clientiTotali,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="integer") */
	protected $clientiARGENTO;
	/** @orm(type="integer") */
	protected $clientiORO;
	/** @orm(type="integer") */
	protected $clientiPLATINO;
	/** @orm(type="integer") */
	protected $clientiTotali;

	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
