<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_215", target="iniz_215")
 */
class I215Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-11-19';
	const LAST_DAY	= '2019-03-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMultiInv`) AS premiCompDimMultiInv,
		SUM(`premiCompDimMultiFree`) AS premiCompDimMultiFree,
		SUM(`premiCompDimMultiTarget`) AS premiCompDimMultiTarget,
		SUM(`premiCompDimMultiDouble`) AS premiCompDimMultiDouble,
		SUM(`premiCompDimMultiItalia`) AS premiCompDimMultiItalia,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDimMultiInv;
	/** @orm(type="float") */
	protected $premiDimMultiFree;
	/** @orm(type="float") */
	protected $premiDimMultiTarget;
	/** @orm(type="float") */
	protected $premiDimMultiDouble;
	/** @orm(type="float") */
	protected $premiDimMultiItalia;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimMultiInv;
	/** @orm(type="integer") */
	protected $pezziDimMultiFree;
	/** @orm(type="integer") */
	protected $pezziDimMultiTarget;
	/** @orm(type="integer") */
	protected $pezziDimMultiDouble;
	/** @orm(type="integer") */
	protected $pezziDimMultiItalia;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimMultiInv;
	/** @orm(type="float") */
	protected $premiCompDimMultiFree;
	/** @orm(type="float") */
	protected $premiCompDimMultiTarget;
	/** @orm(type="float") */
	protected $premiCompDimMultiDouble;
	/** @orm(type="float") */
	protected $premiCompDimMultiItalia;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;

	function notifyMessage() {
		return
			'produzione complessiva € '.$this->premiTOTALI.','.
			' premi in target € '.$this->premiCompTOTALI.', '.
			' incentivazione € '.$this->importoErogTOTALE;
	}
}
