<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_96_intermediari", target="iniz_96_intermediari")
 */
class I96Intermediario {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(readonly) */
	protected $agenzia_id;
	/** @orm(readonly) */
	protected $area;
	/** @orm(readonly) */
	protected $district;

	// User

	/** @orm(primarykey) */
	protected $user_id;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $cognome;
	/** @orm(readonly) */
	protected $codEsazione;

	// status

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="float") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="integer") */
	protected $punti;
	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(readonly) */
	protected $createdAt;
}
