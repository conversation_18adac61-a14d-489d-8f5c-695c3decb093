<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_133", target="iniz_133")
 */
class I133Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-02-01';
	const LAST_DAY	= '2016-04-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo`) AS obiettivo,
		SUM(`pezziAN`) AS pezziAN,
		SUM(`pezziQACconHB`) AS pezziQACconHB,
		SUM(`pezziQACnoHB`) AS pezziQACnoHB,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiTOTALI`) AS premiTOTALI,
		SUM(`premiCompQACconHB`) AS premiCompQACconHB,
		SUM(`premiCompQACnoHB`) AS premiCompQACnoHB,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="integer") */
	protected $pezziAN;
	/** @orm(type="integer") */
	protected $pezziQACconHB;
	/** @orm(type="integer") */
	protected $pezziQACnoHB;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiQACnoHB;
	/** @orm(type="float") */
	protected $premiQACconHB;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="float") */
	protected $premiCompQACnoHB;
	/** @orm(type="float") */
	protected $premiCompQACconHB;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="integer") */
	protected $perc;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
