<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_146", target="iniz_146")
 */
class I146Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-07-01';
	const LAST_DAY	= '2016-09-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(obiettivoPezzi) AS obiettivoPezzi,
		SUM(obiettivoMyAngel) AS obiettivoMyAngel,
		SUM(clientiMyAngel) AS clientiMyAngel,
		SUM(obiettivoOK) AS obiettivoOK,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivoPezzi;
	/** @orm(type="integer") */
	protected $obiettivoMyAngel;

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="integer") */
	protected $clientiMyAngel;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="integer") */
	protected $percPezzi;
	/** @orm(type="integer") */
	protected $percMyAngel;

	/** @orm(type="integer") */
	protected $euroPerPezzo;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
