<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_196", target="iniz_196")
 */
class I196Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-03-01';
	const LAST_DAY	= '2018-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(clientiARGENTO) AS clientiARGENTO,
		SUM(clientiORO) AS clientiORO,
		SUM(clientiPLATINO) AS clientiPLATINO,
		SUM(clientiTotali) AS clientiTotali,
		SUM(clientiMyAngel) AS clientiMyAngel,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="integer") */
	protected $clientiARGENTO;
	/** @orm(type="integer") */
	protected $clientiORO;
	/** @orm(type="integer") */
	protected $clientiPLATINO;
	/** @orm(type="integer") */
	protected $clientiARGENTO_F1;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F1_1P;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F1_2P;
	/** @orm(type="integer") */
	protected $clientiARGENTO_F2;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F2_1P;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F2_2P;
	/** @orm(type="integer") */
	protected $clientiARGENTO_F3;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F3_1P;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_F3_2P;
	/** @orm(type="integer") */
	protected $clientiTotali;
	/** @orm(type="integer") */
	protected $clientiMyAngel;
	/** @orm(type="integer") */
	protected $percMyAngel;

	/** @orm(type="float") */
	protected $importoBase;
	/** @orm(type="float") */
	protected $importoBonusF1;
	/** @orm(type="float") */
	protected $importoBonusF2;
	/** @orm(type="float") */
	protected $importoBonusF3;
	/** @orm(type="float") */
	protected $importoMyAngel;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
