<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_261", target="iniz_261")
 */
class I261Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2021-04-01';
	const LAST_DAY	= '2021-07-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziOver280`) AS pezziOver280,
		SUM(`pezziOver400`) AS pezziOver400,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiOver280`) AS premiOver280,
		SUM(`premiOver400`) AS premiOver400,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="float") */
	protected $ptfInizio;
	/** @orm(type="float") */
	protected $ptfFine;
	/** @orm(type="integer") */
	protected $obiettivoPezzi;
	/** @orm(type="float") */
	protected $premiOver280;
	/** @orm(type="float") */
	protected $premiOver400;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziOver280;
	/** @orm(type="integer") */
	protected $pezziOver400;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="float") */
	protected $incrementoPtf;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $obiettivoDoppioOK;
	/** @orm(type="boolean") */
	protected $incrementoPtfOK;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
