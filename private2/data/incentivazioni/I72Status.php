<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_72", target="iniz_72")
 */
class I72Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-06-01';
	const LAST_DAY	= '2013-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompINF`) AS premiCompINF,
		SUM(`premiCompTUT`) AS premiCompTUT,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`bonusOK`) AS bonusOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $ptf2012;
	/** @orm(type="float") */
	protected $ptf2013;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="float") */
	protected $premiINF;
	/** @orm(type="float") */
	protected $premiTUT;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziINF;
	/** @orm(type="integer") */
	protected $pezziTUT;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompINF;
	/** @orm(type="float") */
	protected $premiCompTUT;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErogINF;
	/** @orm(type="float") */
	protected $importoErogTUT;
	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
