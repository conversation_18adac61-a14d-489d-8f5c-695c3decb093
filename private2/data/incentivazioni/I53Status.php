<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_53", target="iniz_53")
 */
class I53Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-01-01';
	const LAST_DAY	= '2012-06-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`polizzeF1_MS`) AS polizzeF1,
		SUM(`premiF1_MS`) AS premiF1,
		SUM(`importoErogabileF1`) AS importoErogabileF1,
		SUM(`polizzeF2_MS`) + SUM(`polizzeF2_DIM`) AS polizzeF2,
		SUM(`premiF2_MS`) + SUM(`premiF2_DIM`) AS premiF2,
		SUM(`importoErogabileF2`) AS importoErogabileF2,
		SUM(`obiettivoBonus`) AS obiettivoBonus,
		SUM(`importoBonus`) AS importoBonus
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiF1_MS;
	/** @orm(type="float") */
	protected $premiF2_MS;
	/** @orm(type="float") */
	protected $premiF2_DIM;

	/** @orm(type="integer") */
	protected $polizzeF1_MS;
	/** @orm(type="integer") */
	protected $polizzeF2_MS;
	/** @orm(type="integer") */
	protected $polizzeF2_DIM;

	/** @orm(type="boolean") */
	protected $obiettivoF1;
	/** @orm(type="boolean") */
	protected $obiettivoF2;
	/** @orm(type="boolean") */
	protected $obiettivoBonus;

	/** @orm(type="float") */
	protected $importoErogabileF1;
	/** @orm(type="float") */
	protected $importoErogabileF2_MS;
	/** @orm(type="float") */
	protected $importoErogabileF2_DIM;
	/** @orm(type="float") */
	protected $importoErogabileF2;
	/** @orm(type="float") */
	protected $importoBonus;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
