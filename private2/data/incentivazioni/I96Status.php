<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_96", target="iniz_96")
 */
class I96Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-12-01';
	const LAST_DAY	= '2015-03-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,

		SUM(`premiQuiAbito`) AS premiQuiAbito,
		SUM(`premiPluriAttiva`) AS premiPluriAttiva,
		SUM(`premiQuiSicura`) AS premiQuiSicura,
		SUM(`premiDinamicaPlusAlbergo`) AS premiDinamicaPlusAlbergo,
		SUM(`premiDinamicaPlusImpresa`) AS premiDinamicaPlusImpresa,
		SUM(`premiDinamicaPlusCommercio`) AS premiDinamicaPlusCommercio,
		SUM(`premiDIM`) AS premiDIM,
		SUM(`premiLTC`) AS premiLTC,
		SUM(`premiPIP`) AS premiPIP,
		SUM(`premiTCM`) AS premiTCM,
		SUM(`premiTUT`) AS premiTUT,
		SUM(`premiTOTALI`) AS premiTOTALI,

		SUM(`pezziQuiAbito`) AS pezziQuiAbito,
		SUM(`pezziPluriAttiva`) AS pezziPluriAttiva,
		SUM(`pezziQuiSicura`) AS pezziQuiSicura,
		SUM(`pezziDinamicaPlusAlbergo`) AS pezziDinamicaPlusAlbergo,
		SUM(`pezziDinamicaPlusImpresa`) AS pezziDinamicaPlusImpresa,
		SUM(`pezziDinamicaPlusCommercio`) AS pezziDinamicaPlusCommercio,
		SUM(`pezziDIM`) AS pezziDIM,
		SUM(`pezziLTC`) AS pezziLTC,
		SUM(`pezziPIP`) AS pezziPIP,
		SUM(`pezziTCM`) AS pezziTCM,
		SUM(`pezziTUT`) AS pezziTUT,
		SUM(`pezziTOTALI`) AS pezziTOTALI
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiQuiAbito;
	/** @orm(type="float") */
	protected $premiPluriAttiva;
	/** @orm(type="float") */
	protected $premiQuiSicura;
	/** @orm(type="float") */
	protected $premiDinamicaPlusAlbergo;
	/** @orm(type="float") */
	protected $premiDinamicaPlusImpresa;
	/** @orm(type="float") */
	protected $premiDinamicaPlusCommercio;
	/** @orm(type="float") */
	protected $premiDIM;
	/** @orm(type="float") */
	protected $premiLTC;
	/** @orm(type="float") */
	protected $premiPIP;
	/** @orm(type="float") */
	protected $premiTCM;
	/** @orm(type="float") */
	protected $premiTUT;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziQuiAbito;
	/** @orm(type="integer") */
	protected $pezziPluriAttiva;
	/** @orm(type="integer") */
	protected $pezziQuiSicura;
	/** @orm(type="integer") */
	protected $pezziDinamicaPlusAlbergo;
	/** @orm(type="integer") */
	protected $pezziDinamicaPlusImpresa;
	/** @orm(type="integer") */
	protected $pezziDinamicaPlusCommercio;
	/** @orm(type="integer") */
	protected $pezziDIM;
	/** @orm(type="integer") */
	protected $pezziLTC;
	/** @orm(type="integer") */
	protected $pezziPIP;
	/** @orm(type="integer") */
	protected $pezziTCM;
	/** @orm(type="integer") */
	protected $pezziTUT;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
