<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_155", target="iniz_155")
 */
class I155Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-12-01';
	const LAST_DAY	= '2016-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo1`) AS obiettivo1,
		SUM(`pezzi2015`) AS pezzi2015,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premi2015`) AS premi2015,
		SUM(`premiTOTALI`) AS premiTOTALI,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="float") */
	protected $obiettivo1;
	/** @orm(type="float") */
	protected $obiettivo2;
	/** @orm(type="float") */
	protected $obiettivo3;
	/** @orm(type="integer") */
	protected $obiettivoRinnovo;


	/** @orm(type="integer") */
	protected $pezzi2015;
	/** @orm(type="float") */
	protected $premi2015;

	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="float") */
	protected $variazPremioMedio;
	/** @orm(type="boolean") */
	protected $obiettivo1OK;
	/** @orm(type="boolean") */
	protected $obiettivo2OK;
	/** @orm(type="boolean") */
	protected $obiettivo3OK;
	/** @orm(type="boolean") */
	protected $obiettivoRinnovoOK;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
