<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_182", target="iniz_182")
 */
class I182Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2017-10-01';
	const LAST_DAY	= '2017-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
