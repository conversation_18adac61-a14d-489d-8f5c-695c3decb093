<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_84", target="iniz_84")
 */
class I84Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-05-01';
	const LAST_DAY	= '2014-10-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziCFS`) AS pezziCFS,
		SUM(`pezziQAC`) AS pezziQAC,
		SUM(`premiCompCFS`) AS premiCompCFS,
		SUM(`premiCompQAC`) AS premiCompQAC,
		SUM(`obiettivoCfsOK`) AS obiettivoCfsOK,
		SUM(`obiettivoQacOK`) AS obiettivoQacOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoCFS;
	/** @orm(type="integer") */
	protected $obiettivoQAC;

	/** @orm(type="float") */
	protected $premiCFS;
	/** @orm(type="float") */
	protected $premiQAC;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziCFS;
	/** @orm(type="integer") */
	protected $pezziQAC;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompCFS;
	/** @orm(type="float") */
	protected $premiCompQAC;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoCfsOK;
	/** @orm(type="boolean") */
	protected $obiettivoQacOK;
	/** @orm(type="integer") */
	protected $percIncent;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
