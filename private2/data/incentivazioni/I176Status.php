<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_176", target="iniz_176")
 */
class I176Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2017-06-01';
	const LAST_DAY	= '2017-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(obiettivo) AS obiettivo,
		SUM(clientiARGENTO) AS clientiARGENTO,
		SUM(clientiORO) AS clientiORO,
		SUM(clientiPLATINO) AS clientiPLATINO,
		SUM(clientiTotali) AS clientiTotali,
		SUM(clientiMyAngel) AS clientiMyAngel,
		SUM(obiettivoOK) AS obiettivoOK,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="integer") */
	protected $clientiARGENTO;
	/** @orm(type="integer") */
	protected $clientiORO;
	/** @orm(type="integer") */
	protected $clientiPLATINO;
	/** @orm(type="integer") */
	protected $clientiARGENTO_QoP;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_QoP;
	/** @orm(type="integer") */
	protected $clientiOROPLAT_QeP;
	/** @orm(type="integer") */
	protected $clientiTotali;
	/** @orm(type="integer") */
	protected $clientiMyAngel;
	/** @orm(type="integer") */
	protected $percMyAngel;

	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(type="float") */
	protected $importoBase;
	/** @orm(type="float") */
	protected $importoMyAngel;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
