<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_127", target="iniz_127")
 */
class I127Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-02-08';
	const LAST_DAY	= '2016-07-29';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompPremium`) AS premiCompPremium,
		SUM(`premiCompDomus`) AS premiCompDomus,
		SUM(`premiCompEasy`) AS premiCompEasy,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;

	/** @orm(type="float") */
	protected $premiPremium;
	/** @orm(type="float") */
	protected $premiDomus;
	/** @orm(type="float") */
	protected $premiEasy;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziPremium;
	/** @orm(type="integer") */
	protected $pezziDomus;
	/** @orm(type="integer") */
	protected $pezziEasy;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompPremium;
	/** @orm(type="float") */
	protected $premiCompDomus;
	/** @orm(type="float") */
	protected $premiCompEasy;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivo1OK;
	/** @orm(type="boolean") */
	protected $obiettivo2OK;
	/** @orm(type="boolean") */
	protected $obiettivo3OK;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
