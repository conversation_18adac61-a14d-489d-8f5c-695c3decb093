<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_254", target="iniz_254")
 */
class I254Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2021-01-01';
	const LAST_DAY	= '2021-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(clientiMONOAUTO_ARGENTO) AS clientiMONOAUTO_ARGENTO,
		SUM(clientiMONOAUTO_OROPLAT) AS clientiMONOAUTO_OROPLAT,
		SUM(clientiARGENTO_OROPLAT) AS clientiARGENTO_OROPLAT,
		SUM(clientiMONOAREA_ARGENTO_CON_AUTO) AS clientiMONOAREA_ARGENTO_CON_AUTO,
		SUM(clientiMONOAREA_ARGENTO_NO_AUTO) AS clientiMONOAREA_ARGENTO_NO_AUTO,
		SUM(clientiMONOAREA_OROPLAT) AS clientiMONOAREA_OROPLAT,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivoMONOAUTO;
	/** @orm(type="integer") */
	protected $obiettivoMONOAREA;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="integer") */
	protected $clientiMONOAUTO_ARGENTO;
	/** @orm(type="integer") */
	protected $clientiMONOAUTO_OROPLAT;
	/** @orm(type="integer") */
	protected $clientiARGENTO_OROPLAT;
	/** @orm(type="integer") */
	protected $clientiMONOAREA_ARGENTO_CON_AUTO;
	/** @orm(type="integer") */
	protected $clientiMONOAREA_ARGENTO_NO_AUTO;
	/** @orm(type="integer") */
	protected $clientiMONOAREA_OROPLAT;

	/** @orm(type="boolean") */
	protected $bonusObiMinOK;
	/** @orm(type="boolean") */
	protected $bonusMONOAUTO_raddoppioOK;
	/** @orm(type="boolean") */
	protected $bonusMONOAREA_raddoppioOK;

	/** @orm(type="float") */
	protected $importoMONOAUTO;
	/** @orm(type="float") */
	protected $importoMONOAREA;
	/** @orm(type="float") */
	protected $importoMONOAUTO_bonusObjMin;
	/** @orm(type="float") */
	protected $importoMONOAREA_bonusObjMin;
	/** @orm(type="float") */
	protected $importoMONOAUTO_bonusRaddoppio;
	/** @orm(type="float") */
	protected $importoMONOAREA_bonusRaddoppio;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
