<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_226", target="iniz_226")
 */
class I226Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2019-11-01';
	const LAST_DAY	= '2020-02-29';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTV) AS pezziTV,
		SUM(pezziTOTALI) AS pezziTOTALI
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $ptfPercTV;
	/** @orm(type="integer") */
	protected $obiettivoAN;
	/** @orm(type="float") */
	protected $obiettivoPercTV;

	/** @orm(type="integer") */
	protected $pezziTV;
	/** @orm(type="integer") */
	protected $percTV;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="boolean") */
	protected $obiettivoOK;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
	function notifyMessage() {
		return
			'Hai totalizzato '.$this->pezziTOTALI.' nuovi contratti su un obiettivo di '.$this->obiettivoAN.'.'.
			' Le polizze in Target Verde sono il '.$this->percTV.'% su un obiettivo del '.$this->obiettivoPercTV.'%.';
	}
}
