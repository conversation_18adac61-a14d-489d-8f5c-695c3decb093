<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_103", target="iniz_103")
 */
class I103Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-02-16';
	const LAST_DAY	= '2015-04-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMulti`) AS premiCompDimMulti,
		SUM(`premiDimMultiUL`) AS premiDimMultiUL,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompDimCap`) AS premiCompDimCap,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`premiCompSuperBonusTcm`) AS premiCompSuperBonusTcm,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogBonus`) AS importoErogBonus,
		SUM(`importoErogSuperBonusTcm`) AS importoErogSuperBonusTcm,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;

	/** @orm(type="float") */
	protected $premiDimMulti;
	/** @orm(type="float") */
	protected $premiDimMultiUL;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiDimCap;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="float") */
	protected $premiSuperBonusTcm;

	/** @orm(type="integer") */
	protected $pezziDimMulti;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziDimCap;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="integer") */
	protected $pezziSuperBonusTcm;

	/** @orm(type="float") */
	protected $premiCompDimMulti;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompDimCap;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="float") */
	protected $premiCompSuperBonusTcm;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;
	/** @orm(type="boolean") */
	protected $superbonusTcmOK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonus;
	/** @orm(type="float") */
	protected $importoErogSuperBonusTcm;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
