<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_75", target="iniz_75")
 */
class I75Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2013-10-21';
	const LAST_DAY	= '2013-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimBonus`) AS premiCompDimBonus,
		SUM(`premiCompDimInv`) AS premiCompDimInv,
		SUM(`premiCompDimFreeInv`) AS premiCompDimFreeInv,
		SUM(`premiCompDimPiuQ`) AS premiCompDimPiuQ,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDimBonus;
	/** @orm(type="float") */
	protected $premiDimInv;
	/** @orm(type="float") */
	protected $premiDimFreeInv;
	/** @orm(type="float") */
	protected $premiDimPiuQ;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimBonus;
	/** @orm(type="integer") */
	protected $pezziDimInv;
	/** @orm(type="integer") */
	protected $pezziDimFreeInv;
	/** @orm(type="integer") */
	protected $pezziDimPiuQ;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimBonus;
	/** @orm(type="float") */
	protected $premiCompDimInv;
	/** @orm(type="float") */
	protected $premiCompDimFreeInv;
	/** @orm(type="float") */
	protected $premiCompDimPiuQ;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="float") */
	protected $perc;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
