<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_119", target="iniz_119")
 */
class I119Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-10-01';
	const LAST_DAY	= '2015-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompQac`) AS premiCompQac,
		SUM(`premiCompQacHb`) AS premiCompQacHb,
		SUM(`premiCompPlInf`) AS premiCompPlInf,
		SUM(`premiCompInfCond`) AS premiCompInfCond,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogQac`) AS importoErogQac,
		SUM(`importoErogQacHb`) AS importoErogQacHb,
		SUM(`importoErogPlInf`) AS importoErogPlInf,
		SUM(`importoErogInfCond`) AS importoErogInfCond,
		SUM(`importoErogBonus`) AS importoErogBonus,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoQac;
	/** @orm(type="integer") */
	protected $obiettivoQacHb;
	/** @orm(type="integer") */
	protected $obiettivoPlInf;
	/** @orm(type="float") */
	protected $obiettivoInfCond;

	/** @orm(type="float") */
	protected $premiQac;
	/** @orm(type="float") */
	protected $premiQacHb;
	/** @orm(type="float") */
	protected $premiPlInf;
	/** @orm(type="float") */
	protected $premiInfCond;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziQac;
	/** @orm(type="integer") */
	protected $pezziQacHb;
	/** @orm(type="integer") */
	protected $pezziPlInf;
	/** @orm(type="integer") */
	protected $pezziInfCond;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompQac;
	/** @orm(type="float") */
	protected $premiCompQacHb;
	/** @orm(type="float") */
	protected $premiCompPlInf;
	/** @orm(type="float") */
	protected $premiCompInfCond;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoQacOK;
	/** @orm(type="boolean") */
	protected $obiettivoQacHbOK;
	/** @orm(type="boolean") */
	protected $obiettivoPlInfOK;
	/** @orm(type="boolean") */
	protected $obiettivoInfCondOK;
	/** @orm(type="integer") */
	protected $percQac;
	/** @orm(type="integer") */
	protected $percPlInf;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErogQac;
	/** @orm(type="float") */
	protected $importoErogQacHb;
	/** @orm(type="float") */
	protected $importoErogPlInf;
	/** @orm(type="float") */
	protected $importoErogInfCond;
	/** @orm(type="float") */
	protected $importoErogBonus;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
