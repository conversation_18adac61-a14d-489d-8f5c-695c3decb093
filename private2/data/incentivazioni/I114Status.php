<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_114", target="iniz_114")
 */
class I114Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-06-10';
	const LAST_DAY	= '2015-09-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivoAuto`) AS obiettivoAuto,
		SUM(`obiettivoHome`) AS obiettivoHome,
		SUM(`pezziAuto`) AS pezziAuto,
		SUM(`pezziQACconHB`) AS pezziHome,
		SUM(`pezziQACnoHB`) AS pezziQACnoHB,
		SUM(`premiCompAuto`) AS premiCompAuto,
		SUM(`premiCompQACtotAN`) AS premiCompQACtotAN,
		SUM(`premiCompQACconHB`) AS premiCompQACconHB
SQL;

	const SUMMARY_AREA_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivoAuto`) AS obiettivoAuto,
		SUM(`obiettivoHome`) AS obiettivoHome,
		SUM(`pezziAuto`) AS pezziAuto,
		SUM(`pezziQACconHB`) AS pezziHome,
		SUM(`pezziQACnoHB`) AS pezziQACnoHB,
		SUM(`premiCompAuto`) AS premiCompAuto,
		SUM(`premiCompQACtotAN`) AS premiCompQACtotAN,
		SUM(`premiCompQACconHB`) AS premiCompQACconHB
SQL;

	const SUMMARY_GEO_SQL = <<<SQL
		regione, provincia, COUNT(*) AS numAgenzie,
		SUM(`obiettivoAuto`) AS obiettivoAuto,
		SUM(`obiettivoHome`) AS obiettivoHome,
		SUM(`pezziAuto`) AS pezziAuto,
		SUM(`pezziQACconHB`) AS pezziHome,
		SUM(`pezziQACnoHB`) AS pezziQACnoHB,
		SUM(`premiCompAuto`) AS premiCompAuto,
		SUM(`premiCompQACtotAN`) AS premiCompQACtotAN,
		SUM(`premiCompQACconHB`) AS premiCompQACconHB
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoQACtotAN;
	/** @orm(type="integer") */
	protected $obiettivoQACconHB;
	/** @orm(type="integer") */
	protected $obiettivoAuto;
	/** @orm(type="integer") */
	protected $obiettivoHome;
	/** @orm(type="float") */
	protected $premiAuto;
	/** @orm(type="float") */
	protected $premiHome;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziAuto;
	/** @orm(type="integer") */
	protected $pezziQACtotAN;
	/** @orm(type="integer") */
	protected $pezziQACconHB;
	/** @orm(type="integer") */
	protected $pezziQACnoHB;

	/** @orm(type="float") */
	protected $premiCompAuto;
	/** @orm(type="float") */
	protected $premiCompQACtotAN;
	/** @orm(type="float") */
	protected $premiCompQACconHB;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
