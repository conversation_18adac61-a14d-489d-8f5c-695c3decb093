<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_196_myangel")
 */
class I196MyAngel {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;

	// Dati cliente

	/** @orm(primarykey) */
	protected $codiceCliente;
	/** @orm */
	protected $tipo;
	/** @orm */
	protected $CF_PIVA;
	/** @orm */
	protected $data;
	/** @orm */
	protected $nome;
	/** @orm */
	protected $cognome;
	/** @orm */
	protected $telefono;
	/** @orm */
	protected $email;
}
