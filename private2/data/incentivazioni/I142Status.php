<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_142", target="iniz_142")
 */
class I142Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-05-23';
	const LAST_DAY	= '2016-08-04';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompDimMultiInv`) AS premiCompDimMultiInv,
		SUM(`premiCompDimMultiFree`) AS premiCompDimMultiFree,
		SUM(`premiCompDimQuo`) AS premiCompDimQuo,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErog`) AS importoErog,
		SUM(`importoErogBonusUnitLinked`) AS importoErogBonusUnitLinked,
		SUM(`importoErogProQuota`) AS importoErogProQuota,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;

	/** @orm(type="float") */
	protected $premiDimMultiInv;
	/** @orm(type="float") */
	protected $premiDimMultiFree;
	/** @orm(type="float") */
	protected $premiDimQuo;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDimMultiInv;
	/** @orm(type="integer") */
	protected $pezziDimMultiFree;
	/** @orm(type="integer") */
	protected $pezziDimQuo;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDimMultiInv;
	/** @orm(type="float") */
	protected $premiCompDimMultiFree;
	/** @orm(type="float") */
	protected $premiCompDimQuo;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="float") */
	protected $frazDimMultiInv_UL;
	/** @orm(type="float") */
	protected $frazDimMultiFree_UL;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusUnitLinkedOK;
	/** @orm(type="boolean") */
	protected $obiettivoProQuotaOK;

	/** @orm(type="float") */
	protected $importoErog;
	/** @orm(type="float") */
	protected $importoErogBonusUnitLinked;
	/** @orm(type="float") */
	protected $importoErogProQuota;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
