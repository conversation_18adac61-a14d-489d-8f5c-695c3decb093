<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_58", target="iniz_58")
 */
class I58Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-04-01';
	const LAST_DAY	= '2012-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`polizzeGA200`) AS polizzeGA200,
		SUM(`premiGA200`) AS premiGA200,
		SUM(`importoErogabileGA200`) AS importoErogabileGA200,
		SUM(`polizzePLINF`) AS polizzePLINF,
		SUM(`premiPLINF`) AS premiPLINF,
		SUM(`importoErogabilePLINF`) AS importoErogabilePLINF,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoGA200;

	/** @orm(type="float") */
	protected $premiGA200;
	/** @orm(type="float") */
	protected $premiPLINF;

	/** @orm(type="integer") */
	protected $polizzeGA200;
	/** @orm(type="integer") */
	protected $polizzePLINF;


	/** @orm(type="float") */
	protected $importoErogabileGA200;
	/** @orm(type="float") */
	protected $importoErogabilePLINF;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
