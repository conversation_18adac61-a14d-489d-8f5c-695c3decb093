<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_110_intermediari", target="iniz_110_intermediari")
 */
class I110Intermediario {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(readonly) */
	protected $agenzia_id;
	/** @orm(readonly) */
	protected $area;
	/** @orm(readonly) */
	protected $district;

	// User

	/** @orm(primarykey) */
	protected $user_id;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $cognome;
	/** @orm(readonly) */
	protected $codEsazione;

	/** @orm(readonly) */
	protected $createdAt;
}
