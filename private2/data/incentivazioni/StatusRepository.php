<?php
namespace data\incentivazioni;
use metadigit\core\db\Query,
	metadigit\core\db\orm\Metadata;

class StatusRepository extends \metadigit\core\db\orm\Repository {

	const SQL_OBJ_AREE_BY_PEZZI = <<<SQL
		status, area, COUNT(*) AS numAgenzie,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(objGAobjPezzi) AS objGAobjPezzi,
		ROUND( SUM(pezziTOTALI) / SUM(objGAobjPezzi) * 100 ) AS objGApercPezzi,
		SUM(IF(premiTOTALI > 0, 1 ,0)) AS agenzieProd,
		ROUND( SUM(IF(premiTOTALI > 0, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieProd,
		SUM(IF(objGAstatus = 1, 1 ,0)) AS agenzieStatusOK,
		ROUND( SUM(IF(objGAstatus = 1, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieStatusOK,
		SUM(IF(objGApercPezzi >= 100, 1 ,0)) AS agenzieObjOK,
		ROUND( SUM(IF(objGApercPezzi >= 100, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieObjOK,
		SUM(pezziTOTALI) AS pezziTOTALI
SQL;

	const SQL_OBJ_AREE_BY_PREMI = <<<SQL
		status, area, COUNT(*) AS numAgenzie,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(objGAobjPremi) AS objGAobjPremi,
		ROUND( SUM(premiTOTALI) / SUM(objGAobjPremi) * 100 ) AS objGApercPremi,
		SUM(IF(premiTOTALI > 0, 1 ,0)) AS agenzieProd,
		ROUND( SUM(IF(premiTOTALI > 0, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieProd,
		SUM(IF(objGAstatus = 1, 1 ,0)) AS agenzieStatusOK,
		ROUND( SUM(IF(objGAstatus = 1, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieStatusOK,
		SUM(IF(objGApercPremi >= 100, 1 ,0)) AS agenzieObjOK,
		ROUND( SUM(IF(objGApercPremi >= 100, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieObjOK,
		SUM(pezziTOTALI) AS pezziTOTALI
SQL;

	const SQL_OBJ_DISTRICTS_BY_PEZZI = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(objGAobjPezzi) AS objGAobjPezzi,
		ROUND( SUM(pezziTOTALI) / SUM(objGAobjPezzi) * 100 ) AS objGApercPezzi,
		SUM(IF(premiTOTALI > 0, 1 ,0)) AS agenzieProd,
		ROUND( SUM(IF(premiTOTALI > 0, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieProd,
		SUM(IF(objGAstatus = 1, 1 ,0)) AS agenzieStatusOK,
		ROUND( SUM(IF(objGAstatus = 1, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieStatusOK,
		SUM(IF(objGApercPezzi >= 100, 1 ,0)) AS agenzieObjOK,
		ROUND( SUM(IF(objGApercPezzi >= 100, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieObjOK,
		SUM(pezziTOTALI) AS pezziTOTALI
SQL;

	const SQL_OBJ_DISTRICTS_BY_PREMI = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(objGAobjPremi) AS objGAobjPremi,
		ROUND( SUM(premiTOTALI) / SUM(objGAobjPremi) * 100 ) AS objGApercPremi,
		SUM(IF(premiTOTALI > 0, 1 ,0)) AS agenzieProd,
		ROUND( SUM(IF(premiTOTALI > 0, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieProd,
		SUM(IF(objGAstatus = 1, 1 ,0)) AS agenzieStatusOK,
		ROUND( SUM(IF(objGAstatus = 1, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieStatusOK,
		SUM(IF(objGApercPremi >= 100, 1 ,0)) AS agenzieObjOK,
		ROUND( SUM(IF(objGApercPremi >= 100, 1 ,0)) / COUNT(*) * 100 ) AS percAgenzieObjOK,
		SUM(pezziTOTALI) AS pezziTOTALI
SQL;

	public $objGaType = 'PREMI';

	function __sleep() {
		return ['_oid', 'class', 'pdo', 'objGaType'];
	}

	function fetchSummary() {
		$sql = constant($this->class.'::SUMMARY_SQL');
		$criteriaExp = $this->criteriaExp();
		$Query = (new Query($this->pdo))->on(Metadata::get($this->class)->sql('source'), $sql)
			->groupBy('status, area, district')->withRollup()->having('status != "OFF"')
			->criteriaExp($criteriaExp);
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	function fetchSummaryArea() {
		$sql = constant($this->class.'::SUMMARY_AREA_SQL');
		$criteriaExp = $this->criteriaExp();
		$Query = (new Query($this->pdo))->on(Metadata::get($this->class)->sql('source'), $sql)
			->groupBy('status, area, district')->withRollup()->having('status != "OFF"')
			->criteriaExp($criteriaExp);
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	function fetchSummaryGeo() {
		$sql = constant($this->class.'::SUMMARY_GEO_SQL');
		$criteriaExp = $this->criteriaExp();
		$Query = (new Query($this->pdo))->on(Metadata::get($this->class)->sql('source'), $sql)
			->groupBy('regione, provincia')->withRollup()
			->criteriaExp($criteriaExp);
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	function fetchObjAree() {
		$sql = ($this->objGaType=='PREMI') ? self::SQL_OBJ_AREE_BY_PREMI : self::SQL_OBJ_AREE_BY_PEZZI;
		$criteriaExp = $this->criteriaExp();
		$Query = (new Query($this->pdo))->on(Metadata::get($this->class)->sql('source'), $sql)
			->groupBy('status, area')->withRollup()->having('status != "OFF"')
			->criteriaExp($criteriaExp);
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	function fetchObjDistricts() {
		$sql = ($this->objGaType=='PREMI') ? self::SQL_OBJ_DISTRICTS_BY_PREMI : self::SQL_OBJ_DISTRICTS_BY_PEZZI;
		$criteriaExp = $this->criteriaExp();
		$Query = (new Query($this->pdo))->on(Metadata::get($this->class)->sql('source'), $sql)
			->groupBy('status, area, district')->withRollup()->having('status != "OFF"')
			->criteriaExp($criteriaExp);
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	function fetchObjAgenzie() {
		$criteriaExp = $this->criteriaExp();
		return $this->execFetchAll(__FUNCTION__, null, null, '', $criteriaExp, self::FETCH_ARRAY);
	}

	/** Build $criteriaExp based on $_SESSION['AUTH']
	 * @return string
	 */
	protected function criteriaExp() {
		$criteriaExp = '';
		switch($_SESSION['AUTH']['UTYPE']) {
			case 'AREAMGR':
			case 'FOR':
				$criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
				break;
			case 'ASV':
				if($_SESSION['AUTH']['AREA']) $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
				break;
			case 'DISTRICTMGR':
				$criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'].'|district,EQ,'.$_SESSION['AUTH']['DISTRICT'];
				break;
		}
		return $criteriaExp;
	}
}
