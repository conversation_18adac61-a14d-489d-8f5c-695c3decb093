<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_83", target="iniz_83")
 */
class I83Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-03-01';
	const LAST_DAY	= '2014-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziPack`) AS pezziPack,
		SUM(`premiPack`) AS premiPack,
		SUM(`pezziPluri`) AS pezziPluri,
		SUM(`premiPluri`) AS premiPluri,
		SUM(`importoErogPack`) AS importoErogPack,
		SUM(`importoErogPluri`) AS importoErogPluri,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="integer") */
	protected $pezziPack;
	/** @orm(type="float") */
	protected $premiPack;
	/** @orm(type="integer") */
	protected $pezziPluri;
	/** @orm(type="float") */
	protected $premiPluri;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErogPack;
	/** @orm(type="float") */
	protected $importoErogPluri;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
