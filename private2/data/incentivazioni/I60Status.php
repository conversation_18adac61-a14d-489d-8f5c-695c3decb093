<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_60", target="iniz_60")
 */
class I60Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2012-05-28';
	const LAST_DAY	= '2012-07-27';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`premiCompDIMINV`) AS premiCompDIMINV,
		SUM(`premiCompDIMPQ`) AS premiCompDIMPQ,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="float") */
	protected $premiDIMINV;
	/** @orm(type="float") */
	protected $premiDIMPQ;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziDIMINV;
	/** @orm(type="integer") */
	protected $pezziDIMPQ;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompDIMINV;
	/** @orm(type="float") */
	protected $premiCompDIMPQ;
	/** @orm(type="float") */
	protected $premiCompTOTALI;


	/** @orm(type="integer") */
	protected $obiettivo;
	/** @orm(type="integer") */
	protected $percObiettivo;
	/** @orm(type="integer") */
	protected $pesoDPQ;

	/** @orm(type="float") */
	protected $bonus;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
