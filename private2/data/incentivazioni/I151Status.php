<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_151", target="iniz_151")
 */
class I151Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2016-10-01';
	const LAST_DAY	= '2016-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(obiettivo) AS obiettivo,
		SUM(pezziDinFullAttivita) AS pezziDinFullAttivita,
		SUM(pezziDinPlusImpresa) AS pezziDinPlusImpresa,
		SUM(pezziDinPlusAlbergo) AS pezziDinPlusAlbergo,
		SUM(pezziDinPlusCommercio) AS pezziDinPlusCommercio,
		SUM(pezziRealtaFabbricati) AS pezziRealtaFabbricati,
		SUM(pezziGAGlobFabbricati) AS pezziGAGlobFabbricati,
		SUM(pezziGAProtezFabbricati) AS pezziGAProtezFabbricati,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(premiCompDinFullAttivita) AS premiCompDinFullAttivita,
		SUM(premiCompDinPlusImpresa) AS premiCompDinPlusImpresa,
		SUM(premiCompDinPlusAlbergo) AS premiCompDinPlusAlbergo,
		SUM(premiCompDinPlusCommercio) AS premiCompDinPlusCommercio,
		SUM(premiCompRealtaFabbricati) AS premiCompRealtaFabbricati,
		SUM(premiCompGAGlobFabbricati) AS premiCompGAGlobFabbricati,
		SUM(premiCompGAProtezFabbricati) AS premiCompGAProtezFabbricati,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;

	/** @orm(type="integer") */
	protected $pezziDinFullAttivita;
	/** @orm(type="integer") */
	protected $pezziDinPlusImpresa;
	/** @orm(type="integer") */
	protected $pezziDinPlusAlbergo;
	/** @orm(type="integer") */
	protected $pezziDinPlusCommercio;
	/** @orm(type="integer") */
	protected $pezziRealtaFabbricati;
	/** @orm(type="integer") */
	protected $pezziGAGlobFabbricati;
	/** @orm(type="integer") */
	protected $pezziGAProtezFabbricati;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiDinFullAttivita;
	/** @orm(type="float") */
	protected $premiDinPlusImpresa;
	/** @orm(type="float") */
	protected $premiDinPlusAlbergo;
	/** @orm(type="float") */
	protected $premiDinPlusCommercio;
	/** @orm(type="float") */
	protected $premiRealtaFabbricati;
	/** @orm(type="float") */
	protected $premiGAGlobFabbricati;
	/** @orm(type="float") */
	protected $premiGAProtezFabbricati;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="float") */
	protected $premiCompDinFullAttivita;
	/** @orm(type="float") */
	protected $premiCompDinPlusImpresa;
	/** @orm(type="float") */
	protected $premiCompDinPlusAlbergo;
	/** @orm(type="float") */
	protected $premiCompDinPlusCommercio;
	/** @orm(type="float") */
	protected $premiCompRealtaFabbricati;
	/** @orm(type="float") */
	protected $premiCompGAGlobFabbricati;
	/** @orm(type="float") */
	protected $premiCompGAProtezFabbricati;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;

	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
