<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_170", target="iniz_170")
 */
class I170Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2017-04-05';
	const LAST_DAY	= '2017-08-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(obiettivo1) AS obiettivo1,
		SUM(pezziDinFullAtt) AS pezziDinFullAtt,
		SUM(pezziDinPlusImp) AS pezziDinPlusImp,
		SUM(pezziDinPlusAlb) AS pezziDinPlusAlb,
		SUM(pezziDinPlusCom) AS pezziDinPlusCom,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiTOTALI) AS premiTOTALI,
		SUM(premiCompDinFullAtt) AS premiCompDinFullAtt,
		SUM(premiCompDinPlusImp) AS premiCompDinPlusImp,
		SUM(premiCompDinPlusAlb) AS premiCompDinPlusAlb,
		SUM(premiCompDinPlusCom) AS premiCompDinPlusCom,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// GEO
	/** @orm(type="integer", readonly) */
	protected $provincia_id;
	/** @orm(readonly) */
	protected $provincia;
	/** @orm(type="integer", readonly) */
	protected $regione_id;
	/** @orm(readonly) */
	protected $regione;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo1;
	/** @orm(type="integer") */
	protected $obiettivo2;

	/** @orm(type="integer") */
	protected $pezziDinFullAtt;
	/** @orm(type="integer") */
	protected $pezziDinPlusImp;
	/** @orm(type="integer") */
	protected $pezziDinPlusAlb;
	/** @orm(type="integer") */
	protected $pezziDinPlusCom;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiDinFullAtt;
	/** @orm(type="float") */
	protected $premiDinPlusImp;
	/** @orm(type="float") */
	protected $premiDinPlusAlb;
	/** @orm(type="float") */
	protected $premiDinPlusCom;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="float") */
	protected $premiCompDinFullAtt;
	/** @orm(type="float") */
	protected $premiCompDinPlusImp;
	/** @orm(type="float") */
	protected $premiCompDinPlusAlb;
	/** @orm(type="float") */
	protected $premiCompDinPlusCom;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivo1OK;
	/** @orm(type="boolean") */
	protected $obiettivo2OK;
	/** @orm(type="integer") */
	protected $perc;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
