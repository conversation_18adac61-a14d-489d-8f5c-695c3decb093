<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_108", target="iniz_108")
 */
class I108Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-04-01';
	const LAST_DAY	= '2015-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`obiettivo`) AS obiettivo,
		SUM(`pezziAgriStart`) AS pezziAgriStart,
		SUM(`pezziAgrirama`) AS pezziAgrirama,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiCompAgriStart`) AS premiCompAgriStart,
		SUM(`premiCompAgrirama`) AS premiCompAgrirama,
		SUM(`premiCompTOTALI`) AS premiCompTOTALI,
		SUM(`obiettivoOK`) AS obiettivoOK,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivo;
	/** @orm(type="float") */
	protected $premiAgriStart;
	/** @orm(type="float") */
	protected $premiAgrirama;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziAgriStart;
	/** @orm(type="integer") */
	protected $pezziAgrirama;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompAgriStart;
	/** @orm(type="float") */
	protected $premiCompAgrirama;
	/** @orm(type="float") */
	protected $premiCompTOTALI;


	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
