<?php
namespace data\incentivazioni;
/**
 * @orm(source="iniz_176_clienti")
 */
class I176Cliente {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;

	// Dati cliente

	/** @orm */
	protected $nome;
	/** @orm(primarykey) */
	protected $codFisc;
	/** @orm */
	protected $segmento;
	/** @orm */
	protected $sottosegmento;
	/** @orm(type="integer") */
	protected $punteggio;

	/** @orm(type="integer") */
	protected $pezziQAC;
	/** @orm(type="integer") */
	protected $pezziPLI;
	/** @orm(type="integer") */
	protected $pezziOther;
}
