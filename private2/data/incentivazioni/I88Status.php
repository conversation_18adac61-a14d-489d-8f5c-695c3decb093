<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_88", target="iniz_88")
 */
class I88Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2014-04-01';
	const LAST_DAY	= '2014-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziBloccaPrezzo`) AS pezziBloccaPrezzo,
		SUM(`pezziBlackBox`) AS pezziBlackBox,
		SUM(`pezziBonusDuetto`) AS pezziBonusDuetto,
		SUM(`pezziBonusFull`) AS pezziBonusFull,
		SUM(`pezziTOTALI`) AS pezziTOTALI,
		SUM(`premiTOTALI`) AS premiTOTALI,
		SUM(`importoErogTOTALE`) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $obiettivo;
	/** @orm
	 * @validate(regex="/^(BLOCCAPREZZO|BLACKBOX|BONUSDUETTO|BONUSFULL)$/") */
	protected $jolly;

	/** @orm(type="float") */
	protected $premiBloccaPrezzo;
	/** @orm(type="float") */
	protected $premiBlackBox;
	/** @orm(type="float") */
	protected $premiBonusDuetto;
	/** @orm(type="float") */
	protected $premiBonusFull;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziBloccaPrezzo;
	/** @orm(type="integer") */
	protected $pezziBlackBox;
	/** @orm(type="integer") */
	protected $pezziBonusDuetto;
	/** @orm(type="integer") */
	protected $pezziBonusFull;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompBloccaPrezzo;
	/** @orm(type="float") */
	protected $premiCompBlackBox;
	/** @orm(type="float") */
	protected $premiCompBonusDuetto;
	/** @orm(type="float") */
	protected $premiCompBonusFull;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoOK;
	/** @orm(type="integer") */
	protected $percWelcomeback;
	/** @orm(type="boolean") */
	protected $bonusOK;

	/** @orm(type="float") */
	protected $importoErogBloccaPrezzo;
	/** @orm(type="float") */
	protected $importoErogBlackBox;
	/** @orm(type="float") */
	protected $importoErogBonusDuetto;
	/** @orm(type="float") */
	protected $importoErogBonusFull;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
