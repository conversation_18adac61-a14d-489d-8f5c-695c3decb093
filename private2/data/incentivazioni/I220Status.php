<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_220", target="iniz_220")
 */
class I220Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2018-03-01';
	const LAST_DAY	= '2018-12-31';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(pezziTOTALI) AS pezziTOTALI,
		SUM(premiCompTOTALI) AS premiCompTOTALI,
		SUM(clientiMONO_ARGENTO) AS clientiMONO_ARGENTO,
		SUM(clientiMONO_OROPLAT) AS clientiMONO_OROPLAT,
		SUM(clientiARGENTO_OROPLAT) AS clientiARGENTO_OROPLAT,
		SUM(clientiTotali) AS clientiTotali,
		SUM(clientiMyAngel) AS clientiMyAngel,
		SUM(importoErogTOTALE) AS importoErogTOTALE
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $ptf2018;
	/** @orm(type="float") */
	protected $premiTOTALI;
	/** @orm(type="integer") */
	protected $pezziTOTALI;
	/** @orm(type="float") */
	protected $premiCompTOTALI;
	/** @orm(type="integer") */
	protected $clientiMONO_ARGENTO;
	/** @orm(type="integer") */
	protected $clientiMONO_OROPLAT;
	/** @orm(type="integer") */
	protected $clientiARGENTO_OROPLAT;
	/** @orm(type="integer") */
	protected $clientiF1_1P;
	/** @orm(type="integer") */
	protected $clientiF1_2P;
	/** @orm(type="integer") */
	protected $clientiF2_1P;
	/** @orm(type="integer") */
	protected $clientiF2_2P;
	/** @orm(type="integer") */
	protected $clientiF3_1P;
	/** @orm(type="integer") */
	protected $clientiF3_2P;
	/** @orm(type="integer") */
	protected $clienti_bonusPG_MONO_ARGENTO;
	/** @orm(type="integer") */
	protected $clienti_bonusPG_MONO_OROPLAT;
	/** @orm(type="integer") */
	protected $clienti_bonusPG_ARGENTO_OROPLAT;
	/** @orm(type="integer") */
	protected $clientiTotali;
	/** @orm(type="integer") */
	protected $clientiMyAngel;
	/** @orm(type="integer") */
	protected $percMyAngel;
	/** @orm(type="boolean") */
	protected $bonusPtfOK;

	/** @orm(type="float") */
	protected $importoBase;
	/** @orm(type="float") */
	protected $importoBonusF1;
	/** @orm(type="float") */
	protected $importoBonusF2;
	/** @orm(type="float") */
	protected $importoBonusF3;
	/** @orm(type="float") */
	protected $importoMyAngel;
	/** @orm(type="float") */
	protected $importoSuperBonus;
	/** @orm(type="float") */
	protected $importoExtraBonusPG;
	/** @orm(type="float") */
	protected $importoErogTOTALE;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	//protected $objGAobjPremi;
	/** @orm(type="integer") */
	protected $objGAobjPezzi;
	/** @orm(type="float") */
	//protected $objGApercPremi;
	/** @orm(type="float") */
	protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
