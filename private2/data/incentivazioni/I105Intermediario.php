<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_105_intermediari", target="iniz_105_intermediari")
 */
class I105Intermediario {
	use \metadigit\core\db\orm\EntityTrait;

	// Agenzia

	/** @orm(readonly) */
	protected $agenzia_id;
	/** @orm(readonly) */
	protected $area;
	/** @orm(readonly) */
	protected $district;

	// User

	/** @orm(primarykey) */
	protected $user_id;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $cognome;
	/** @orm(readonly) */
	protected $codEsazione;

	// status

	/** @orm(type="integer") */
	protected $clienti;
	/** @orm(readonly) */
	protected $createdAt;
}
