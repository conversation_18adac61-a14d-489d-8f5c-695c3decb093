<?php
namespace data\incentivazioni;
use metadigit\core\db\orm\OrmEvent;

class ACL {
	use \metadigit\core\CoreTrait;

	function onFetch(OrmEvent $Event) {
		$criteriaExp = null;
		$Repository = $Event->getRepository();
		switch(get_class($Repository)) {
			case 'data\incentivazioni\StatusRepository':
				switch($_SESSION['AUTH']['UTYPE']) {
					case 'KA':
					case 'AMMINISTRATORE':
						break;
					case 'AREAMGR':
					case 'FOR':
						$criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
						break;
					case 'ASV':
						if($_SESSION['AUTH']['AREA']) $criteriaExp = 'area,EQ,'.$_SESSION['AUTH']['AREA'];
						break;
					case 'DISTRICTMGR':
						$criteriaExp = 'district,EQ,'.$_SESSION['AUTH']['DISTRICT'];
						break;
					case 'AGENTE':
						$criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'];
						break;
					default:
						trigger_error('UTYPE unknown, ACL not defined!', E_USER_NOTICE);
				}
				break;
			default:
				switch($Repository->_oid()) {
					case 'data.incentivazioni.IncentivazioniRepository':

						break;
					case 'data.incentivazioni.PolizzeRepository':
						switch($_SESSION['AUTH']['UTYPE']) {
							case 'KA':
							case 'AMMINISTRATORE':
							case 'AREAMGR':
							case 'DISTRICTMGR':
							case 'FOR':
							case 'ASV':
								break;
							case 'AGENTE':
								$criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'];
								break;
							case 'INTERMEDIARIO':
								$criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'].'|codEsazione,EQ,'.$_SESSION['AUTH']['COD_ESAZIONE'];
								break;
							default:
								trigger_error('UTYPE unknown, ACL not defined!', E_USER_NOTICE);
						}
						break;
					default:
						switch($_SESSION['AUTH']['UTYPE']) {
							case 'KA':
							case 'AMMINISTRATORE':
							case 'AREAMGR':
							case 'DISTRICTMGR':
							case 'FOR':
							case 'ASV':
								break;
							case 'AGENTE':
								$criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'];
								break;
							case 'INTERMEDIARIO':
								$criteriaExp = 'agenzia_id,EQ,'.$_SESSION['AUTH']['AGENZIA'].'|codEsazione,EQ,'.$_SESSION['AUTH']['COD_ESAZIONE'];
								break;
							default:
								trigger_error('UTYPE unknown, ACL not defined!', E_USER_NOTICE);
						}
				}
		}
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, $_SESSION['AUTH']['UTYPE'].': '.$criteriaExp);
		$Event->criteriaExp($criteriaExp);
	}
}
