<?php
namespace data\incentivazioni;
/**
 * @orm(source="vw_iniz_102", target="iniz_102")
 */
class I102Status {
	use \metadigit\core\db\orm\EntityTrait;

	const FIRST_DAY	= '2015-02-02';
	const LAST_DAY	= '2015-04-30';

	const SUMMARY_SQL = <<<SQL
		status, area, district, COUNT(*) AS numAgenzie,
		SUM(`pezziPLI`) AS pezziPLI,
		SUM(`pezziQAC`) AS pezziQAC,
		SUM(`premiCompPLI`) AS premiCompPLI,
		SUM(`premiCompQAC`) AS premiCompQAC,
		SUM(`obiettivoPliOK`) AS obiettivoPliOK,
		SUM(`obiettivoQacOK`) AS obiettivoQacOK,
		SUM(`importoErogabile`) AS importoErogabile
SQL;

	// Agenzia

	/** @orm(primarykey) */
	protected $agenzia_id;
	/** @orm(type="integer", readonly) */
	protected $area;
	/** @orm(type="integer", readonly) */
	protected $district;
	/** @orm(readonly) */
	protected $localita;
	/** @orm(readonly) */
	protected $nome;
	/** @orm(readonly) */
	protected $status;

	// Dati iniziativa

	/** @orm(type="integer") */
	protected $gruppo;
	/** @orm(type="integer") */
	protected $obiettivoPLI;
	/** @orm(type="integer") */
	protected $obiettivoQAC;

	/** @orm(type="float") */
	protected $premiPLI;
	/** @orm(type="float") */
	protected $premiQAC;
	/** @orm(type="float") */
	protected $premiTOTALI;

	/** @orm(type="integer") */
	protected $pezziPLI;
	/** @orm(type="integer") */
	protected $pezziQAC;
	/** @orm(type="integer") */
	protected $pezziTOTALI;

	/** @orm(type="float") */
	protected $premiCompPLI;
	/** @orm(type="float") */
	protected $premiCompQAC;
	/** @orm(type="float") */
	protected $premiCompTOTALI;

	/** @orm(type="boolean") */
	protected $obiettivoPliOK;
	/** @orm(type="boolean") */
	protected $obiettivoQacOK;

	/** @orm(type="float") */
	protected $importoErogabile;

	// OBJ Direzione

	/** @orm(type="boolean") */
	protected $objGAshow;
	/** @orm(type="float") */
	protected $objGAobjPremi;
	/** @orm(type="integer") */
	//protected $objGAobjPezzi;
	/** @orm(type="float") */
	protected $objGApercPremi;
	/** @orm(type="float") */
	//protected $objGApercPezzi;
	/** @orm(type="boolean") */
	protected $objGAstatus;
	/** @orm(type="float") */
	protected $objGApercStatus;
}
