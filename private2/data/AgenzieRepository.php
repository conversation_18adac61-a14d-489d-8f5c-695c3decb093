<?php
namespace data;
use metadigit\core\db\Query,
	metadigit\core\db\orm\Metadata,
	metadigit\core\db\orm\OrmEvent;

class AgenzieRepository extends \metadigit\core\db\orm\Repository {

	static $aree = [
		1 => ['id'=>1, 'code'=>'NO', 'nome'=>'Nord-Ovest'],
		//2 => ['id'=>2, 'code'=>'N', 'nome'=>'Nord'],
		3 => ['id'=>3, 'code'=>'NE', 'nome'=>'Nord-Est'],
		4 => ['id'=>4, 'code'=>'CN', 'nome'=>'Centro-Nord'],
		//5 => ['id'=>5, 'code'=>'RM', 'nome'=>'Roma & Prov.'],
		//6 => ['id'=>6, 'code'=>'', 'nome'=>'Agenzie di Direzione'],
		7 => ['id'=>7, 'code'=>'SO', 'nome'=>'Sud-Ovest'],
		8 => ['id'=>8, 'code'=>'C', 'nome'=>'Centro'],
		//9 => ['id'=>9, 'code'=>'', 'nome'=>'Varie'],
		//10 =>['id'=>10, 'code'=>'SE', 'nome'=>'Sud-Est']
		51 =>['id'=>51, 'code'=>'S1', 'nome'=>'Area Speciale 1'],
		52 =>['id'=>52, 'code'=>'S2', 'nome'=>'Area Speciale 2'],
		99 =>['id'=>99, 'code'=>'DZ', 'nome'=>'Area Direzione']
	];

	function arrayAree() {
		switch($_SESSION['AUTH']['UTYPE']) {
			case 'KA':
			case 'AMMINISTRATORE':
				return array_values(self::$aree);
				break;
			case 'AREAMGR':
			case 'DISTRICTMGR':
			case 'AGENTE':
				return [self::$aree[$_SESSION['AUTH']['AREA']]];
				break;
		}
	}

	function arrayDistricts() {
		$OrmEvent = new OrmEvent($this);
		$this->Context->trigger(OrmEvent::EVENT_PRE_FETCH_ALL, null, null, $OrmEvent);
		$Query = (new Query($this->pdo))->setCriteriaDictionary(Metadata::get($this->class)->criteria())->on('users', 'area, district, nome, cognome')
			->orderBy('district ASC')->criteria('active = 1 AND type = "DISTRICTMGR"')->criteriaExp($OrmEvent->getCriteriaExp());
		return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);

	}

    function fetchArray($fields, $criteriaExp = '') {
        $OrmEvent = new OrmEvent($this);
        $this->Context->trigger(OrmEvent::EVENT_PRE_FETCH_ALL, null, null, $OrmEvent);
        $Query = (new Query($this->pdo))->setCriteriaDictionary(Metadata::get($this->class)->criteria())->on('agenzie', $fields)
            ->orderBy('id ASC')->criteriaExp('status,IN,ON,INTER,NEW')->criteriaExp($OrmEvent->getCriteriaExp());
        if ($criteriaExp) {
            $Query = $Query->criteriaExp($criteriaExp);
        }
        return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
    }

	function fetchArrayAllStatus($fields, $criteriaExp = 'status,IN,ON,INTER') {
		$OrmEvent = new OrmEvent($this);
		$this->Context->trigger(OrmEvent::EVENT_PRE_FETCH_ALL, null, null, $OrmEvent);
		$Query = (new Query($this->pdo))->setCriteriaDictionary(Metadata::get($this->class)->criteria())->on('agenzie', $fields)
			->orderBy('id ASC')->criteriaExp($criteriaExp)->criteriaExp($OrmEvent->getCriteriaExp());
        return $Query->execSelect()->fetchAll(\PDO::FETCH_ASSOC);
	}

	/**
	 * @param Agenzia $Agenzia
	 * @param string|null $validateMode
	 * @return array
	 */
	function validate($Agenzia, $validateMode) {
		$errors = [];
		$idCriteria = ($Agenzia->id==null) ? 'id IS NOT NULL' : 'id != :id';

		if($Agenzia->localita == '') $errors['localita'] = 'required';
		if($Agenzia->nome == '') $errors['nome'] = 'required';
		$Agenzia->email = strtolower($Agenzia->email);
		if($Agenzia->telefono == '') $errors['telefono'] = 'required';
		//if($Agenzia->fax == '') $errors['fax'] = 'required';
		if($Agenzia->indirizzo == '') $errors['indirizzo'] = 'required';
		if($Agenzia->cap == '') $errors['cap'] = 'required';
		if($Agenzia->citta == '') $errors['citta'] = 'required';
		//if($Agenzia->mandato == '') $errors['mandato'] = 'required';
		if($Agenzia->dataMandato == '') $errors['dataMandato'] = 'required';
		//if($Agenzia->dataRiorg == '') $errors['dataRiorg'] = 'required';

		return $errors;
	}
}
