<?php
namespace data;
/**
 * @orm(source="users_icebox")
 */
class UserIcebox {
	use \metadigit\core\db\orm\EntityTrait;

	/** @orm(type="integer", primarykey, autoincrement) */
	protected $id;

	// personal data

	/** @orm(null) */
	protected $nome;
	/** @orm(null) */
	protected $cognome;
	/** @orm(null) */
	protected $agenzia_id;
    /** @orm(null) */
    protected $login;
	/** @orm(null)
	 * @validate(regex="/^(AGENTE|INTERMEDIARIO)$/") */
	protected $type;
    /** @orm(null) */
    protected $email;

	// system data
	/** @orm(readonly) */
	protected $createdAt;
}
