<?php

namespace data\apps\formazione\Managers;


use data\apps\formazione\Repositories\ExternalCreditsRepository;
use data\apps\formazione\Repositories\SelfCertificatedCreditsRepository;
use DateTime;
use metadigit\core\CoreTrait;
use TrainingDev\Repositories\Interfaces\AttendanceRepositoryInterface;
use TrainingDev\Repositories\Interfaces\ClassRoomRepositoryInterface;
use TrainingDev\Repositories\Interfaces\CourseRepositoryInterface;
use TrainingDev\Repositories\Interfaces\UserRepositoryInterface;

class DashboardManager
{
    use CoreTrait;
    const MIN_RANGE_SLOT_1 = 0;
    const MAX_RANGE_SLOT_1 = 14.99;
    const MIN_RANGE_SLOT_2 = 15;
    const MAX_RANGE_SLOT_2 = 29.99;
    const MIN_RANGE_SLOT_3 = 30;
    const MAX_RANGE_SLOT_3 = 10000000;

    const MAX_CREDITS      = 30; // Total amount credits

    /**
     * @var ClassRoomRepositoryInterface
     */
    protected $classroomRepository;

    /**
     * @var AttendanceRepositoryInterface
     */
    protected $attendanceRepository;

    /**
     * @var UserRepositoryInterface
     */
    protected $userRepository;

    /**
     * @var CourseRepositoryInterface
     */
    protected $courseRepository;

    /**
     * @var ExternalCreditsRepository
     */
    protected $externalCreditsRepository;

    /**
     * @var SelfCertificatedCreditsRepository
     */
    protected $selfCertifiedCreditsRepository;

    /**
     * Retrieve data to show on user's dashboard
     *
     * @param $userId
     * @param $agencyCode
     * @return array
     */
    public function getDashboardCharts($userId, $agencyCode, $selectedYear)
    {
        $dashboard = [
            'status' => $this->status($userId, $selectedYear)
        ];

        if ($_SESSION['AUTH']['UTYPE'] == 'AGENTE') {
            $dashboard['agencyHours'] = [
                "slot0" => $this->slotNeverAttended($agencyCode, $selectedYear),
                "slot1" => $this->slot($agencyCode, self::MIN_RANGE_SLOT_1, self::MAX_RANGE_SLOT_1, $selectedYear),
                "slot2" => $this->slot($agencyCode, self::MIN_RANGE_SLOT_2, self::MAX_RANGE_SLOT_2, $selectedYear),
                "slot3" => $this->slot($agencyCode, self::MIN_RANGE_SLOT_3, self::MAX_RANGE_SLOT_3, $selectedYear),
            ];
        }

        return $dashboard;
    }

    /**
     * Retrieve data to show on user's dashboard
     *
     * @param $userId
     * @param $agencyCode
     * @return mixed
     */
    public function getDashboardCompletedCourses($userId, $agencyCode)
    {
        return $this->completedCourses($agencyCode, $userId);
    }

    /**
     * Retrieve data to show on user's dashboard
     *
     * @param $userId
     * @param $agencyCode
     * @return mixed
     */
    public function getDashboardActiveCourses($userId, $agencyCode)
    {
        $year   = date('Y');
        $result = null;

        if ($_SESSION['AUTH']['UTYPE'] == 'AGENTE') {
            $result = $this->activeCourses($userId, $agencyCode);
        }

        return $result;
    }

    /**
     * Calculate percentage
     *
     * @param $credits
     * @param $userCredits
     * @return string
     */
    protected function percentage($credits, $userCredits)
    {
        return number_format((float)($userCredits > 0) ? $userCredits / $credits : 0, 2) * 100;
    }

    /**
     * Calculate slot
     *
     * @param $agencyCode
     * @param $minRange
     * @param $maxRange
     * @param null $year
     * @return mixed
     */
    protected function slot($agencyCode, $minRange, $maxRange, $year = null)
    {
        $totalAttendees = $this->userRepository->countTotalByAgencyId($agencyCode);
        $attendees      = $this->attendanceRepository->findCreditsInRange($agencyCode, $year);

        return $this->createSlotResult($attendees, $totalAttendees, $minRange, $maxRange);
    }

    /**
     * Calculate slot never attended
     *
     * @param $agencyCode
     * @param null $year
     * @return object
     */
    protected function slotNeverAttended($agencyCode, $year = null)
    {
        $totalAttendees = $this->userRepository->countTotalByAgencyId($agencyCode);
        $attendees      = $this->attendanceRepository->findNeverAttended($agencyCode, $year);

        return $this->createSlotResult($attendees, $totalAttendees, 0, 0);
    }

    /**
     * Create data object to return for calculation slot
     *
     * @param $attendees
     * @param $totalAttendees
     * @param $minRange
     * @param $maxRange
     * @return object
     */
    protected function createSlotResult($attendees, $totalAttendees, $minRange, $maxRange)
    {
        $employees = [];

        foreach ($attendees as $a) {
            if (! key_exists($a->user_id, $employees)) {
                $employees[$a->user_id] = [
                    "name" => $a->firstname ." ". $a->lastname,
                    "id" => $a->user_id,
                    "credits" => 0
                ];
            }

            $employees[$a->user_id]['credits'] += $a->credits;
        }

        foreach ($employees as $id => $values) {
            if ($values['credits'] < $minRange || $values['credits'] > $maxRange) {
                unset($employees[$id]);
            }
        }

        $countAttendees = count($employees);

        return (object) [
            "percentage" => $this->percentage($totalAttendees, $countAttendees),
            "total" => $countAttendees,
            "employees" => $employees
        ];
    }

    /**
     * Completed course
     *
     * @param $agencyCode
     * @param $userId
     * @return mixed
     */
    protected function completedCourses($agencyCode, $userId)
    {
        return $this->classroomRepository->completedCoursesByUserId($agencyCode, $userId);
    }

    /**
     * Courses status to he was invited to
     *
     * @param $userId
     * @param $agencyCode
     * @param $year
     * @return array
     */
    protected function activeCourses($userId, $agencyCode)
    {
        $result     = [];
        $courses    = $this->courseRepository->activeCourses($agencyCode);

        foreach ($courses as $course) {
            $classrooms = $this->createDataClassroomsByCourse($course);

            if (count($classrooms) > 0) {
                // Max participants and duration days
                $agencyLimit = isset(json_decode($course->data, 1)['agencyLimit']) ?
                    json_decode($course->data, 1)['agencyLimit'] : 0;
                $durationDays = isset(json_decode($course->data, 1)['duration']) ?
                    json_decode($course->data, 1)['duration'] : 0;

                // Filter
                $agents = isset(json_decode($course->filters, 1)['agents']) ?
                    json_decode($course->filters, 1)['agents'] : 0;
                $intermediariesRegistered = isset(json_decode($course->filters, 1)['intermediariesRegistered']) ?
                    json_decode($course->filters, 1)['intermediariesRegistered'] : 0;
                $employeesRegistered = isset(json_decode($course->filters, 1)['employeesRegistered']) ?
                    json_decode($course->filters, 1)['employeesRegistered'] : 0;
                $employeesUnregistered = isset(json_decode($course->filters, 1)['employeesUnregistered']) ?
                    json_decode($course->filters, 1)['employeesUnregistered'] : 0;

                // Data course
                $data = [
                    'course_id' => $course->id,
                    'title' => $course->title,
                    'formationHours' => $course->credits,
                    'durationDays' => $durationDays,
                    'agencyLimit' => $agencyLimit,
                    'cover' => $course->cover,
                    'agents' => $agents,
                    'intermediariesRegistered' => $intermediariesRegistered,
                    'employeesRegistered' => $employeesRegistered,
                    'employeesUnregistered' => $employeesUnregistered
                ];

                // Data classrooms
                $data['locations'] = $classrooms;

                // Data Attendances
                $data['seats'] = $this->createDataAttendancesByCourse($userId, $course, $agencyCode, $agencyLimit);

                $result[] = $data;
            }
        }

        return $result;
    }

    protected function createDataClassroomsByCourse($course)
    {
        $data  = [];

        foreach ($this->classroomRepository->findByCourse($course->id) as $classroom) {
            $data[] = [
                'date' => $classroom->firstDay->format('d/m/Y'),
                'city' => $classroom->city,
                'place' => $classroom->locationName,
                'seats' => $classroom->seats,
                'booked' => $classroom->booked,
                'signedup' => $classroom->signedup,
                'vacantSeats' => $classroom->seats - ($classroom->signedup + $classroom->booked),
                'days' => $this->getLocationDates(json_decode($classroom->data)),
            ];
        }

        return $data;
    }

    protected function createDataAttendancesByCourse($userId, $course, $agencyCode, $maxParticipants)
    {
        $data = [];

        foreach ($this->attendanceRepository->findByCourseAndAgencyCode($course->id, $agencyCode) as $attendance) {
            if ($attendance->state == 'deleted') {
                continue;
            }

            if ($attendance->type == 'AGENTE' && $attendance->user_id != $userId) {
                continue;
            }

            $data[] = [
                'status' => $attendance->state,
                'name' => $attendance->firstname . " " . $attendance->lastname
            ];
        }

        $count = count($data);
        if ($count < $maxParticipants) {
            for ($i = $count; $i < $maxParticipants; $i++) {
                $data[] = [
                    'status' => 'vacant',
                    'name' => ''
                ];
            }
        }

        return $data;
    }

    /**
     * Retrieve user credits status
     *
     * @param $userId
     * @param $year
     * @return array
     */
    public function status($userId, $year)
    {
        $area                       = $this->getUserCredits($userId, $year, 'area');
        $direz                      = $this->getUserCredits($userId, $year, 'direz');
        $eLearning                  = $this->getUserCredits($userId, $year, 'e-learning');
        $external                   = $this->getUserCredits($userId, $year, null, 'external');
        $selfCertificated           = $this->getUserCredits($userId, $year, null, 'selfCertificated');
        $checkGDPR                  = $this->doneGdpr($userId);
        $checkAntiriciclaggio       = $this->doneAntiriciclaggio($userId);
        $gdprEditable               = $this->isGdprEditable($userId);
        $antiriciclaggioEditable    = $this->isAntiriciclaggioEditable($userId);

        return [
            "gdpr" => (int)$checkGDPR,
            "antiriciclaggio" => (int)$checkAntiriciclaggio,
            "isGdprEditable" => (int)$gdprEditable,
            "isAntiriciclaggioEditable" => (int)$antiriciclaggioEditable,
            "groupama" => [
                "area" => (float)number_format($area, 2),
                "direz" => (float)number_format($direz,2),
                "elearning" => (float)number_format($eLearning,2),
                "total" => (float)number_format($area + $direz + $eLearning,2),
            ],
            "external" => (float)number_format($external,2),
            "selfCertificated" => (float)number_format($selfCertificated, 2),
            "totalCredits" => $selfCertificated > 0 ? (float)number_format($area + $direz + $eLearning + $selfCertificated,2) : (float)number_format($area + $direz + $eLearning + $external,2),
        ];
    }

    /**
     * Get user credits (groupama) by year
     *
     * @param $userId
     * @param $year
     * @param $groupamaType
     * @param $creditsType
     * @return float|int
     */
    protected function getUserCredits($userId, $year, $groupamaType, $creditsType = 'attendance')
    {
        $userCredits = 0;

        if ($creditsType === 'attendance') {
            $userCredits = $this->attendanceRepository->countCreditsByCourseType($userId, $groupamaType, $year);
        }

        if ($creditsType === 'external') {
            $userCredits = $this->externalCreditsRepository->countExternalCredits($userId, $year);
        }

        if ($creditsType === 'selfCertificated') {
            $userCredits = $this->selfCertifiedCreditsRepository->countSelfCertificatedCredits($userId, $year);
        }

        return $userCredits ? $userCredits : 0;
    }

    private function doneGdpr($userId)
    {
        return $this->courseRepository->checkGdprAntiriciclaggio($userId, "gdpr");
    }

    private function doneAntiriciclaggio($userId)
    {
        return $this->courseRepository->checkGdprAntiriciclaggio($userId, "antiriciclaggio");
    }

    private function isGdprEditable($userId)
    {
        return $this->courseRepository->isGdprAntiriciclaggioEditable($userId, "gdpr");
    }

    private function isAntiriciclaggioEditable($userId)
    {
        return $this->courseRepository->isGdprAntiriciclaggioEditable($userId, "antiriciclaggio");
    }

    /**
     * Get Uniqum courses logged user is invited to
     *
     * @param $userId
     * @param $agencyCode
     * @param $year
     * @return array
     */
    public function uniqumActiveCourses($userId, $agencyCode)
    {
        $result     = [];
        $courses    = $this->courseRepository->activeCourses($agencyCode, null, 1, 'UNIQUM');

        foreach ($courses as $course) {
            $classrooms = $this->createDataClassroomsByCourse($course);

            if (count($classrooms) > 0) {
                // Max participants and duration days
                $agencyLimit = isset(json_decode($course->data, 1)['agencyLimit']) ?
                    json_decode($course->data, 1)['agencyLimit'] : 0;
                $durationDays = isset(json_decode($course->data, 1)['duration']) ?
                    json_decode($course->data, 1)['duration'] : 0;
                $subtitle = isset(json_decode($course->data, 1)['subtitle']) ?
                    json_decode($course->data, 1)['subtitle'] : "";

                // Data course
                $data = [
                    'course_id' => $course->id,
                    'title' => $course->title,
                    'subtitle' => $subtitle,
                    'formationHours' => $course->credits,
                    'durationDays' => $durationDays,
                    'agencyLimit' => $agencyLimit,
                    'cover' => $course->cover,
                    'totalSeats' => $this->getTotalSeats($classrooms),
                ];
                $data['remainingSeats'] = $this->getRemainingSeats($classrooms, $data['totalSeats']);

                // Data classrooms
                $data['locations'] = $classrooms;

                // Data Attendances
                $data['seats'] = $this->createDataAttendancesByCourse($userId, $course, $agencyCode, $agencyLimit);

                $result[] = $data;
            }
        }

        return $result;
    }

    protected function getTotalSeats($classrooms)
    {
        $total = 0;
        foreach ($classrooms as $classroom) {
            $total += $classroom['seats'];
        }
        return $total;
    }

    protected function getRemainingSeats($classrooms, $totalSeats)
    {
        $remainingSeatsAmount = $totalSeats;
        foreach ($classrooms as $classroom) {
            $remainingSeatsAmount -= ( (int)$classroom['booked'] + (int)$classroom['signedup'] );
        }
        return $remainingSeatsAmount;
    }

    protected function getLocationDates($data)
    {
        $days = [];
        foreach ($data->days as $day) {
            $date = new \DateTime($day->date);
            $days[] = $date->format('d/m/Y');
        }
        return $days;
    }

}
