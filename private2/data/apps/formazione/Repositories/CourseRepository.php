<?php

namespace data\apps\formazione\Repositories;

use TrainingDev\Repositories\Interfaces\CourseRepositoryInterface;
use metadigit\core\Kernel;

class CourseRepository extends \metadigit\core\db\orm\Repository implements CourseRepositoryInterface
{
    public function find($id)
    {
        return $this->fetch($id);
    }

    public function findByTitle($title, $year, $courseId = null)
    {
        $pdo = Kernel::pdo();
        $query = "select * from tra_course as tc where tc.title = :title and tc.groupamaType = 'area' and tc.year = :year";

        $params = [
            ':title' => addslashes($title),
            ':year' => $year,
        ];

        if ($courseId) {
            $query .= " and tc.id <> :courseId";

            $params[':courseId'] = $courseId;
        }

        $stmt = $pdo->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchColumn(0);
    }

    public function store(array $model)
    {
        return $this->insert(null, $model);
    }

    public function save($id, array $model)
    {
        return $this->update($id, $model);
    }

    public function changeStatus($id, array $model)
    {
        return $this->update($id, $model);
    }

    public function addDetails($id, array $model)
    {
        return $this->update($id, $model);
    }

    /**
     * Get recipients by filters
     *
     * @param $data
     * @return string
     */
    public function recipientsFilter($data)
    {
        $filters = $ids = $stmtParams = [];
        $deleted = $outcome = $participation = "";

        // BASIC FILTERS

        if (isset($data['agents']) && $data['agents']) {
            $filters[] = "(u.type = 'AGENTE')";
        }
        if (isset($data['intermediariesRegistered']) && $data['intermediariesRegistered']) {
            $filters[] = "(u.type = 'INTERMEDIARIO' AND u.rui REGEXP '^E')";
        }
        if (isset($data['employeesRegistered']) && $data['employeesRegistered']) {
            $filters[] = "(u.type = 'INTERMEDIARIO' AND u.rui REGEXP '^E' AND u.ruolo IN ('DIP_AGZ','DIP_SUBAGZ'))";
        }
        if (isset($data['employeesUnregistered']) && $data['employeesUnregistered']) {
            $filters[] = "(u.type = 'INTERMEDIARIO' AND (u.rui IS NULL OR u.rui NOT REGEXP '^E') AND u.ruolo IN ('DIP_AGZ','DIP_SUBAGZ'))";
        }
        if (isset($data['deleted']) && $data['deleted']) {
            $deleted = "u.id NOT IN (:ids)";

            $stmtParams[':ids'] = implode(',', (array)$data['deleted']);
        }

        // ADVANCED FILTERS

        if (isset($data['outcome']) && ($data['outcome']['hasPassed'] != "")) {
            if ($data['outcome']['hasPassed']) {
                $outcome = "(a.course_id = :courseId AND a.id IS NOT NULL AND a.result = 'ok')";
            } else {
                $outcome = "(a.course_id = :courseId AND a.id IS NOT NULL AND a.result in ('ko', 'none'))";
            }

            $stmtParams[':courseId'] = $data['outcome']['courseId'];
        }
        if (isset($data['participation']) && ($data['participation']['hasParticipated'] != "")) {
            if ($data['participation']['hasParticipated']) {
                $participation = "(a.course_id = :partCourseId AND a.id IS NOT NULL AND a.state = 'signedup')";

                $stmtParams[':partCourseId'] = $data['participation']['courseId'];
            } else {
                $participation = "(a.id IS NULL)";
            }
        }

        // If not exists filters the ids list is empty
        if ((count($filters) == 0) && $deleted == "") {
            return "";
        }

        // CREATE QUERY

        $sql = "SELECT DISTINCT(u.id)
                  FROM users AS u
                       LEFT JOIN tra_attendance AS a ON u.id = a.user_id
                WHERE ((u.nome IS NOT NULL) AND (u.isAzienda != 1) AND (u.active = 1))";

        if (count($filters) > 0) {
            $sql .= "AND (" . implode(' OR ', $filters) . ")";

            if ($deleted != "") {
                $sql .= " AND " . $deleted;
            }

            if ($outcome != "") {
                $sql .= " AND " . $outcome;
            }

            if ($participation != "") {
                $sql .= " AND " . $participation;
            }
        }

        if (count($filters) == 0) {
            if ($deleted != "") {
                $sql .= "AND " . $deleted;

                if ($participation != "") {
                    $sql .= " AND " . $participation;
                }

                if ($outcome != "") {
                    $sql .= " AND " . $outcome;
                }
            } elseif ($participation != "") {
                $sql .= "WHERE " . $participation;

                if ($outcome != "") {
                    $sql .= " AND " . $outcome;
                }
            } elseif ($outcome != "") {
                $sql .= "WHERE " . $outcome;
            }
        }

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($stmtParams);

        $users = $stmt->fetchAll();

        // RETRIEVE ONLY IDs

        foreach ($users as $user) {
            $ids[] = $user['id'];
        }

        if ($ids) {
            return implode(',', $ids);
        }

        return "";
    }

    public function activeCourses($agencyCode, $year = null, $ivass = 1, $tag = null)
    {
        $params = [];

        $sql = "SELECT c.*
                  FROM tra_recipient AS r
                       LEFT JOIN tra_attendance AS a ON (a.user_id = r.user_id AND r.course_id = a.course_id)
                       JOIN tra_course AS c ON c.id = r.course_id
                       JOIN users AS u ON u.id = r.user_id
                 WHERE u.agenzia_id = :agencyCode
                   AND c.ivass = :ivass
                   AND c.status = 'on'
                   AND (a.state <> 'deleted' OR a.state IS NULL) ";

        $params[':agencyCode'] = $agencyCode;
        $params[':ivass'] = $ivass;

        if ($tag) {
            $sql .= " AND c.tag LIKE :tag ";

            $params[':tag'] = $tag;
        }
        else {
            $sql .= " AND c.tag IS NULL ";
        }

        if ($year) {
            $sql .= " AND (c.year = :year) ";

            $params[':year'] = $year;
        }

        $sql .= " GROUP BY c.id
                  ORDER BY c.id DESC";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function getAvailableCoursesByAgency($agencyCode, $year = null)
    {
        $params = [];

        $sql = "SELECT c.id, c.title, c.groupamaType, c.credits, c.status, c.year
                   FROM tra_course AS c
                         JOIN tra_class AS cl ON cl.course_id = c.id
                         JOIN tra_attendance AS a ON a.class_id = cl.id
                         JOIN users AS u ON u.id = a.user_id
                  WHERE u.agenzia_id = :agencyCode ";

        $params[':agencyCode'] = $agencyCode;

        if ($year) {
            $sql .= " AND (c.year = :year) ";

            $params[':year'] = $year;
        }

        $sql .= " GROUP BY c.id
                  ORDER BY c.id DESC";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findAttachmentsByUserId($userId)
    {
        $sql = "SELECT f.*, c.title as courseName
                  FROM tra_file f
                       JOIN tra_course c ON c.id = f.course_id
                       JOIN tra_class cl ON cl.course_id = c.id
                       JOIN tra_attendance a ON a.class_id = cl.id
                 WHERE a.user_id = :userId AND a.result = 'ok' AND f.type = 'MATERIAL' AND c.ivass = 1
                 ORDER BY c.id";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([ ':userId' => $userId ]);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findPublicAttachments($userId)
    {
        $sql = "SELECT f.*, c.title
                  FROM tra_file f
                       JOIN tra_course c ON c.id = f.course_id
                       -- JOIN tra_recipient a ON a.course_id = c.id
                --  WHERE a.user_id != :userId
                   AND f.private = 0 AND c.ivass = 1
                 GROUP BY f.id
                 ORDER BY c.id";

        return Kernel::pdo()->query($sql)->fetchAll(\PDO::FETCH_OBJ);

        //$stmt = Kernel::pdo()->prepare($sql);
        //$stmt->execute([ ':userId' => $userId ]);

        //return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findAmountOfClassesCreatedInCurrentSemester($start, $end, $areaId)
    {
        $params = [
            ':start' => $start,
            ':end' => $end
        ];

        $sql = "SELECT count(1) AS count
                  FROM tra_class AS cl
                 WHERE (cl.created_at BETWEEN :start AND :end)";

        if ($areaId) {
            $sql .= " AND cl.area = :areaId ";

            $params[':areaId'] = $areaId;
        }

        //$sql .= " GROUP BY cl.id ";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findAmountOfAttendancesVerifiedInCurrentSemester($start, $end, $areaId)
    {
        $params = [
            ':start1' => $start,
            ':end1' => $end,
            ':start2' => $start,
            ':end2' => $end
        ];

        $sql = "SELECT SUM(
			      (CASE WHEN (tra_attendance.flagStrumentiUpdatedDate BETWEEN :start1 AND :end1) THEN 1 ELSE 0 END) +
			      (CASE WHEN (tra_attendance.flagDidatticaUpdatedDate BETWEEN :start2 AND :end2) THEN 1 ELSE 0 END)
			    ) AS count
                  FROM tra_class
                  JOIN tra_attendance ON tra_attendance.class_id = tra_class.id";

        if ($areaId) {
            $sql .= " WHERE tra_class.area = :areaId ";

            $params[':areaId'] = $areaId;
        }

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(\PDO::FETCH_OBJ);
    }

    public function findBy($filters)
    {
        $criteriaExp = "";

        foreach ($filters as $key => $value) {
            $criteriaExp .= "{$key},EQ,{$value}|";
        }

        $criteriaExp = substr($criteriaExp, 0, -1);

        if (empty($criteriaExp)) {
            return null;
        }

        return $this->fetchOne(null, null, $criteriaExp);
    }

    public function checkGdprAntiriciclaggio($userId, $type)
    {
        $sql = "SELECT count(1)
                  FROM tra_attendance a JOIN tra_course c ON c.id = a.course_id
                 WHERE a.user_id = :userId
                   AND a.result = 'ok'
                   AND c.$type = 1";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([ ':userId' => $userId ]);

        $checkGroupama = $stmt->fetchColumn(0);

        $sql = "SELECT count(1)
                  FROM tra_external_credits e
                 WHERE e.user_id = :userId
                   AND e.$type = 1";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([ ':userId' => $userId ]);

        $checkExternal = $stmt->fetchColumn(0);

        return $checkGroupama + $checkExternal;
    }

    public function isGdprAntiriciclaggioEditable($userId, $type)
    {
        $sql = "SELECT e.id
                  FROM tra_external_credits e
                 WHERE e.user_id = :userId
                   AND e.$type = 1";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([ ':userId' => $userId ]);

        return $stmt->fetchColumn(0);

    }

    public function findUniqumCoursesByAgency($agencyId)
    {
        $sql = "
        SELECT c.title, c.cover, c.id
            FROM tra_course c
            JOIN tra_class cl ON c.id = cl.course_id
            JOIN tra_attendance a on cl.id = a.class_id
            JOIN users u ON a.user_id = u.id
        WHERE c.tag LIKE '%UNIQUM%' AND u.agenzia_id = :agencyId
        GROUP BY c.id";

        $stmt = Kernel::pdo()->prepare($sql);
        $stmt->execute([ ':agencyId' => $agencyId ]);

        return $stmt->fetchAll(\PDO::FETCH_ASSOC);

    }

}
