<?php
namespace util\csv;
use metadigit\lib\util\csv\Exception;

class CsvProcessor extends \metadigit\lib\util\csv\CsvProcessor {
	use \metadigit\core\CoreTrait;

	/**
	 * Analyze CSV.
	 * Return array with:
	 * - array of properties
	 * - array of stats info
	 * - array of errors
	 * @param string $file file path
	 * @param string|callback $explodeFn explode function to parse CSV lines  (mus return array of values)
	 * @param bool|int $limit n° of lines to import
	 * @param bool|int $offset start line to begin with
	 * @throws Exception
	 * @return array
	 */
	function analyzeFile($file, $explodeFn=null, $limit=false, $offset=false) {
		$explodeFn = $this->initExplodeFn($explodeFn);
		if(!file_exists($file)) throw new Exception(11, [$file]);
		$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'START - File: '.$file);
		$tot = 1 + (int) exec('cat '.$file.' | wc -l');
		$ok = $i = 0;
		$data = ['rows'=>0, 'columns'=>0, 'data'=>[], 'stats'=>['startTime' => time()], 'errors'=>[]];
		$errors = [];
		$fhandle = fopen($file, 'r');
		while(!feof($fhandle) && (!$limit||($limit&&$i<$limit))) {
			$success = false;
			$i++;
			$line = str_replace(chr(10),'',fgets($fhandle, $this->maxLineLenght));
			try {
				if(empty($line) && feof($fhandle)) {
					$i--; $tot--;
					break;
				}
				if(empty($line)) {
					$data['errors'][] = ['index'=>$i, 'data'=>$line, 'errCode'=>self::ERROR_LINE_EMPTY, 'errMsg'=>'EMPTY LINE'];
					continue;
				}
				$values = call_user_func($explodeFn, $line);
				switch ($i) {
					case 1:
						$data['columns'] = count($values);
						foreach ($values as $k=>$v) {
							$data['data'][$k]['label'] = $v;
						}
						$success = true;
						break;
					case 2:
						foreach ($values as $k=>$v) {
							if(preg_match('/^[0-9]+$/', $v)) $data['data'][$k]['type'] = 'integer';
							elseif(preg_match('/^[0-9]+\.[0-9]+$/', $v)) $data['data'][$k]['type'] = 'float';
							elseif(preg_match('/^(true|false|TRUE|FALSE){1}$/', $v)) $data['data'][$k]['type'] = 'boolean';
							elseif(preg_match('/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/', $v)) $data['data'][$k]['type'] = 'date';
							elseif(preg_match('/^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$/', $v)) $data['data'][$k]['type'] = 'datetime';
							else $data['data'][$k]['type'] = 'string';
						}
						$success = true;
						break;
					default:
						continue;
				}
				if(true===$success) $ok++;
				// TERMINATE if too many errors
				if($this->errorLimit > 0 && count($errors) >= $this->errorLimit) {
					fseek($fhandle,0,SEEK_END);
					break;
				}
			} catch(\Exception $Ex) {
				$data['errors'][] = ['index'=>$i, 'data'=>$line, 'errCode'=>self::ERROR_LINE_EXCEPTION, 'errMsg'=>'EXCEPTION', 'errData'=>['code'=>$Ex->getCode(), 'msg'=>$Ex->getMessage(), 'file'=>$Ex->getFile(), 'line'=>$Ex->getLine()]];
			}
		}
		$data['rows'] = $i;
		$data['stats']['endTime'] = time();
		$data['stats']['executionTime'] = $data['stats']['endTime'] - $data['stats']['startTime'];
		$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'END - File: '.$file);
		return $data;
	}

	/**
	 * Verify configuration options
	 * @param string|callback|null $explodeFn
	 * @return callback
	 * @throws Exception
	 */
	private function initExplodeFn($explodeFn) {
		if(is_null($explodeFn)) {
			$delimiter = $this->csvDelimiter;
			$enclosure = $this->csvEnclosure;
			$escape = $this->csvEscape;
			$trim = $this->csvTrim;
			$explodeFn = function($line) use ($delimiter, $enclosure, $escape, $trim)  {
				$values = str_getcsv($line, $delimiter, $enclosure, $escape);
				if($trim) $values = array_map('trim', $values);
				return $values;
			};
		}
		if(!is_callable($explodeFn)) throw new Exception(1);
		return $explodeFn;
	}
}
