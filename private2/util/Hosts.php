<?php
namespace util;

class Hosts {
	static function getStaticHost(){
		switch(gethostname()){
			case 'groupama-pa-1':
				$staticHost = 'static.portaleagendo.it';
				break;
			case 'groupama-pa-2':
				$staticHost = 'intranet-prod.groupama.it/staticportaleagendo';
				break;
			case 'groupama-pa-dev':
				if($_SESSION['SSO'])
					$staticHost = 'intranet-test.groupama.it/staticportaleagendo';
				else
					$staticHost = 'static.test.portaleagendo.it';
				break;
			case 'dev1':
				$staticHost = 'static.test1.portaleagendo.it';
				break;
			case 'dev2':
				$staticHost = 'static.test2.portaleagendo.it';
				break;
			case 'groupama-p2-pub':
				$staticHost = 'static.new1.portaleagendo.it';
				break;
			case 'groupama-p2-pri':
				$staticHost = 'static.new2.portaleagendo.it';
				break;
			default:
				$staticHost = 'static.vbox.portaleagendo.it';
		}
		return $staticHost;
	}
}
