<?php

namespace service;

use metadigit\core\Exception,
    metadigit\core\db\orm\Repository,
    metadigit\core\session\Session,
    data\User;

class UsersService
{
    use \metadigit\core\CoreTrait, \metadigit\core\db\PdoTrait;

    const PWD_EXPIRE_DAYS = 180;
    const TOKEN_EXPIRE_MINUTES = 1440; // 24 h

    /** @var \service\AbiService */
    protected $AbiService;
    /** Mailer
     * @var \metadigit\core\mail\Mailer */
    protected $Mailer;
    /** UsersRepository
     * @var \data\UsersRepository */
    protected $UsersRepository;
    /** UsersAuthRepository
     * @var \metadigit\core\db\orm\Repository */
    protected $UsersAuthRepository;

    /**
     * @var \data\apps\formazione\Utils\SigningUp
     */
    protected $signingUp;

    /**
     * Initialize User session & cookie
     * @param object $User
     * @param boolean $regenerate
     */
    function initUserSession($User, $regenerate = true)
    {
        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__);
        // @TODO do not work with redirect // if($regenerate) @session_regenerate_id(true);
        define('SESSION_NEW_UID', $User->id);
        // set SESSION
        $SessionAUTH = new Session('AUTH', true);
        $SessionAUTH->UID = $User->id;
        $SessionAUTH->UNAME = $_SESSION['TRACE_UNAME'] = $User->nome . ' ' . $User->cognome;
        $SessionAUTH->UTYPE    = $User->type;
        $SessionAUTH->UROLE    = $User->ruolo;
        $SessionAUTH->GID    = '';
        $SessionAUTH->GNAME    = $User->type;
        $SessionAUTH->AREA    = $User->area;
        $SessionAUTH->DISTRICT    = $User->district;
        $SessionAUTH->AGENZIA    = $User->agenzia_id;
        if ($User->type == 'INTERMEDIARIO') $SessionAUTH->COD_ESAZIONE = $User->codEsazione;
        // @FIXME set SESSION['User'], needed by OLD framework code
        $data = $this->UsersRepository->toArray($User);
        $_SESSION['User'] = new \pagendo\model\User($data);
    }

    function initUserACL($User)
    {
        $_SESSION['__ACL__'] = [
            'CP' => '0000000000000',
            'admin' => false,
            'apps' => [
                'reportistica' => false,
                'tassozero' => false
            ]
        ];
        switch ($User->type) {
            case 'KA':
                $_SESSION['__ACL__']['admin']['agenzie'] = true;
                $_SESSION['__ACL__']['admin']['iniziative'] = true;
                $_SESSION['__ACL__']['admin']['news'] = true;
                $_SESSION['__ACL__']['admin']['stampa'] = true;
                $_SESSION['__ACL__']['admin']['users'] = true;
                $_SESSION['__ACL__']['apps']['reportistica'] = true;
                $_SESSION['__ACL__']['apps']['tassozero']['actionEdit'] = true;
                $_SESSION['__ACL__']['reticomp'] = true;
                $_SESSION['__ACL__']['CP']        = '1111111111111';
                $_SESSION['__ACL__']['imprendo'] = true;
                $_SESSION['__ACL__']['mypage'] = true;
                $_SESSION['__ACL__']['gotit'] = true;
                $_SESSION['__ACL__']['simvita'] = true;
				break;
            case 'MYPAGE':
                $_SESSION['__ACL__']['mypage'] = true;
                if (in_array($User->id, [42511, 46376])) $_SESSION['__ACL__']['apps']['ivass'] = true; // Giudici e di napoli hanno accesso a IVASS
                break;
            case 'DIREZ':
                if (in_array($User->id, [23037])) $_SESSION['__ACL__']['apps']['ivass'] = true; // Pompili
                if (in_array($User->id, [2515])) $_SESSION['__ACL__']['apps']['reportistica'] = true; // Buzzi
                if (in_array($User->id, [17407])) {
                    $_SESSION['__ACL__']['imprendo'] = true;
                    $_SESSION['__ACL__']['mypage'] = true;
                } // Verdirosi
                break;
            case 'AMMINISTRATORE':
                // CONTROL PANEL
                if (in_array($User->cognome, ['Bodi', 'Cannarsa', 'Cavotta', 'Di Felice', 'Marrucci', 'Vella'])) $_SESSION['__ACL__']['admin']['agenzie'] = true;
                if (in_array($User->cognome, ['Bodi', 'Cannarsa', 'Cavotta', 'Di Felice', 'Marrucci', 'Vella'])) $_SESSION['__ACL__']['admin']['iniziative'] = true;
                if (in_array($User->cognome, ['Bodi', 'Cannarsa', 'Cavotta', 'Di Felice', 'Marrucci', 'Vella'])) $_SESSION['__ACL__']['admin']['users'] = true;
                if (in_array($User->id, [2515, 69])) $_SESSION['__ACL__']['admin']['users'] = true;
                if (in_array($User->id, [74])) $_SESSION['__ACL__']['admin']['users'] = true;
                if (in_array($User->id, [74])) $_SESSION['__ACL__']['admin']['agenzie'] = true;
                if (in_array($User->cognome, ['Belli', 'Bodi', 'Buzzi', 'Cannarsa', 'Cavotta', 'Cecchini', 'Cori', 'Di Felice', 'Foresi', 'Hribal', 'INGENITO', 'Madonna', 'Marrucci', 'Mastronuzzi', 'Molella', 'Poggi', 'Puddu', 'Tonielli', 'Torti', 'Vella', 'ZAMPAGNA'])) $_SESSION['__ACL__']['admin']['news'] = true;
                if (in_array($User->cognome, ['Amici', 'Albano', 'Belli', 'Bodi', 'Buzzi', 'Cannarsa', 'Cavotta', 'Cecchini', 'Cori', 'Di Felice', 'Foresi', 'Hribal', 'INGENITO', 'Madonna', 'Mastronuzzi', 'Marrucci', 'Mastronuzzi', 'Molella', 'Poggi', 'Puddu', 'Tonielli', 'Torti', 'Vella', 'ZAMPAGNA'])) $_SESSION['__ACL__']['admin']['stampa'] = true;

                // APPS
                if (in_array($User->cognome, ['Madonna', 'Poggi'])) $_SESSION['__ACL__']['apps']['reportistica'] = true;
                if (in_array($User->cognome, ['Di Felice'])) $_SESSION['__ACL__']['apps']['tassozero']['actionEdit'] = true;
                // Rosati, Catinella, Botta, Zacchei, Puddu, Visentin, Pantano, Amici, Puccini, Torti, Elia, Cori, Simonetti, Damiata, De Martino, Mastronuzzi, Sclacione, Fabbri
                if (in_array($User->id, [2527, 2528, 14933, 15727, 73, 11574, 21880, 19172, 9227, 75, 15981, 65, 34490, 54, 11288, 18937, 38835, 48725])) $_SESSION['__ACL__']['apps']['ivass'] = true;
                if (in_array($User->id, [74, 9222, 71])) $_SESSION['__ACL__']['apps']['objeco'] = true; // Rocchi, Checchetti, Natalucci

                // PAGENDO 1.0
                if ($User->id == 41053)                 $_SESSION['__ACL__']['CP']        = '0001000000000'; // ROBOT x Gestione Contributi
                elseif (in_array($User->cognome, ['Bodi', 'Buzzi', 'Cannarsa', 'Cavotta', 'Colonna', 'Di Felice', 'Madonna', 'Marrucci', 'Poggi', 'Tonielli', 'Vella']))    $_SESSION['__ACL__']['CP']        = '1011010110111';
                elseif ($User->cognome == 'Hribal')    $_SESSION['__ACL__']['CP']        = '1001010110111';
                elseif ($User->cognome == 'Torti')        $_SESSION['__ACL__']['CP']        = '1011000100001';
                elseif ($User->cognome == 'Delamothe')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Rocchi')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Trerotoli')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Puccini')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Falcone')    $_SESSION['__ACL__']['CP']        = '1000000000001';
                elseif ($User->cognome == 'Lanza')        $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Insardi')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Zampieri')    $_SESSION['__ACL__']['CP']        = '1000000000001';
                elseif ($User->cognome == 'de Martino') $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Colonna')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Vella')        $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Bertini')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'INGENITO')    $_SESSION['__ACL__']['CP']        = '1000000100011';
                elseif ($User->cognome == 'ZAMPAGNA')    $_SESSION['__ACL__']['CP']        = '1000000100011';
                elseif ($User->cognome == 'Presta')    $_SESSION['__ACL__']['CP']        = '1000000000011';
                elseif ($User->cognome == 'Puddu')        $_SESSION['__ACL__']['CP']        = '1001010101011';
                elseif ($User->cognome == 'Belli')        $_SESSION['__ACL__']['CP']        = '1000000100001';
                elseif ($User->cognome == 'Cori')        $_SESSION['__ACL__']['CP']        = '1001000110011';
                elseif ($User->cognome == 'Cecchini')    $_SESSION['__ACL__']['CP']        = '1011000111111';
                elseif ($User->cognome == 'Camuffo')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                elseif ($User->cognome == 'Cinicolo')    $_SESSION['__ACL__']['CP']        = '1001000000001';
                else                                $_SESSION['__ACL__']['CP']        = '1000100000001';
                // Accesso OBJ ECONOMICO
                if ($User->cognome == 'Cecchetti')    $_SESSION['__ACL__']['PIAN']    = true; // Cecchetti
                elseif ($User->cognome == 'Rocchi')    $_SESSION['__ACL__']['PIAN']    = true;    // Rocchi
                elseif ($User->cognome == 'Natalucci') $_SESSION['__ACL__']['PIAN']    = true;    // Natalucci
                else                                 $_SESSION['__ACL__']['PIAN']     = false;
                // Accesso PDA
                if ($User->cognome == 'Di Felice')    $_SESSION['__ACL__']['PDA']    = true; // Di Felice
                else                                 $_SESSION['__ACL__']['PDA'] = false;

                // Veridirosi e Madonna unici AMMINISTRATORE con accesso a MyPage
                if (in_array($User->id, [17407, 69])) $_SESSION['__ACL__']['mypage'] = true;

                // Utenti che hanno richiesto accesso al simulatore vita
                if(in_array($User->id, [61])) $_SESSION['__ACL__']['simvita'] = true;

                // Banner GOTIT
                $_SESSION['__ACL__']['gotit'] = true;

                break;
            case 'AREAMGR':
                $_SESSION['__ACL__']['CP']        = '1010100100001';    // ObjEconomico + Formazione + GestContributi + News
                $_SESSION['__ACL__']['gotit'] = true;
                $_SESSION['__ACL__']['simvita'] = true;
				break;
			case 'FOR':
                if(in_array($User->id, [83])) $_SESSION['__ACL__']['apps']['ivass'] = true; // Scotti
                break;
			case 'ASV':
				$_SESSION['__ACL__']['CP']		= '1000000100001';	// News + ObjEconomico + PDA
                $_SESSION['__ACL__']['simvita'] = true;
				break;
			case 'DISTRICTMGR':
				$_SESSION['__ACL__']['CP']		= '1010000000001';	// ObjEconomico + Formazione
                $_SESSION['__ACL__']['gotit'] = true;
                $_SESSION['__ACL__']['simvita'] = true;
				break;
			case 'FORMAZIONE':
                $_SESSION['__ACL__']['gotit'] = true;
                if (in_array($User->cognome, ['Botta', 'Catinella', 'Rosati', 'Tonielli'])) $_SESSION['__ACL__']['admin']['users'] = true;
                if ($User->cognome == 'CINICOLO')        $_SESSION['__ACL__']['CP']        = '0010000000011';    // Formazione + TTS
                elseif ($User->cognome == 'Perrella')    $_SESSION['__ACL__']['CP']        = '0010000000000';    // Formazione
                else                                $_SESSION['__ACL__']['CP']        = '0010000000001';    // Formazione

                if (in_array($User->id, [15727])) {
                    //Zacchei
                    $_SESSION['__ACL__']['apps']['avanzamenti'] = true;
                    $_SESSION['__ACL__']['apps']['objeco'] = true;
                    $_SESSION['__ACL__']['PIAN']    = true;
                }
                break;
            case 'UNIVERSE':
                $_SESSION['__ACL__']['CP']        = '0010000000001';    // Formazione
                break;
            case 'PROMOTICA':
                $_SESSION['__ACL__']['CP']        = '0000011000000';    // Catalogo + Ordini
                break;
            case 'AREAMGR_COLLAB':
            case 'BANCHE':
                if (in_array($User->id, [12381, 25644])) $_SESSION['__ACL__']['apps']['ivass'] = true; // Simonetti, Candidori
                break;
            case 'FORMAZ_SIWEB':
            case 'OTHERS':
                $_SESSION['__ACL__']['CP']        = '0010000000000';    // Formazione
                break;
            case 'IVASS':
                $_SESSION['__ACL__']['CP']        = '0010000000000';    // Formazione
                break;
            case 'AUDIT':
                $_SESSION['__ACL__']['CP']        = '0010000000000';    // Formazione
                break;
            case 'INTERMEDIARIO':
                $_SESSION['__ACL__']['ELEARNING'] = $User->acl_elearning;
                break;
            case 'AGENTE':
                $_SESSION['__ACL__']['reticomp_agt'] = true;
                $_SESSION['__ACL__']['imprendo'] = true;
                $_SESSION['__ACL__']['gotit'] = true;
                $_SESSION['__ACL__']['simvita'] = true;
                break;
        }

        if (in_array($User->id, [3, 4, 6, 7, 8, 9, 10, 12, 13, 14, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 35, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 59, 61, 63, 69, 74, 76, 83, 96, 97, 2512, 2515, 2527, 2528, 2529, 2530, 2531, 2532, 2533, 2534, 2535, 2537, 9125, 9126, 9227, 9279, 10489, 10490, 11339, 11385, 11823, 12381, 13930, 14933, 16020, 16944, 17407, 17785, 17786, 17849, 17994, 18787, 18892, 18926, 19398, 20639, 21075, 21140, 21870, 21948, 23036, 23037, 23038, 23039, 55, 54, 33701])) {
            $_SESSION['__ACL__']['reticomp'] = true;
        }

        if (in_array($User->id, [69, 2515])) {
            // Madonna, Buzzi
            $_SESSION['__ACL__']['apps']['myplace'] = true;
        }

        if (in_array($User->agenzia_id, [
            // Roma
            "NB63", "G066", "N993", "G106", "G814", "G815", "G258", "ND97", "N921", "NC60", "N478", "G124", "G075", "GL46", "N268", "N384", "N058", "G980", "N852", "G063", "G023", "G420", "N537", "N312", "N442", "G133", "N957", "NC09", "N298", "N734", "G471", "G863", "N144", "G988", "G080", "NF10", "ND00", "N316", "G217", "N211", "G892", "N180", "N895", "NF40", "N173",
            // Piacenza
            "G478", "G051", "N030", "G280", "N104", "N105", "G055", "G322", "N320", "G481", "G146", "G482", "ND16", "G116", "G850", "G248", "G203", "G252", "G136", "G264", "G454", "G114", "G373", "G835", "G286", "G485", "G435", "N191", "G295", "GL45", "G266", "G343", "NB36", "G862", "G031", "GL08", "G199", "G550", "G159", "G162", "G059", "G432", "G958", "G150", "G661", "G204", "G473", "G216", "G857", "N392"
        ])) {
            $_SESSION['__ACL__']['avtop'] = true;
        }

        if (in_array($User->id, [
            3, 4, 6, 7, 8, 9, 10, 12, 13, 14, 18, 21, 24, 27, 28, 29, 30, 33, 35, 43, 44, 45, 49, 50, 52, 53, 54, 55, 59, 61, 63, 69, 72, 74, 76, 79, 83, 2512, 2527, 2529, 2530, 2531, 2532, 2533, 2535, 2537, 9125, 9126, 9227, 10397, 10489, 10490, 11385, 11386, 11601, 11823, 12381, 16020, 16944, 17051, 17994, 18942, 19345, 19745, 19922, 20639, 21140, 21870, 21948, 23037, 26352, 30247, 33583, 33701, 33755, 34421, 34706, 34872, 34914, 36371, 76, 2530, 63, 74, 9227, 9228, 10397, 2527, 11288, 19922, 19745
        ])) {
            // manca
            // rossella pulera
            // cordier
            // sitia
            // capelli
            // lausdat
            $_SESSION['__ACL__']['fam_comm'] = true;
        }

        /*if (in_array($User->agenzia_id, [
            // Elenco agenzie 'pilota' MyPage
            'N008', 'N864', 'NB39', 'G375', 'N104', 'G045', 'G031', 'N105', 'G435', 'G136', 'GL63', 'G204', 'G373', 'G454', 'N992', 'G411', 'G051', 'N532', 'GM19', 'G124', 'G637', 'G258', 'N188', 'N063', 'G287', 'G174', 'N268', 'N734', 'N445', 'GL04', 'N173', 'N144', 'G849', 'GM21', 'N138', 'G067', 'N036', 'G980', 'G050', 'G063'
        ]))
        {
            $_SESSION['__ACL__']['mypage'] = true;
        }*/

        if (in_array($User->agenzia_id, [
            "G030", "G031", "G045", "G051", "G059", "G065", "G067", "G075", "G081", "G101", "G114", "G116", "G117", "G119", "G122", "G126", "G143", "G146", "G159", "G162", "G163", "G174", "G187", "G190", "G193", "G203", "G211", "G243", "G244", "G249", "G254", "G256", "G260", "G269", "G277", "G295", "G296", "G306", "G371", "G375", "G411", "G428", "G431", "G435", "G451", "G454", "G471", "G476", "G482", "G484", "G486", "G542", "G766", "G816", "G837", "G848", "G850", "G857", "G864", "G875", "G902", "G943", "G980", "G981", "G983", "G993", "GL23", "GL60", "GL63", "GL85", "GM12", "GM15", "GM19", "GM21", "GM23", "GM28", "GM40", "GM43", "GM51", "GM53", "GM63", "M18", "M35", "N033", "N036", "N048", "N050", "N079", "N086", "N144", "N173", "N180", "N268", "N312", "N335", "N392", "N465", "N479", "N569", "N852", "N863", "N959", "N984", "N992", "NB36", "NC60", "NC70", "ND16", "ND66", "ND70", "NF22", "NF54"
        ])) {
            $_SESSION['__ACL__']['wel_azi'] = true;
        }
    }

    /*
	 * UPDATE User "active" flag
	 * @param integer $userID
	 * @param boolean $active
	 * @return boolean TRUE on success
	 */
    function setActive($userID, $active = true)
    {
        $response = false;
        // ACL
        $User = $this->UsersRepository->fetch($userID);
        $this->checkACL($User);

        // update
        switch ($active) {
            case false:
                $response = (bool) $this->pdoStExecute('UPDATE users SET active = 0 WHERE id = :id', ['id' => $userID]);
                break;
            case true:
                $response = (bool) $this->pdoStExecute('UPDATE users SET active = 1 WHERE id = :id', ['id' => $userID]);
                $UserAuth = $this->UsersAuthRepository->fetch($userID);
                if (is_null($UserAuth->login) && is_null($UserAuth->login1) && is_null($UserAuth->login2))
                    $this->setLogin($userID, null, true);
                break;
        }

        // Check if the user is attachable to the courses
        $this->signingUp->attachToCourse($User);

        if ($this->syncAbi($User) === false) {
            http_response_code(400);
            return false;
        }
        return $response;
    }

    /**
     * UPDATE User "acl_elearning" flag
     * @param integer $userID User ID
     * @param boolean $acl_elearning
     * @return array
     */
    function setElearning($userID, $acl_elearning)
    {
        $response = [];
        // ACL
        $User = $this->UsersRepository->fetch($userID);
        $this->checkACL($User);

        // check & update
        $User = $this->UsersRepository->update($userID, ['acl_elearning' => $acl_elearning]);
        $response['data'] = $this->UsersRepository->toArray($User);

        if ($this->syncAbi($User) === false) {
            http_response_code(400);
            $response['errors'][] = 'ABI sync';
        }
        return $response;
    }

    /*
	 * UPDATE User "account"
	 * @param integer $userID
	 * @param array $data
	 * @return array
	 * @throws \Exception
	 */
    function setAccount($userID, $data)
    {
        // Login Alternativo
        $data['login'] =  isset($data['login']) ? $data['login'] : null;
        // Login 1
        $data['login1'] = isset($data['login1']) ? $data['login1'] : null;
        // Login 2
        $data['login2'] = isset($data['login2']) ? $data['login2'] : null;
        $data['email'] =  isset($data['email']) ? $data['email'] : null;


        $response = [];

        $this->trace(LOG_ERR, 1, __FUNCTION__, 'SET ACCOUNT::withParameters: ' . $userID . " " , $data);
        // ACL
        $User = $this->UsersRepository->fetch($userID);
        $this->checkACL($User);
        // ACL

        if (!(bool)$this->setLogin( $userID, $data)) {
            http_response_code(400);
            $response['errors'][] = 'LOGIN';
        }
        if (!(bool)$this->setEmail($userID, $data['email'])) {
            http_response_code(400);
            $response['errors'][] = 'EMAIL';
        }

        $response['data'] = $this->UsersRepository->toArray($User);

        return $response;
    }


    /*
	 * UPDATE User "login"
	 * @param integer $userID
	 * @param array|null $loginArr
	 * @param boolean $temp assign temporary login
	 * @return boolean TRUE on success
	 * @throws \Exception
	 */
    function setLogin($userID, $loginArr = null, $temp = false)
    {

        $this->trace(LOG_ERR, 1,  __FUNCTION__, 'SET ACCOUNT::InsideLogin', $loginArr);
        $response = false;
        // ACL
        $User = $this->UsersRepository->fetch($userID);
        $this->checkACL($User);
      // Login Alternativo
        $login = isset($loginArr['login']) ? trim($loginArr['login']) : null;
        // Login 1
        $login1 = isset($loginArr['login1']) ? $loginArr['login1'] : null;
        // Login 2
        $login2 = isset($loginArr['login2']) ? $loginArr['login2'] : null;

        // check & update
        if (($login || $login1 || $login2 ) && !$temp) {
            // @INFO: LoginAlternativo - Validation
            if($login) {
              $this->trace(LOG_ERR, 1,  __FUNCTION__, 'Checking format', $login);
              if (!preg_match('/^[A-Z0-9]{7,9}$/', $login)) {
                $this->trace(LOG_ERR, 1,  __FUNCTION__, 'Invalid login format1', $login);
                throw new Exception(501, 'INVALID FORMAT');
              }
              if (preg_match('/^PA[0-9]{7}$/', $login)) {
                $this->trace(LOG_ERR, 1,  __FUNCTION__, 'Invalid login format2', $login);
                throw new Exception(501, 'INVALID FORMAT');
              }
            }
      
            // @TODO: should login1 and login2 have regex check?
            $response = (bool) $this->pdoStExecute(
                'UPDATE users_auth SET login = :login, login1 = :login1, login2 = :login2 WHERE id = :id',
                [
                    'id' => $userID,
                    'login' => $login,
                    'login1' => $login1,
                    'login2' => $login2
                ]
            );
        } elseif (is_null($login) && $temp) {
            while (!$login) {
                $login = 'PA' . str_pad($userID, 7, '0', STR_PAD_LEFT);
                if ((bool) $this->pdoStExecute('SELECT COUNT(*) FROM users_auth WHERE login = :login', ['login' => $login])->fetchColumn())
                    $login = null;
            }
            $response = (bool) $this->pdoStExecute('UPDATE users_auth SET login = :login WHERE id = :id', ['id' => $userID, 'login' => $login]);
        }

        if ($this->syncAbi($User) === false) {
            $this->trace(LOG_ERR, 1,  __FUNCTION__, 'syncAby failed', $login);
            http_response_code(400);
            return false;
        }

        return $response;
    }

    /*
	 * UPDATE User "email"
	 * @param integer $userID
	 * @param string $email
	 * @return boolean TRUE on success
	 * @throws \Exception
	 */
    function setEmail($userID, $email)
    {
        $email = strtolower($email);
        // ACL
        $User = $this->UsersRepository->fetch($userID);
        $this->checkACL($User);

        // check & update
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->trace(LOG_ERR, 1,  __FUNCTION__, 'SET EMAIL::Invalid email format', $email);
            throw new Exception(1, 'INVALID FORMAT');
        }
        $User = $this->UsersRepository->update($userID, ['email' => $email]);
        if ($User !== true && $this->syncAbi($User) === false) {
            http_response_code(400);
            return false;
        }
        return true;
    }

    /**
     * UPDATE User "password"
     * @param boolean $token
     * @param string $old_password
     * @param string $password1
     * @param string $password2
     * @return array
     * @throws \Exception
     */
    function setPassword($token, $old_password, $password1, $password2)
    {
        $response = ['success' => false, 'errCode' => 'EXCEPTION'];
        $data = $this->pdoStExecute('SELECT password, password2 FROM users_auth WHERE id = :id LIMIT 1', ['id' => SESSION_UID])->fetch(\PDO::FETCH_ASSOC);
        if (
            (isset($_SESSION['PWD_TOKEN']) && $_SESSION['PWD_TOKEN'] == $token) ||
            (password_verify($old_password, @$data['password'])) ||
            (empty($data['password']) && ($old_password == @$data['password2'] || md5($old_password) == @$data['password2']))
        ) { // OLD PASSWORD OK
            if ($password1 !== $password2) {
                $response['errCode'] = 'PASSWORDS_MISMATCH';
                $response['errors']['password1'] = 'MISMATCH';
                $response['errors']['password2'] = 'MISMATCH';
            } elseif (!preg_match('/^.{6,}$/', $password1)) {
                $response['errCode'] = 'PASSWORD_REGEX';
                $response['errors']['password1'] = 'REGEX';
                $response['errors']['password2'] = 'REGEX';
            } else {
                $ok = $this->pdoStExecute(
                    'UPDATE users_auth SET password = :password, password2 = NULL, expirePwd = :expirePwd WHERE id = :id',
                    ['id' => SESSION_UID, 'password' => password_hash($password1, PASSWORD_DEFAULT), 'expirePwd' => date('Y-m-d', time() + 60 * 60 * 24 * self::PWD_EXPIRE_DAYS)]
                )
                    ->rowCount();
                if ($ok == 1) {
                    unset($_SESSION['PWD_TOKEN']);
                    unset($response['errCode']);
                    $response['success'] = true;
                }
            }
        } else { // OLD PASSWORD invalid
            $response['errCode'] = 'WRONG_OLD_PASSWORD';
            $response['errors']['old_password'] = 'INVALID';
        }
        return $response;
    }

    /**
     * RESCUE User password with email TOKEN
     * @param string $email
     * @return array
     * @throws \Exception
     */
    function passwordRescue($email)
    {
        $response = ['success' => false, 'errCode' => 'EXCEPTION'];
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $response['errCode'] = 'EMAIL_REGEX';
        } elseif (!$User = $this->UsersRepository->fetchOne(null, null, 'email,EQ,' . $email)) {
            $response['errCode'] = 'EMAIL_MISMATCH';
        } else {
            $token = hash('sha256', rand(100000, 999999) . $email . time());
            $ok = $this->pdoStExecute(
                'UPDATE users_auth SET token = :token, expireToken = :expireToken WHERE id = :id',
                ['id' => $User->id, 'token' => $token, 'expireToken' => date('Y-m-d H:i:s', time() + 60 * self::TOKEN_EXPIRE_MINUTES)]
            )
                ->rowCount();
            if ($ok == 1) {
                $body = file_get_contents(__DIR__ . '/mail/tpl/pwd-rescue.txt');
                $body = str_replace('[nome]', $User->nome, $body);
                $body = str_replace('[cognome]', $User->cognome, $body);
                $body = str_replace('[LINK]', 'https://' . $_SERVER['SERVER_NAME'] . '/api/auth/token/' . $token, $body);
                $Message = $this->Mailer->newMessage();
                $Message->setFrom('<EMAIL>', 'PortaleAgendo')
                    ->setReplyTo('<EMAIL>')
                    ->setReturnPath('<EMAIL>')
                    ->setSubject('PortaleAgendo - recupero password')
                    ->setBody($body);
                switch (\metadigit\core\ENVIRONMENT) {
                    case 'PROD':
                        $Message->setTo($User->email);
                        break;
                    default:
                        $Message->setTo('<EMAIL>');
                }
                
                if (\metadigit\core\ENVIRONMENT !== 'DEVEL') {
                    $this->Mailer->send($Message);
                }
                unset($response['errCode']);
                $response['success'] = true;
                http_response_code(200);
            }
        }
        return $response;
    }

    /**
     * Sync User data to Abi if required
     * @param \data\User|int $UserOrId
     * @return bool|null TRUE on successfull sync, FALSE on erro, NULL if sync not required
     */
    function syncAbi($UserOrId)
    {
        // check input
        if (is_int($UserOrId)){
          $User = $this->UsersRepository->fetch($UserOrId);
        }
        else if ($UserOrId instanceof User){
          $User = $UserOrId;
        }else {
          throw new Exception('required int $userId or \data\User as param');
        }

        // validate rules
        if (!$User->acl_elearning) return null;
        // 2020-04-20 NEO abilitati su ABI // && $User->ruolo != 'NEO'
        if (!in_array($User->type, ['AREAMGR', 'DISTRICTMGR', 'ASV', 'AGENTE', 'INTERMEDIARIO'])) return null;
        if (strtoupper($User->rui[0]) == 'B') return null;

        // @INFO: sync ABI doesn't work in develop
        if (\metadigit\core\ENVIRONMENT === 'DEVEL') {
            return true;
        }
        return $this->AbiService->registerUser($User->id);
    }

    /**
     * @param User $User
     * @throws Exception
     */
    protected function checkACL($User)
    {
        if ($_SESSION['AUTH']['UTYPE'] == 'KA')
            return;
        if ($_SESSION['AUTH']['UTYPE'] == 'AMMINISTRATORE' && $_SESSION['__ACL__']['admin']['users'])
            return;
        if ($_SESSION['AUTH']['UTYPE'] == 'AGENTE' && $_SESSION['AUTH']['AGENZIA'] == $User->agenzia_id && $User->type == 'INTERMEDIARIO')
            return;
        throw new Exception(401, 'ACL NOT AUTHORIZED');
    }
}
