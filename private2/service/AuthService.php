<?php
namespace service;
use metadigit\core\Exception,
	metadigit\core\http\Request,
	metadigit\core\http\Response,
	metadigit\core\web\DispatcherEvent;
use metadigit\core\mail\Mailer;

class AuthService {
	use \metadigit\core\CoreTrait, \metadigit\core\db\PdoTrait;

	const HASH = 'AKDIJE762JKIO567FUIQW';
	const HASH_CONTAPUNTI = 'Roidxo992sfoq002';
	const HASH_ONTEAM = 'Roidxo992sfoq002';
	const SECRET_IMPRENDO = 'VyyfzZHQGm8Fj90QZJaG';
	const IV_IMPRENDO = '2B70Vu6r';
    const SECRET_GOTIT = 'syG6123__?1s0#/s3s.K';
	const IV_GOTIT = '2B401u6r';
	const URL_COMUNICANDO = 'http://www.portalecomunicando.it/login.php';
	const URL_FOCUSCLIENTE = 'http://www.focuscliente.com/';
	const URL_PRESENTAMICI_2014 = 'http://www.presentamici.it/loginAgenzia.php';
	const URL_PRESENTAMICI_2015 = 'http://www.presentamici2015.it/loginAgenzia.php';

	const SSO_MISSING	= 0;
	const SSO_OK		= 1;
	const SSO_KO		= 2;
	const SSO_ICEBOX	= 3;
	const SSO_SUPER_TOKEN = 'H38X90P13TEH744';

	/** AppAuthRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $AppAuthRepository;
	/** @var \service\UsersService */
	protected $UsersService;
	/** UsersRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $UsersRepository;
	/** UsersAuthRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $UsersAuthRepository;
	/** UsersIceboxRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $UsersIceboxRepository;

    /**
     * @var Mailer
     */
    protected $mailer;


	/**
	 * Authentication & Security interceptor:
	 * - XSRF protection
	 * @param DispatcherEvent $DispatcherEvent
	 */
	function checkAuth(DispatcherEvent $DispatcherEvent) {
		$Req = $DispatcherEvent->getRequest();
		$Res = $DispatcherEvent->getResponse();
		$APP = $Req->getAttribute('APP');

		// check XSRF-TOKEN
		if(isset($_SESSION['XSRF-TOKEN'])) {
			TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'CHECK AUTH xsrf');
			//if($APP == 'UI') return;
			$token = $Req->getHeader('X-XSRF-TOKEN');
			if($token == $_SESSION['XSRF-TOKEN'])
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'XSRF-TOKEN match: OK');
			else {
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'XSRF-TOKEN not valid: BLOCK ACCESS');
// @FIXME		http_response_code(401);
// @FIXME		$DispatcherEvent->stopPropagation();
			}
		} else {
			TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'CHECK AUTH no xsrf');
			$token = md5(uniqid(rand(1,999)));
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'initialize XSRF-TOKEN: '.$token);
			setcookie('XSRF-TOKEN', $token);
			$_SESSION['XSRF-TOKEN'] = $token;
		}

		// check AUTH
		if(isset($_SESSION['AUTH']) || $APP == 'AUTH' || $Req->URI()=='/api/users/abi-api-call-test') return;

		// check SSO
		switch ($this->checkAuthSSO($Req, $Res)) {
			case self::SSO_MISSING:
				if($APP != 'UI') {
					TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'SSO missing & AUTH not valid: BLOCK ACCESS');
					http_response_code(401);
					$DispatcherEvent->stopPropagation();
				}
				break;
			case self::SSO_OK:
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'SSO: OK');
				break;
			case self::SSO_ICEBOX:
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'SSO: ICEBOX');
				if($APP == 'UI') $Res->redirect('/sso-icebox.html', 302);
				else http_response_code(401);
				$DispatcherEvent->stopPropagation();
				break;
			case self::SSO_KO:
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'SSO failed: BLOCK ACCESS');
				http_response_code(401);
				$DispatcherEvent->stopPropagation();
				break;
		}
		//TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'da fak?', print_r($this->sso($DispatcherEvent), 1));
	}

	/**
	 * Remote services tokens:
	 * - Comunicando
	 * - ContaPunti
	 * - FocusCliente
	 * - eLearning
	 * - GeviService
	 * - Presentamici
	 *
	 * @param integer $userID User ID
	 * @return array
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function servicesTokens($userID) {
		$tokens = [];
		$User = $this->UsersRepository->fetch($userID);
		$UserAuth = $this->UsersAuthRepository->fetch($userID);

		$queryFn = function($HASH, $params=[]) use ($User) {
			return http_build_query(array_merge(
				[
					'hash'	=> md5($User->id.$HASH.$User->type.$User->agenzia_id),
					'id'	=> $User->id,
					'nome'	=> $User->nome,
					'cognome'=> $User->cognome,
					'agenzia'=> $User->agenzia_id,
					'type'	=> $User->type,
					'ghost'	=> isset($_SESSION['GHOST']) ? 1:0
				], $params));
		};

		// Comunicando, FocusCliente, Presentamici 2014/2015
		if(in_array($User->type, ['KA','AMMINISTRATORE','AREAMGR','DISTRICTMGR','AGENTE', 'MYPAGE'])) {
			$tokens['comunicando'] = $queryFn(self::HASH);
		}
		//$tokens['focuscliente'] = $queryFn(self::HASH);
		//$tokens['presentamici2014'] = $queryFn(self::HASH);
		//$tokens['presentamici2015'] = $queryFn(self::HASH);
		//$tokens['presentamici2015'] .= ($User->type == 'AGENTE' && !isset($_SESSION['GHOST'])) ? '&utm_source=int_dp&utm_medium=banner_490x230&utm_content=presentamici&utm_campaign=2015' : '&utm_source=int_dp_ghost&utm_medium=banner_320x150&utm_content=presentamici&utm_campaign=2015';

		// ContaPunti
		switch ($User->type) {
			case 'AMMINISTRATORE':
				if(in_array($User->id, [61,63,69,70,74,76,10397]))
					$tokens['contapunti'] = $queryFn(self::HASH_CONTAPUNTI, ['email'=>$User->email]);
				break;
			case 'AREAMGR':
				if(in_array($User->id, [45,53,59,10489,11187,16944,20639]))
					$tokens['contapunti'] = $queryFn(self::HASH_CONTAPUNTI, ['email'=>$User->email]); break;
			case 'DISTRICTMGR':
				if(in_array($User->id, [3,4,6,7,8,9,10,12,13,14,18,24,27,28,29,30,33,35,43,44,49,50,52,2512,9125,9126,10490,11823,13930,16020,17994,19345,21948,22729,26352,30247,33755,34706,34914,38700]))
					$tokens['contapunti'] = $queryFn(self::HASH_CONTAPUNTI, ['email'=>$User->email]); break;
			case 'AGENTE':			$tokens['contapunti'] = $queryFn(self::HASH_CONTAPUNTI); break;
			case 'INTERMEDIARIO':	$tokens['contapunti'] = $queryFn(self::HASH_CONTAPUNTI, ['codEsazione'=>$User->codEsazione]); break;
		}

		// OnTeam
		$queryFnOnTeam = function($HASH, $param) use ($User) {
			return http_build_query([
				'hash'	=> md5($User->id.$HASH.$User->type.$param),
				'id'	=> $User->id,
				'nome'	=> $User->nome,
				'cognome'=> $User->cognome,
				'type'	=> $User->type,
				'ghost'	=> isset($_SESSION['GHOST']) ? 1:0
			]);
		};
		switch ($User->type) {
			case 'AMMINISTRATORE':	$tokens['onteam'] = $queryFnOnTeam(self::HASH_ONTEAM, null); break;
			case 'AREAMGR':			$tokens['onteam'] = $queryFnOnTeam(self::HASH_ONTEAM, $User->area).'&area='.$User->area; break;
			case 'DISTRICTMGR':		$tokens['onteam'] = $queryFnOnTeam(self::HASH_ONTEAM, $User->district).'&district='.$User->district; break;
			case 'AGENTE':			$tokens['onteam'] = $queryFnOnTeam(self::HASH_ONTEAM, $User->agenzia_id).'&agenzia='.$User->agenzia_id; break;
		}

		// ABI e-learning
		if($User->acl_elearning) {
			$tokens['elearning'] = true;
		}

		// RetiComplementari
		if (isset($_SESSION['__ACL__']['reticomp']) || isset($_SESSION['__ACL__']['reticomp_agt'])) {
			$tokens['reticomp'] = $queryFn(self::HASH);
		}

		// SimpleAgent
		$tokens['simpleagent'] = 'type='.$User->type.'&codeage='.$User->agenzia_id;

		// AVTOP
		if (isset($_SESSION['__ACL__']['avtop'])) {
			$tokens['avtop'] = $queryFn(self::HASH);
		}

        // Imprendo
        $queryFnImprendo = function($secret, $iv) use ($User) {
            $active = $User->active ? 1 : 0;
            // Replace @ with # in email to avoid conflict during md5
            //$email = str_replace("@","#", $User->email);
            $base = "{$User->type}/{$User->agenzia_id}/{$User->id}/{$User->nome}/{$User->cognome}/{$User->email}/{$active}";
            $base = $base . "::#::" . md5($base);
            $base = openssl_encrypt($base, "blowfish", $secret, false, $iv);
            $token = urlencode($base);
            return http_build_query([
                'token'	=> $token,
            ]);
        };

        if (isset($_SESSION['__ACL__']['imprendo'])) {
            $tokens['imprendo'] = $queryFnImprendo(self::SECRET_IMPRENDO, self::IV_IMPRENDO);
        }

        if (isset($_SESSION['__ACL__']['gotit'])) {
            $tokens['gotit'] = $queryFnImprendo(self::SECRET_GOTIT, self::IV_GOTIT);
        }

        if (isset($_SESSION['__ACL__']['simvita'])) {
            $tokens['simvita'] = $queryFnImprendo(self::SECRET_GOTIT, self::IV_GOTIT);
        }

		return $tokens;
	}

	/**
	 * @param Request $Req
	 * @param Response $Res
	 * @return int one of SSO_* values: SSO_OK/SSO_KO/SSO_ICEBOX
	 */
	protected function checkAuthSSO(Request $Req, Response $Res) {
		try {
			// Get SSO input OR fail
			$login = $Req->getHeader('iv-user');
			if(!$login && $Req->get('token') === self::SSO_SUPER_TOKEN)
				$login = $Req->get('iv-user');
			if(empty($login)) {
				$_SESSION['SSO'] = false;
				return self::SSO_MISSING;
			} else
				$_SESSION['SSO'] = true;

			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'SSO login: '.$login, $Req->get('iv-user'));

			// existing User
			if ($userID = $this->findUserSSO($login)) {
				$this->authenticate($userID);
				return self::SSO_OK;
			}

			// already ice-boxed User
			if ($icebox = $this->pdoStExecute('SELECT COUNT(*) FROM users_icebox WHERE login = :login LIMIT 1', ['login'=>$login])->fetch(\PDO::FETCH_COLUMN)) {
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'User already ice-boxed: '.$login);
				return self::SSO_ICEBOX;
			}

			// unknown User, put into ice-box
			$API = new GroupamaProfileManager();
			if (! $userData = $API->userProfile($login)) {
				// @TODO maybe something different ?
				return self::SSO_KO;
			} else {
				$userData['createdAt'] = date(DATE_ISO8601);
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'USER DATA', print_r($userData, true));
				$this->UsersIceboxRepository->insert(null, $userData);
				return self::SSO_ICEBOX;
			}
		} catch(Exception $Ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, 'SSO Error', print_r($Ex->getTraceAsString(), true));
			return self::SSO_KO;
		}
	}

    protected function findUserSSO($login) {
        try {
            $users = $this
                ->pdoStExecute(
                    'SELECT id,active,agenzia_id,email,area,district FROM vw_users_auth WHERE active=1 AND agenzia_id IS NOT NULL AND (login = :login OR login1 = :login1 OR login2 = :login2)',
                    ['login'=>$login, 'login1'=>$login, 'login2'=>$login]
                )->fetchAll();

            $count = $users ? count($users) : 0;

            if ($count == 1) {
                $fields = [ 'email', 'area', 'district' ];

                array_walk($fields, function($value) use ($users, $login){
                    if (! isset($users[0][$value]) || ! $users[0][$value]) {
                        $this->log("WARNING: $value non impostato per utenza $login");
                    }
                });

                return $users[0]['id'];
            }

            $this->log("SSO Failure: trovati $count utenti con login $login", true);
        } catch (\Exception $ex) {

        }

        return null;
    }

	/**
	 * Authenticate User and start Session
	 * @param $userID
	 * @return bool
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function authenticate($userID) {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'User ID: '.$userID);
		$User = $this->UsersRepository->fetch($userID);
		$this->UsersService->initUserSession($User);
		$this->UsersService->initUserACL($User);
		// set cookie for GeviService & MarketingService auto-login [sessionID(26) + userID(6) + timestamp(10)]
		$cookie = session_id() . str_pad(SESSION_NEW_UID, 6, '0', STR_PAD_LEFT) . time();
		setcookie('__crud', $cookie, 0, '/');
		setcookie('__gevi', $cookie, 0, '/');
		unset($_SESSION['XSRF-TOKEN']);
		return true;
	}

	protected function log($data, $mail = false) {
		$text = date('Y-m-d H:i:s') . ": " . print_r($data, true);
		file_put_contents("/srv/portaleagendo.it/data/logs/sso.log", "\n$text", FILE_APPEND);

        if ($mail) {
            $message = $this->mailer->newMessage()
                ->setSubject("Anomalia MyPlace SSO")
                ->setBody($data)
                ->setFrom(['<EMAIL>'=>'Agendo'])
                ->setTo(['<EMAIL>', '<EMAIL>']);

            $this->mailer->send($message);
        }
	}

	public function checkApiAuth(DispatcherEvent $event) {
		$request = $event->getRequest();

		if (! $token = $request->getHeader('X-AUTH-TOKEN')) {
			TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'Empty X-AUTH-TOKEN');
			http_response_code(400);
			return $event->stopPropagation();
		}

		// Load app.
		if (! $app = $this->AppAuthRepository->fetchOne(null, null, "token,EQ,$token")) {
			http_response_code(401);
			return $event->stopPropagation();
		}

		if ($app->tokenExpires < time()) {
			TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'Token expired');
			http_response_code(401);
			return $event->stopPropagation();
		}

		// @TODO Hash token?
		/*if (! password_verify($token, $app->token)) {
			TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'Invalid X-AUTH-TOKEN');
			http_response_code(401);
			return $event->stopPropagation();
		}*/
	}

	public function makeApiToken($httpXAuth) {
		if (! $credentials = $this->getApiCredentials($httpXAuth)) {
			return null;
		}

		if (! $app = $this->AppAuthRepository->fetchOne(null, null, "appId,EQ,{$credentials[0]}")) {
			return null;
		}

		if (! password_verify($credentials[1], $app->appKey)) {
			return null;
		}

		// Generate token.
		$token = sha1((time() / rand(1, 256)) . $app->appId . uniqid());
		$tokenExpires = time() + 86400;

		// Update app.
		$app->token = $token;
		$app->tokenExpires = $tokenExpires;
		$this->AppAuthRepository->update($app->id, $app);

		return (object)compact('token', 'tokenExpires');
	}

	/**
	 * @param string $httpXAuth
	 * @return array [$appId, $appKey]
	 */
	public function getApiCredentials($httpXAuth) {
		if (! $credentials = $httpXAuth) {
			return null;
		}

		if (! $credentials = base64_decode($credentials)) {
			return null;
		}

		if (count($credentials = explode(":", $credentials)) != 2) {
			return null;
		}

		return $credentials;
	}

	function fixture() {
		require_once(__DIR__.'/../fixture.php');
	}

    /**
     * Disable modifies for AREAMGR_DEP
     *
     * @param DispatcherEvent $dispatcherEvent
     */
    public function checkAreaMngDep(DispatcherEvent $dispatcherEvent)
    {
        $request = $dispatcherEvent->getRequest();
        if ($_SESSION['AUTH']['UTYPE'] === 'AREAMGR' && $_SESSION['AUTH']['UROLE'] === 'DEPUTY') {

            $requestUri = $_SERVER['REQUEST_URI'];

            $allowedUri = [
                '/api/auth/login',
                '/api/auth/logout',
                '/api/apps/contributi/contributi'
            ];

            $patternsUri = [
                [ 'pattern' => '/^\/api\/apps\/contributi\/contributi\/\d+$/', 'method' => 'PUT' ]
            ];

            $isValidPatternUri = false;
            foreach ($patternsUri as $patternUri) {
                if (preg_match($patternUri['pattern'], $requestUri) && $request->getMethod() === $patternUri['method']) {
                    $isValidPatternUri = true;
                    break;
                }
            }

            if (!$isValidPatternUri && $request->getMethod() !== 'GET' && !in_array($requestUri, $allowedUri, true)) {
                http_response_code(403);
                return $dispatcherEvent->stopPropagation();
            }
        }
    }
}
