<?php
namespace service;

use const metadigit\core\ENVIRONMENT;
use const metadigit\core\UPLOAD_DIR;

class AbiService {
	use \metadigit\core\CoreTrait, \metadigit\core\db\PdoTrait;

	const ABI_API_URL = [
		'PROD'	=> 'https://groupama.forassurance.it/webservice/rest/server.php',
		'DEV' 	=> 'https://groupamadev.forassurance.it/webservice/rest/server.php',
		'DEV1'	=> 'https://test1.portaleagendo.it/api/webservices/abi/api-self-test',
		'DEV2'	=> 'https://test2.portaleagendo.it/api/webservices/abi/api-self-test',
		'VBOX'	=> 'https://vbox.portaleagendo.it/api/webservices/abi/api-self-test'
	];

	const METHOD_USER_REGISTER = [
		'_'	=> 'USER_REG',
		'wsfunction'	=> 'local_wm_agendo_registra_utente',
		'token' => [
			'PROD'	=> 'dcf105bd39f385dfa6ae3edb46c309da',
			'DEV'	=> 'e46d174469112e82fc239781db939608'
		]
	];
	const METHOD_USER_DELETE = [
		'_' => 'USER_DEL',
		'wsfunction' => 'local_wm_agendo_elimina_utente',
		'token' => [
			'PROD' => '',
			'DEV' => ''
		]
	];
	const METHOD_USER_SSO = [
		'_' => 'USER_SSO',
		'wsfunction' => 'auth_userkey_request_login_url',
		'token' => [
			'PROD' => '59472be8fa2209034bfd80c01a38ff71',
			'DEV' => 'ce9b1a3a2ce5e2031daf9da479662f59'
		]
	];
    const METHOD_PARTECIPATION_REPORT = [
        '_' => 'PARTECIPATION_REPORT',
        'wsfunction' => 'local_wm_agendo_report_partecipazione',
        'token' => [
            'PROD' => 'dcf105bd39f385dfa6ae3edb46c309da', //'ebe37eaa8fa331cf4dbea026178c5331',
            'DEV' => 'e46d174469112e82fc239781db939608'
        ]
    ];

	/** @var \metadigit\core\db\orm\Repository */
	protected $UsersRepository;
	/** @var \metadigit\core\db\orm\Repository */
	protected $UsersAuthRepository;

	protected $debug = [
		'code'		=> 0,
		'params'	=> [],
		'response'	=> ''
	];

	function debug() {
		return $this->debug;
	}

	/**
	 * Moodle API: registra_utente
	 * @return boolean
	 */
	function registerUser($userId) {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [START] ABI API call ==========================');
		try {
			$User = $this->UsersRepository->fetch($userId);
			$UserAuth = $this->UsersAuthRepository->fetch($userId);

			$params = [
				'id_utente_agendo'	=> $userId,
				'nome'				=> $User->nome,
				'cognome'			=> $User->cognome,
				'email'				=> $User->email,
				'department'		=> $User->type
			];
			if(!is_null($User->agenzia_id)) $params['numero_agenzia'] = $User->agenzia_id;
			if($UserAuth) {
				if(!is_null($UserAuth->login) && substr($UserAuth->login,0,2)!='PA') $params['matricola'] = $UserAuth->login;
				elseif(!is_null($UserAuth->login2) && substr($UserAuth->login2,0,2)!='PA') $params['matricola'] = $UserAuth->login2;
				elseif(!is_null($UserAuth->login1) && substr($UserAuth->login1,0,2)!='PA') $params['matricola'] = $UserAuth->login1;
			}

			$response = $this->callAPI(self::METHOD_USER_REGISTER, $userId, $params);

			if(isset($response['result']) && $response['result'] == 'success')
				return true;
			else {
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'API response FAILURE', '<code>' . print_r($response, true) . '</code>');
				return false;
			}
		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
			return false;
		} finally {
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [END] ABI API call ==========================');
		}
	}

	/**
	 * Moodle API: elimina_utente
	 * @return boolean
	 */
	function deleteUser($userId) {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [START] ABI API call ==========================');
//		return false;
		try {
			$params = [
				'id_utente_agendo' => $userId
			];
			$response = $this->callAPI(self::METHOD_USER_DELETE, $userId, $params);

			if(isset($response['result']) && $response['result'] == 'success')
				return true;
			else {
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'API response FAILURE', '<code>' . print_r($response, true) . '</code>');
				return false;
			}
		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
			return false;
		} finally {
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [END] ABI API call ==========================');
		}
	}

	/**
	 * Moodle API: auth_userkey_request_login_url
	 * @return string|boolean
	 */
	function userSSO($userId) {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [START] ABI API call ==========================');
		try {
			$params = [
				'user' => [
					'username' => $userId
				]
			];
			$response = $this->callAPI(self::METHOD_USER_SSO, $userId, $params);

			if(isset($response['loginurl']))
				return $response['loginurl'];
			else {
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'API response FAILURE', '<code>' . print_r($response, true) . '</code>');
				return false;
			}
		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
			return false;
		} finally {
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [END] ABI API call ==========================');
		}
	}

    /**
     * Moodle API: report_partecipazione
     * @return string|boolean
     */
    function partecipationReport($from, $to)
    {
        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [START] ABI API call ==========================');
        try {
            $params = [
                'daDataCompletamento' => $from,
                'aDataCompletamento' => $to
            ];
            $response = $this->callAPI(self::METHOD_PARTECIPATION_REPORT, 0, $params);

            if (isset($response['corsi']))
                return $response['corsi'];
            else {
                TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'API response FAILURE', '<code>' . print_r($response, true) . '</code>');
                return false;
            }
        } catch (\Exception $ex) {
            TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
            return false;
        } finally {
            TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, '======= [END] ABI API call ==========================');
        }
    }

	/**
	 * @param array $params
	 */
	protected function callAPI($method, $userId, $params) {
		// API endpoint switch
//		$ABI_API_URL = self::ABI_API_URL['VBOX'];
		if (ENVIRONMENT == 'PROD') {
            $ABI_API_URL = self::ABI_API_URL['PROD'];
            // @HACK: mail del 30/05/25 per risoluzione problema timeout su caricamento corsi elearning
            if ($method['_'] === "PARTECIPATION_REPORT") {
                $ABI_API_URL = str_replace('https://groupama.forassurance.it', 'https://gpama-api.forassurance.it', $ABI_API_URL);
            }
		}
//		elseif (gethostname() == 'dev1')
//			$ABI_API_URL = self::ABI_API_URL['DEV1'];
//		elseif (gethostname() == 'dev2')
//			$ABI_API_URL = self::ABI_API_URL['DEV2'];
		// FORCE REMOTE DEV ENVIRONMENT (groupamadev.forassurance.it)
		else
			$ABI_API_URL = self::ABI_API_URL['DEV'];

		// setup curl
		$API_QUERY = http_build_query([
			'wsfunction'	=> $method['wsfunction'],
			'wstoken'		=> (ENVIRONMENT == 'PROD') ? $method['token']['PROD'] : $method['token']['DEV'],
			'moodlewsrestformat' => 'json'
		]);
		$ch = curl_init($ABI_API_URL.'?'.$API_QUERY);
		curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // force IPv4
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3); //timeout in seconds
		curl_setopt($ch, CURLOPT_TIMEOUT, 600); //timeout in seconds
//		curl_setopt($ch, CURLOPT_HEADER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, [
			'Content-Type: application/x-www-form-urlencoded',
			'accept: application/json'
		]);
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));

		// call CURL
//      $execTime = -\hrtime(true);
		$execTime = -microtime(true);
		$response = curl_exec($ch);
		$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
//		$execTime += \hrtime(true);
		$execTime += microtime(true);

        // Stampo JSON RAW fornito da WEMOLE
        if ( $method["_"] === "PARTECIPATION_REPORT" ) {
            $filename = UPLOAD_DIR . "acquisizione_wemole-{$params["daDataCompletamento"]}-{$params["aDataCompletamento"]}.json";
            $file = fopen($filename, 'w');
            $encodedData = json_encode($response);
            fwrite($file, $encodedData);
            fclose($file);
        }

		// parse response
        $response = (array) json_decode($response);
		switch ($http_code) {
			case 200:
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'API response', '<code>' . print_r($response, true) . '</code>');
				break;
			default:
				TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'API response ERR', '<code>' . print_r($response, true) . '</code>');
		}

		// debug info
		$this->debug = [
			'code'		=> $http_code,
			'params'	=> $params,
			'response'	=> $response
		];
		// debug log
		$this->pdoStExecute('INSERT INTO sys_logs_abi (date, status, method, execTime, user_id, params, response) VALUES (NOW(3), :status, :method, :execTime, :user_id, :params, :response)', [
			'status' => ($http_code == 200 && !isset($response['exception'])) ? 'OK' : 'ERR',
			'method' => $method['_'],
			'execTime' => $execTime,
			'user_id' => $userId,
			'params' => json_encode($params),
			'response' => json_encode($response)
		]);

		return $response;
	}
}
