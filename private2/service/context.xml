<?xml version="1.0" encoding="UTF-8"?>
<context namespace="service">
	<includes>
		<include namespace="system"/>
		<include namespace="data"/>
		<include namespace="data.pd"/>
	</includes>
	<objects>
		<object id="service.AbiService" class="service\AbiService">
			<properties>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
				<property name="UsersAuthRepository" type="object">data.UsersAuthRepository</property>
			</properties>
		</object>
		<object id="service.AuthService" class="service\AuthService">
			<properties>
				<property name="AppAuthRepository" type="object">data.AppAuthRepository</property>
				<property name="UsersService" type="object">service.UsersService</property>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
				<property name="UsersAuthRepository" type="object">data.UsersAuthRepository</property>
				<property name="UsersIceboxRepository" type="object">data.UsersIceboxRepository</property>
				<property name="mailer" type="object">system.Mailer</property>
			</properties>
		</object>
		<object id="service.BrowscapService" class="service\BrowscapService"/>
		<object id="service.GroupamaPDNotificator" class="service\GroupamaPDNotificator"/>
		<object id="service.GroupamaPDCalendar" class="service\GroupamaPDCalendar"/>
		<object id="service.GroupamaUtils" class="service\GroupamaUtils"/>
		<object id="service.StatsService" class="service\StatsService"/>
		<object id="service.UsersService" class="service\UsersService">
			<properties>
				<property name="AbiService" type="object">service.AbiService</property>
				<property name="Mailer" type="object">system.Mailer</property>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
				<property name="UsersAuthRepository" type="object">data.UsersAuthRepository</property>
				<property name="signingUp" type="object">data.apps.formazione.Utils.SigningUp</property>
			</properties>
		</object>
	</objects>
</context>
