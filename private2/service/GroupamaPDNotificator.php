<?php
namespace service;

use const metadigit\core\ENVIRONMENT,
metadigit\core\LOG_DIR;
use data\Agenzia;

class GroupamaPDNotificator {
	use \metadigit\core\db\PdoTrait, GroupamaAuthTrait;

	const GA_API_ASYNC_MESSAGE_URL = 'https://api-prod.groupama.it/asyncMessage/1/gaAMPublishMessage';
	const GA_API_DEV_ASYNC_MESSAGE_URL = 'https://api-coll.groupama.it/asyncMessage/1/gaAMPublishMessage';

	const GA_PAGENDO_URL = 'https://intranet-prod.groupama.it/portaleagendo/';
	const GA_PAGENDO_DEV_URL = 'https://intranet-test.groupama.it/portaleagendo/';

	const XML = <<<XML
<gaAMMessage name="{EVENT_NAME}" sender="AGENDO">
	<payload>{PAYLOAD}</payload>
	<type>application/json</type>
</gaAMMessage>
XML;
	const JSON = [
		'event' => [
			'source' => 'AGENDO',
			'sourceDescription' => 'PORTALE AGENDO',
			'categoryCode' => '',
			'categoryDescription' => '',
			'eventCode' => '',
			'eventCodeDescription' => ''
		],
		'messageText' => [
			'text' => '',
			'shortDescription' => ''
		],
		'recipients' => [
			['recipientCode' => '']
		]
	];

	/**
	 * @param integer $id
	 * @param $name
	 * @param $url
	 * @param array $agenzie
	 */
	function incentivazioneStart($id, $name, $url, array $agenzie) {
		foreach ($agenzie as $agz) {
			$this->enqueue('incentiv', 'start', null, $agz,
				'Incentivazioni',
				sprintf('Nuova incentivazione %s', $name),
				'L\'incentivazione ' . $name . ' è ONLINE' .
				'<br/><a href="' . $this->url('#/incentivazioni/' . $id . '-' . $url . '/') . '" target="_blank">Accedi</a>'
			);
		}
	}

	/**
	 * @param integer $id
	 * @param string $name
	 * @param string $url
	 * @param string $agenziaID
	 * @param string $message
	 */
	function incentivazioneUpdate($id, $name, $url, $agenziaID, $message) {
		$this->enqueue('incentiv', 'update', null, $agenziaID,
			'Incentivazioni',
			sprintf('%s è stata aggiornata', $name), // 2020-04-21 removed custom message
			/*$message.*/ '<br/><a href="' . $this->url('#/incentivazioni/' . $id . '-' . $url . '/') . '" target="_blank">Accedi</a>'
		);
	}

	function avanzamentiUpdate($userId, $agenziaId, $data) {
		$month = $this->month($data['month']);
		$this->enqueue('avanzamenti', 'update', $userId, $agenziaId,
			'Avanzamenti Rappel',
			"Aggiornamento degli avanzamenti rappel al mese di $month",
			'I nuovi dati di ' . $month . ' sono disponibili in Agendo.' .
			'<br/><a href="' . $this->url('#/apps/avanzamenti/agenzia/' . $agenziaId) . '" target="_blank">Accedi</a>'
		);
	}

	public function month($month) {
		$month = intval($month);

		return [
			'',
			'Gennaio',
			'Febbraio',
			'Marzo',
			'Aprile',
			'Maggio',
			'Giugno',
			'Luglio',
			'Agosto',
			'Settembre',
			'Ottobre',
			'Novembre',
			'Dicembre',
		][$month];
	}

	/**
	 * Store notification into the queue (DB)
	 * @param string $app
	 * @param string $event
	 * @param integer $userID
	 * @param string $agenziaID
	 * @param string $header
	 * @param string $title
	 * @param string $text
	 */
	protected function enqueue($app, $event, $userID, $agenziaID, $header, $title, $text) {
		static $pdoStatement;
		if (!$pdoStatement)
			$pdoStatement = $this->pdoPrepare('INSERT INTO pd_notifications (app, event, user_id, agenzia_id, header, title, text) VALUES (:app, :event, :user_id, :agenzia_id, :header, :title, :text)');
		$pdoStatement->execute([
			'app' => strtoupper($app),
			'event' => strtoupper($event),
			'user_id' => $userID,
			'agenzia_id' => $agenziaID,
			'header' => $header,
			'title' => $title,
			'text' => $text
		]);
	}

	/**
	 * Call API AsyncMessage
	 * @param string $app
	 * @param string $event
	 * @return boolean|null
	 */
	function publish($app, $event) {
		static $pdoStatement;
		$app = strtoupper($app);
		$event = strtoupper($event);

		if (!$this->doAuth())
			return null;
		try {
			if (!$pdoStatement)
				$pdoStatement = $this->pdoPrepare('SELECT * FROM pd_notifications WHERE status = \'QUEUE\' AND app = :app AND event = :event');
			$pdoStatement->execute(['app' => $app, 'event' => $event]);
			$data = $pdoStatement->fetchAll(\PDO::FETCH_ASSOC);
			$i = 0;
			foreach ($data as $d) {
				$i++;

				// build JSON message
				$JSON = self::JSON;
				$JSON['event']['categoryCode'] = $d['app'];
				$JSON['event']['categoryDescription'] = $d['header'];
				$JSON['event']['eventCode'] = $d['app'] . '_' . $d['event'];
				$JSON['event']['eventCode'] = $d['app'] . '_' . $d['event'];
				/**
				 * NOT USED
				 * if(isset($d['url'])) {
				 * $JSON['event']['propertyKeys']['link_label_pa'] = 'click';
				 * $JSON['event']['propertyKeys']['link_url_pa'] = 'https://www.portaleagendo.it/'.$d['url'];
				 * }
				 */
				$JSON['messageText']['shortDescription'] = $d['title'];
				$JSON['messageText']['text'] = $d['text'];
				$JSON['recipients'][0]['recipientCode'] = str_replace('G00', '000', Agenzia::codePAtoGA($d['agenzia_id']));
				$JSON = json_encode($JSON);

				// build XML payload
				$XML = self::XML;
				$XML = str_replace('{EVENT_NAME}', $d['app'] . '_' . $d['event'], $XML);
				$XMLPayload = str_replace('{PAYLOAD}', base64_encode($JSON), $XML);

				// AsyncMessage call
				$API_URL = (ENVIRONMENT == 'PROD') ? self::GA_API_ASYNC_MESSAGE_URL : self::GA_API_DEV_ASYNC_MESSAGE_URL;
				TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "CURL $API_URL", nl2br(htmlentities($XMLPayload)));
				$ch = curl_init($API_URL);
				curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // force IPv4
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				curl_setopt($ch, CURLOPT_HTTPHEADER, [
					'Authorization: Bearer ' . GA_TOKEN,
					'Accept: application/xml',
					'Content-Type: application/xml'
				]);
				curl_setopt($ch, CURLOPT_POST, true);
				curl_setopt($ch, CURLOPT_POSTFIELDS, $XMLPayload);
				$response = curl_exec($ch);
				switch ($http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE)) {
					case 200:
						TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'API response OK', '<p>' . nl2br(htmlentities($response)) . '</p>');
						$this->pdoQuery('UPDATE pd_notifications SET status = \'OK\', publishedAt = CURRENT_TIMESTAMP WHERE id = ' . $d['id']);
						break;
					default:
						TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, 'API response ERR', '<p>' . nl2br(htmlentities($response)) . '</p>');
						$this->pdoQuery('UPDATE pd_notifications SET status = \'ERR\', publishedAt = CURRENT_TIMESTAMP WHERE id = ' . $d['id']);
				}
				curl_close($ch);
				// logs
				if (ENVIRONMENT != 'PROD')
					file_put_contents(LOG_DIR . 'pd-notificator-' . $app . '-' . $event . '-' . date('Y-m-d-H:i:s-') . $i . '.log',
						"CURL: POST $API_URL\n" .
						'HEADER Authorization: Bearer ' . GA_TOKEN . "\n" .
						"HEADER Accept: application/xml\n" .
						"HEADER Content-Type: application/xml\n\n" .
						"REQUEST\n" .
						"-- JSON ------------------------------\n$JSON\n--------------------------------------\n" .
						"-- XML -------------------------------\n$XMLPayload\n--------------------------------------\n\n" .
						"RESPONSE HTTP CODE: $http_code\n" .
						"RESPONSE BODY:\n$response"
					);
			}
		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
		}
		return true;
	}

	protected function url($url) {
		return ((ENVIRONMENT == 'PROD') ? self::GA_PAGENDO_URL : self::GA_PAGENDO_DEV_URL) . $url;
	}
}
