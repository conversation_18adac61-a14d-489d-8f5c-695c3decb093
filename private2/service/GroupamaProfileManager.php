<?php
namespace service;

use const metadigit\core\ENVIRONMENT;

class GroupamaProfileManager {
	use \metadigit\core\db\PdoTrait, GroupamaAuthTrait;

	const GA_API_PROFILE_MANAGER_URL = 'https://api-prod.groupama.it/profileManager/1';
	const GA_API_DEV_PROFILE_MANAGER_URL = 'https://api-coll.groupama.it/profileManager/1';

	/**
	 * Call API user profile
	 * @param string $userId Groupama user ID
	 * @return array|null
	 */
	public function userProfile($userId) {
		if (!$this->doAuth()) {
			return null;
		}
		try {
			// ProfileManager call
			$GA_API_PROFILE_MANAGER_URL = (ENVIRONMENT == 'PROD') ? self::GA_API_PROFILE_MANAGER_URL : self::GA_API_DEV_PROFILE_MANAGER_URL;
			$apiUrl = $GA_API_PROFILE_MANAGER_URL . '/userProfileByApplicationName/PORTDIST/' . $userId;
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "CURL $apiUrl");
			$ch = curl_init($apiUrl);
			curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // force IPv4
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . GA_TOKEN]);
			$response = curl_exec($ch);
			curl_close($ch);
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'API MANAGER RESPONSE', '<p>' . nl2br(htmlentities($response)) . '</p>');

		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
			return null;
		}

		return $this->parseResponse($response);
	}

	protected function parseResponse($response) {
		$XML = simplexml_load_string($response);
		$data = [
			'type' => (string) $XML->retrieveUserProfileResponse->userProfileName,
			'nome' => (string) $XML->retrieveUserProfileResponse->nome,
			'cognome' => (string) $XML->retrieveUserProfileResponse->cognome,
			'agenzia_id' => trim((string) $XML->retrieveUserProfileResponse->pvcPrimari),
			'login' => (string) $XML->retrieveUserProfileResponse->userId,
			'email' => (string) $XML->retrieveUserProfileResponse->email,
		];

		// Validation.
		$fields = [
			'type',
			'nome',
			'cognome',
			'agenzia_id',
			'login',
		];

		foreach ($fields as $field) {
			if (!$data[$field]) {
				TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, "Validation failed: $field", print_r($data, true));
				return null;
			}
		}

		switch ($data['type']) {
			case 'Agente':
				$data['type'] = 'AGENTE';
				break;
			case 'Intermediario':
				$data['type'] = 'INTERMEDIARIO';
				break;
			default:
				$data['type'] = null;
		}
		$data['agenzia_id'] = (($data['agenzia_id'][0] == 'N') ? 'N' : 'G') . substr($data['agenzia_id'], 3);
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'USER DATA', print_r($data, true));

		return $data;
	}
}
