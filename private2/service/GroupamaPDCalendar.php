<?php namespace service;

use api\apps\myplace\ApiException;
use metadigit\core\CoreTrait;

class GroupamaPDCalendar {

    // TEST
    // const URL = "https://webapi-coll.groupama.it/api/calendarioCompagnia/1.0";

    // PROD
    const URL = "https://webapi.groupama.it/api/calendarioCompagnia/1.0";

    use GroupamaAuthTrait;

    public function create($event) {
        return $this->call("/company/nuovoEvento", $event);
    }

    public function all() {
        return $this->call("/company/listaEventi", [
            //"startDate" => (new \DateTime("2019-01-01"))->format('c'),
            //"endDate" => (new \DateTime("2019-12-31"))->format('c'),
            "startDate" => "2019-01-01T00:00:00",
            "endDate" => "2019-12-31T23:59:59",
        ]);
    }

    public function update($event) {
        return $this->call("/company/modificaEvento", $event);
    }

    public function delete($event) {
        return $this->call("/company/eliminaEvento/{$event->id}");
    }

    protected function call($endpoint, $payload = null) {
        $this->doAuth();

        $endpoint = static::URL . $endpoint;

        $this->trace(LOG_ERR, 1, __FUNCTION__, "Call $endpoint");

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // force IPv4
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer '.GA_TOKEN,
            //'Accept: application/xml',
            'Content-Type: application/json;charset=UTF-8'
        ]);

        if ($payload) {
            //$payload = json_encode(["body" => $payload]);
            $payload = json_encode($payload);
            $this->trace(LOG_ERR, 1, __FUNCTION__, "API call payload", print_r($payload, 1));
            curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        }

        $result = (array) json_decode(curl_exec($ch));

        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        $this->trace(LOG_ERR, 1, __FUNCTION__, "API response $status", print_r($result, 1));

        curl_close($ch);

        return $this->handleResponse($result, $status);

        return $result && isset($result["body"]) ? $result["body"] : null;
    }

    protected function handleResponse($result, $status) {
        if (! $result) {
            return null;
        }

        if ($status == 200) {
            return isset($result["body"]) ? $result["body"] : null;
        }

        if (isset($result["messages"])) {
            throw new ApiException("API Exception", $status, $result["messages"]);
        }

        throw new ApiException("API Exception", $status);
    }
}
