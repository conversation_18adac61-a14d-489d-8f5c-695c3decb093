<?php
namespace service;

class StatsService {
	use \metadigit\core\CoreTrait, \metadigit\core\db\PdoTrait;

	const SQL_INSERT = 'INSERT INTO stats_users_actions_temp (date, user_id, l1, l2, l3, n)
	 					VALUES (:date, :user_id, :l1, :l2, :l3, 1)
						ON DUPLICATE KEY UPDATE n = n + 1';

	/**
	 * Store stats into DB
	 * @param string $l1
	 * @param string $l2
	 * @param string $l3
	 */
	function add($l1, $l2='', $l3='') {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "STATS:" . $l1.' '.$l2.' '.$l3);
		// avoid double stats during session
		$key = $l1.'-'.$l2.'-'.$l3;
		if(isset($_SESSION['_STATS_']) && false!==array_search($key, $_SESSION['_STATS_'])) {
            TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "STATS: already present in session. SKIP.");
            return;
        }
		$_SESSION['_STATS_'][] = $key;
		$userID = defined('SESSION_NEW_UID') ? SESSION_NEW_UID : SESSION_UID;
		$this->pdoStExecute(self::SQL_INSERT, [ 'date'=>date('Y-m-d'), 'user_id'=>$userID, 'l1'=>$l1, 'l2'=>$l2, 'l3'=>$l3 ]);
        TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "STATS: RECORDED.");
	}
}
