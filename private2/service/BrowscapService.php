<?php
namespace service;
use metadigit\core\event\Event,
	WhichBrowser\Parser;
/**
 * Browser Sniffer implementation with phpbrowscap
 * @see https://github.com/browscap/browscap-php
 */
class BrowscapService {
	use \metadigit\core\CoreTrait, \metadigit\core\db\PdoTrait;

	const SQL_INSERT = 'INSERT INTO stats_users_browser (year, month, user_id, platform, platformVersion, resolution, browser, browserVersion, counter)
	 					VALUES (:year, :month, :user_id, :platform, :platformVersion, :resolution, :browser, :browserVersion, 1)
						ON DUPLICATE KEY UPDATE counter = counter + 1';

	/**
	 * Browser Sniffer
	 * Store sniffed info into $_SESSION
	 */
	function sniff() {
		// implementation of getallheaders() with Nginx
		$headers = '';
		foreach ($_SERVER as $name => $value) {
			if (substr($name, 0, 5) == 'HTTP_') {
				$headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
			}
		}
		// track
		if(!isset($_SESSION['_BROWSCAP_'])) {
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'Browser sniffing...');
			$Browser = new Parser($headers);
			$_SESSION['_BROWSCAP_'] = [
					'platform'=>$Browser->os->name,
					'platformVersion'=>$Browser->os->version ? $Browser->os->version->alias : '',
					'browser'=>$Browser->browser->name,
					'browserVersion'=>$Browser->browser->version->getMajor()
			];
		}
	}

	/**
	 * Store info into DB
	 * @param Event $Event
	 */
	function stats(Event $Event) {
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'store info');
		$this->pdoStExecute(self::SQL_INSERT, [
				'year'=>date('Y'),
				'month'=>date('m'),
				'user_id'=>SESSION_NEW_UID,
				'platform'=>$_SESSION['_BROWSCAP_']['platform'],
				'platformVersion'=>$_SESSION['_BROWSCAP_']['platformVersion'],
				'resolution'=>$Event->resolution,
				'browser'=>$_SESSION['_BROWSCAP_']['browser'],
				'browserVersion'=>$_SESSION['_BROWSCAP_']['browserVersion']
		]);
	}
}
