<?php
namespace service;

use const metadigit\core\ENVIRONMENT;

const GA_API_AUTH_KEY = '2yF48bsGXZ2zmqZTIRrgKRpC1EMC1Mne4LdovvokUFBWWFwE';
const GA_API_AUTH_SECRET = 'GxeDZtb8IGA485sGJw5C0YSLRCSvSbnqCyua6qCaaSkdqw32NzsFZr2rQ57G5Xz5';
const GA_API_TOKEN_URL = 'https://api-prod.groupama.it/oauth/token';

const GA_API_DEV_AUTH_KEY = 'mgST8YcmFguvAc9H2k8q6VyOHLO1ELlzTDSC1e4d4onxLVCB';
const GA_API_DEV_AUTH_SECRET = 'I0DRLakeXw8nRcgqroMDLclPaIx0LMHRy4NoqURzhPCun1WmrGolVuXx4UZrlAuI';
const GA_API_DEV_TOKEN_URL = 'https://api-coll.groupama.it/oauth/token';

trait GroupamaAuthTrait {
	use \metadigit\core\CoreTrait;

	/**
	 * Retrieve Groupama API authentication TOKEN
	 * @return bool TRUE on success
	 */
	protected function doAuth() {
		$GA_API_AUTH_KEY = (ENVIRONMENT == 'PROD') ? GA_API_AUTH_KEY : GA_API_DEV_AUTH_KEY;
		$GA_API_AUTH_SECRET = (ENVIRONMENT == 'PROD') ? GA_API_AUTH_SECRET : GA_API_DEV_AUTH_SECRET;
		$GA_API_TOKEN_URL = (ENVIRONMENT == 'PROD') ? GA_API_TOKEN_URL : GA_API_DEV_TOKEN_URL;
		TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'CURL ' . $GA_API_TOKEN_URL);
		try {
			// Setup curl.
			$ch = curl_init($GA_API_TOKEN_URL);
			curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4); // force IPv4
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Basic ' . base64_encode($GA_API_AUTH_KEY . ':' . $GA_API_AUTH_SECRET)]);
			curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
			$response = (array) json_decode(curl_exec($ch));
			curl_close($ch);

			// Handle response.
			define('GA_TOKEN', $response['access_token']);
			define('GA_TOKEN_EXPIRE', time() + $response['expires_in']);
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'GA TOKEN: ' . GA_TOKEN);
			TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'GA TOKEN expire: ' . $response['expires_in']);

			return true;
		} catch (\Exception $ex) {
			TRACE and $this->trace(LOG_ERR, 1, __FUNCTION__, $ex->getMessage());
			return false;
		}
	}
}
