<?php namespace service;


class GroupamaUtils {

    public function convertAgencyCodeToGroupama($agendoCode) {
        if (! $agendoCode) {
            throw new \Exception("Agendo code is null");
        }

        if (strlen($agendoCode) != 4) {
            throw new \Exception("Agendo code is not 4 chars: $agendoCode");
        }

        $segment = substr($agendoCode, 0, 1);

        $code = substr($agendoCode, 1, 3);

        return $segment == 'G' ? "000" . $code : $segment . "00" . $code;
    }

    public function convertGroupamaCodeToAgendo($groupamaCode) {
        if (! $groupamaCode) {
            throw new \Exception("Groupama code is null");
        }

        $groupamaCode = preg_replace('/\s+/', '', $groupamaCode);

        if (strlen($groupamaCode) != 6) {
            throw new \Exception("Groupama code is not 6 chars: $groupamaCode");
        }

        $firstChar = substr($groupamaCode, 0, 1);

        $code = substr($groupamaCode, 3, 3);

        return $firstChar === 'N' ? 'N' . $code : "G" . $code;
    }

    public function convertGroupamaCode4CharactersToAgendo($groupamaCode) {
        if (!$groupamaCode) {
            throw new \Exception("Groupama code is null");
        }

        $groupamaCode = preg_replace('/\s+/', '', $groupamaCode);

        if (strlen($groupamaCode) != 4) {
            throw new \Exception("Groupama code is not 4 chars: $groupamaCode");
        }

        return substr($groupamaCode, 0, 1) === '0' ? 'G' . substr($groupamaCode, 1) : $groupamaCode;
    }

}