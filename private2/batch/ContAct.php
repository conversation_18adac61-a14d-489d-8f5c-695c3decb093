<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class ContAct extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const FILE_LEAD = 'CONTACT_LEAD.csv';
	const FILE_USCITI = 'CONTACT_USCITI.csv';

	/**
	 * @batch(description="ContAct: import CSV files")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function importcsvAction(Request $Req, Response $Res) {
		$this->importCsvLead();
		$this->importCsvUsciti();
	}


	private function importCsvLead() {
		$this->log('== LEAD ==============: ');
		if(!file_exists(UPLOAD_DIR.self::FILE_LEAD)) {
			$this->log(chr(9).'CSV: '.self::FILE_LEAD.' NON PRESENTE');
			return;
		}

		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ';', 'csvEnclosure' => '']);
		$sql = 'INSERT INTO app_contact_lead ( agenzia_id, data, nome, cognome, email, telefono, indirizzo, cap, localita, eta, dataScadenza, targa, marca, modello, cavalli )' .
			'VALUES ( :agenzia_id, :data, :nome, :cognome, :email, :telefono, :indirizzo, :cap, :localita, :eta, :dataScadenza, :targa, :marca, :modello, :cavalli )' .
			'ON DUPLICATE KEY UPDATE agenzia_id = :agenzia_id2, nome = :nome2, cognome = :cognome2, email = :email2, telefono = :telefono2, indirizzo = :indirizzo2, cap = :cap2, localita = :localita2, eta = :eta2, dataScadenza = :dataScadenza2, marca = :marca2, modello = :modello2, cavalli = :cavalli2 ';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute([
				'agenzia_id' => $values[0], 'agenzia_id2' => $values[0],
				'data' => date('Y-m-d'),
				'nome' => $values[1], 'nome2' => $values[1],
				'cognome' => $values[2], 'cognome2' => $values[2],
				'email' => $values[3], 'email2' => $values[3],
				'telefono' => $values[4], 'telefono2' => $values[4],
				'indirizzo' => $values[5], 'indirizzo2' => $values[5],
				'cap' => $values[7], 'cap2' => $values[7],
				'localita'=>$values[8], 'localita2'=>$values[8],
				'eta' => $values[12], 'eta2' => $values[12],
				'dataScadenza' => $values[6], 'dataScadenza2' => $values[6],
				'targa' => $values[13],
				'marca' => $values[9], 'marca2' => $values[9],
				'modello' => $values[10], 'modello2' => $values[10],
				'cavalli' => $values[11], 'cavalli2' => $values[11],
			]);
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_contact_lead')->fetchColumn();
		$this->log('Numero LEAD iniziali: ' . $oldRecords);
		$this->log(chr(9) . 'CSV: ' . self::FILE_LEAD);
		// process CSV
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . self::FILE_LEAD, 14, $processFn);
		$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach ($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
		}
		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_contact_lead')->fetchColumn();
		$this->log('Numero LEAD finali: ' . $newRecords);
	}

	private function importCsvUsciti() {
		$this->log('== USCITI ==============: ');
		if(!file_exists(UPLOAD_DIR.self::FILE_USCITI)) {
			$this->log(chr(9).'CSV: '.self::FILE_USCITI.' NON PRESENTE');
			return;
		}

		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		$sql = 'INSERT INTO app_contact_usciti ( agenzia_id, produttore, dataUscita, nominativo, dataNascita, indirizzo, cap, localita, comune, provincia, codiceFisc, eta, settore, polizza, targa, classeInt, classeCU, marca, modello, cavalli, cilindrata, premioRCA )'.
			'VALUES ( :agenzia_id, :produttore, :dataUscita, :nominativo, :dataNascita, :indirizzo, :cap, :localita, :comune, :provincia, :codiceFisc, :eta, :settore, :polizza, :targa, :classeInt, :classeCU, :marca, :modello, :cavalli, :cilindrata, :premioRCA )'.
			'ON DUPLICATE KEY UPDATE agenzia_id = :agenzia_id, produttore = :produttore2, dataUscita = :dataUscita2, nominativo = :nominativo2, dataNascita = :dataNascita2, indirizzo = :indirizzo2, cap = :cap2, localita = :localita2, comune = :comune2, provincia = :provincia2, codiceFisc = :codiceFisc2, eta = :eta2, settore = :settore2, polizza = :polizza2, classeInt = :classeInt2, classeCU = :classeCU2, marca = :marca2, modello = :modello2, cavalli = :cavalli2, cilindrata = :cilindrata2, premioRCA = :premioRCA2 ';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function($i, $line, $values) use ($pdoSt) {
			//$agenziaID = (($values[0][0] == 'N') ? 'N' : 'G').substr($values[0], 3, 3);
			$pdoSt->execute([
				'agenzia_id'=>$values[0], 'agenzia_id2'=>$values[0],
				'produttore'=>$values[2], 'produttore2'=>$values[2],
				'dataUscita'=>$values[3], 'dataUscita2'=>$values[3],
				'nominativo'=>$values[1], 'nominativo2'=>$values[1],
				'dataNascita'=>$values[4], 'dataNascita2'=>$values[4],
				'indirizzo'=>$values[5], 'indirizzo2'=>$values[5],
				'cap'=>$values[6], 'cap2'=>$values[6],
				'localita'=>$values[7], 'localita2'=>$values[7],
				'comune'=>$values[8], 'comune2'=>$values[8],
				'provincia'=>$values[9], 'provincia2'=>$values[9],
				'codiceFisc'=>$values[10], 'codiceFisc2'=>$values[10],
				'eta'=>$values[11], 'eta2'=>$values[11],
				'settore'=>$values[12], 'settore2'=>$values[12],
				'polizza'=>$values[13], 'polizza2'=>$values[13],
				'targa'=>$values[14],
				'classeInt'=>(int)$values[15], 'classeInt2'=>(int)$values[15],
				'classeCU'=>(int)$values[16], 'classeCU2'=>(int)$values[16],
				'marca'=>$values[17], 'marca2'=>$values[17],
				'modello'=>$values[18], 'modello2'=>$values[18],
				'cavalli'=>(int)$values[19], 'cavalli2'=>(int)$values[19],
				'cilindrata'=>(int)$values[20], 'cilindrata2'=>(int)$values[20],
				'premioRCA'=>$values[21], 'premioRCA2'=>$values[21]
			]);
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_contact_usciti')->fetchColumn();
		$this->log('Numero USCITI iniziali: '.$oldRecords);
		$this->log(chr(9).'CSV: '.self::FILE_USCITI);
		// process CSV
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE_USCITI, 22, $processFn);
		$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
		}
		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_contact_usciti')->fetchColumn();
		$this->log('Numero USCITI finali: '.$newRecords);
	}
}
