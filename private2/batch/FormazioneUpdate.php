<?php

namespace batch;


use data\apps\formazione\Managers\MailerManager;
use data\apps\formazione\Managers\PDFManager;
use data\apps\formazione\Models\Certificate;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;
use metadigit\core\db\orm\Repository;
use metadigit\core\Kernel;
use metadigit\core\util\DateTime;

class FormazioneUpdate
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
    use \metadigit\core\db\PdoTrait;
    use \metadigit\lib\batch\BatchTrait;

    /**
     * @var MailerManager
     */
    protected $mailerManager;

    /**
     * @var PDFManager
     */
    protected $pdfManager;

    /**
     * Send area courses certificates
     *
     * @batch(description="Formazione: invio attestati per corsi di Area")
     * @throws \Exception
     */
    public function sendAreaCoursesCertificatesAction(Request $Req, Response $Res)
    {

        $query     = "SELECT * FROM tra_mail_certificate";
        $recipients = $this->pdoQuery($query)->fetchAll();

        $totalRecipients = count($recipients);
        $emailSent = 0;
        $emailFailed = 0;
        $emailFailedDetail = [];

        foreach ($recipients as $recipient) {

            $data = $this->getAttendeeData($recipient["class_id"], $recipient["attendance_id"]);
            //$this->trace(LOG_ERR, 1, __FUNCTION__, "DATA", print_r($data,1));
            $classroom = $this->pdoQuery("SELECT * FROM tra_class WHERE id = {$recipient["class_id"]} ")->fetch();
            $course = $this->pdoQuery("SELECT * FROM tra_course WHERE {$classroom["course_id"]}")->fetch();
            $filename = PDFManager::DIR_TEMP . "$data->attendeeFirstName-$data->attendeeLastName.pdf";
            $this->trace(LOG_ERR, 1, __FUNCTION__, "FILENAME", print_r($filename,1));

            $pdf = $this->pdfManager->generatePDF($data, $classroom);

            //file_put_contents($filename, $pdf->output());

            try {
                $this->mailerManager->send(
                    $data->attendeeEmail,
                    $data->attendeeFirstName . " " . $data->attendeeLastName,
                    'Attestato '.$course->title,
                    'Gentile '.$data->attendeeFirstName . " " . $data->attendeeLastName.' in allegato il suo attestato per il corso in oggetto.',
                    $filename);
                //$this->areaCertificates->delete($recipient['id']);
                $emailSent++;

            } catch (\Exception $ex) {
                $emailFailed++;
                $emailFailedDetail[] = [
                    "email" => $data->attendeeEmail,
                    "name" => "$data->attendeeFirstName $data->attendeeLastName",
                    "course" => $course->title,
                ];
            }

        }

        $this->log("===================");
        $this->log("===================");
        $this->log("===================");
        $this->log("SOMMARIO INVII:");
        $this->log("Invii totali previsti: $totalRecipients");
        $this->log("Invii riusciti: $emailSent");
        $this->log("Invii falliti: $emailFailed");
        $this->log("Dettaglio invii falliti: " . implode(',', $emailFailedDetail));

    }


    /**
     * Send Notices to Publication a Course
     *
     * @batch(description="Formazione: invio email di invito")
     * @throws \Exception
     */
    public function sendPublicationNoticesAction(Request $Req, Response $Res)
    {
        $this->sendNotices('publication');
    }

    /**
     * Send Notices to Registration a Course
     *
     * @batch(description="Formazione: invio email di registrazione")
     * @throws \Exception
     */
    public function sendRegistrationNoticesAction(Request $Req, Response $Res)
    {
        $this->sendRegistrationNotices();
    }

    /**
     * Send Notices to Memo a Course
     *
     * @batch(description="Formazione: invio email di memo")
     * @throws \Exception
     */
    public function sendMemoNoticesAction(Request $Req, Response $Res)
    {
        $today = (new \DateTime('now'))->format('Y-m-d');

        // Notice required for the body message
        $query = "SELECT * FROM tra_mail_notice n WHERE n.type = 'memo' AND n.sent = 0";
        $notices = $this->pdoQuery($query)->fetchAll();

        foreach ($notices as $notice) {
            try {
                // Retrieve course's info and replace tags in body
                $query = "SELECT * FROM tra_course c WHERE c.id = ".$notice['course_id'];

                $this->log("Execute query: $query");

                $course = $this->pdoQuery($query)->fetch();

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);

                // Retrieve all ClassRooms to associate to a Course
                $query = "SELECT * FROM vw_tra_class c WHERE c.course_id = ".$notice['course_id']." AND c.sendMemo = 0";

                $this->log("Execute query: $query");

                $classRooms = $this->pdoQuery($query)->fetchAll();

                $countStarted = 0;  // Count started class

                foreach ($classRooms as $classRoom) {
                    if (!$classRoom['firstDay']) {
                        continue;
                    }

                    $this->log("Class ".$classRoom['id']);

                    $firstDay = \DateTime::createFromFormat('Y-m-d', $classRoom['firstDay']);
                    $firstDay->sub(new \DateInterval("P".$notice['step']."D"));
                    $firstDay = $firstDay->format('Y-m-d');

                    if ($today > $firstDay) {
                        // Already sent because class is began
                        $countStarted++;
                        continue;
                    }

                    if ($today !== $firstDay) {
                        continue;
                    }

                    // Replace classroom tags in body
                    $classroomBody = $this->mailerManager->replaceClassroomTags($body, $classRoom);

                    // Retrieve Recipients
                    $query = "SELECT r.*
                                FROM vw_tra_recipient_attendances r
                               WHERE r.class_id = ".$classRoom['id']."
                                 AND r.course_id = ".$notice['course_id']."
                                 AND r.state = 'signedup'";

                    $this->log("Execute query: $query");

                    $recipients = $this->pdoQuery($query)->fetchAll();

                    $this->log("BEGIN::SEND MAIL");
                    $this->mailerManager->sendToRecipients($recipients, $notice['subject'], $classroomBody, $course['id']);
                    $this->log("END::SEND MAIL");

                    // Update class
                    $query = "UPDATE tra_class c SET c.sendMemo = 1 WHERE c.id = ".$classRoom['id'];

                    $this->log("Execute query: $query");

                    $this->pdoExec($query);
                }

                // Sign notice sent Recipients for all classes
                $this->trace(LOG_DEBUG, 1, "COUNT", __FUNCTION__, "Count: " . count($classRooms));
                $this->trace(LOG_DEBUG, 1, "STARTED", __FUNCTION__, "$countStarted: " . $countStarted);

                // Set notice sent boolean
                if (count($classRooms) === 0) {
                    $query = "UPDATE tra_mail_notice n SET n.sent = 1 WHERE n.id = ".$notice['id'];

                    $this->log("Execute query: $query");

                    $this->pdoExec($query);
                }
            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
            }
        }
    }

    /**
     * Send Notices
     *
     * @param $type
     * @throws \Exception
     */
    private function sendNotices($type)
    {
        $today = (new \DateTime('now'))->format('Y-m-d');
        $query = "SELECT * FROM tra_mail_notice n WHERE n.type = '$type' AND n.sent = 0 AND sendDate = '$today'";
        $notices = $this->pdoQuery($query)->fetchAll();

        foreach ($notices as $notice) {
            try {
                // Retrieve course's info and replace tags in body.
                // Check if the course is active.
                $query = "SELECT c.* FROM tra_course c WHERE c.id = ".$notice['course_id']." AND c.status = 'on'";
                $course = $this->pdoQuery($query)->fetch();

                if (! $course) {
                    throw new \Exception("The course {$notice['course_id']} not exists or it is not active");
                }

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);

                // Retrieve Recipients
                $query = "SELECT * FROM vw_tra_recipient r WHERE r.course_id = ".$notice['course_id'];
                $recipients = $this->pdoQuery($query)->fetchAll();

                $this->mailerManager->sendToRecipients($recipients, $notice['subject'], $body, $course['id']);

                // Sign notice sent Recipients
                $query = "UPDATE tra_mail_notice n SET n.sent = 1 WHERE n.id = ".$notice['id'];
                $this->pdoExec($query);
            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
            }
        }
    }

    /**
     * Send Registration mail
     *
     * @throws \Exception
     */
    private function sendRegistrationNotices()
    {

        $countRecipients = 0;
        $countEmailSent = 0;
        $countEmailFailed = 0;
        $recipientsArray = [
            "sent" => [],
            "failed" => [],
        ];

        $this->log("===== BATCH START =====");

        try {
            $query = "SELECT a.id, a.firstname, a.lastname, a.email, a.course_id, a.class_id, a.user_id, a.type
                        FROM vw_tra_attendance a
                   WHERE a.sentMailRegistration = 0
                     AND a.state = 'signedup'
                     AND a.firstDay > CURDATE()
                     AND a.courseStatus = 'on'";
            $attendances = $this->pdoQuery($query)->fetchAll();
            $countRecipients = count($attendances);
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            exit;
        }

        foreach ($attendances as $attendance) {

            $this->log("== Email processing START for recipient: {$attendance["firstname"]} {$attendance["lastname"]}");

            try {
                $query = "SELECT * FROM tra_mail_notice n WHERE n.type = 'registration' AND n.course_id = ".$attendance['course_id'];
                $notice = $this->pdoQuery($query)->fetch();

                // Retrieve course's info and replace tags in body
                $query = "SELECT c.* FROM tra_course c WHERE c.id = ".$attendance['course_id'];
                $course = $this->pdoQuery($query)->fetch();

                // Retrieve classroom's info and replace tags in body
                $query = "SELECT c.* FROM vw_tra_class c WHERE c.id = ".$attendance['class_id'];
                $classRoom = $this->pdoQuery($query)->fetch();

                // Retrieve user's info and replace tags in body
                $query = "SELECT u.* FROM vw_tra_users u WHERE u.id = ".$attendance['user_id'];
                $user = $this->pdoQuery($query)->fetch();

                $this->trace(LOG_DEBUG, 1, "__FUNCTION__", "ATTENDANCE", print_r($attendance, 1));
                $this->trace(LOG_DEBUG, 1, "__FUNCTION__", "COURSE ID: {$course["id"]}");
                // Retrieve the user's agent
                if ( $attendance["type"] === 'AGENTE' ) {
                    $query = "SELECT u.* FROM vw_tra_users u WHERE u.type = 'AGENTE' AND u.email IS NOT NULL AND u.active = 1 AND u.id = ".$attendance['user_id'];
                    $agent = $this->pdoQuery($query)->fetch();
                }
                else {
                    $query = "SELECT u.* FROM vw_tra_users u WHERE u.agencyId = '".$user['agencyId']."' AND u.type = 'AGENTE' AND u.email IS NOT NULL AND u.active = 1 ORDER BY u.id LIMIT 1";
                    $agent = $this->pdoQuery($query)->fetch();
                }

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);
                $body = $this->mailerManager->replaceClassroomTags($body, $classRoom);
                $body = $this->mailerManager->replaceUserTags($body, $user);

                // Send Mail to Agent of Attendance
                if (
                    $this->mailerManager->send(
                    ($agent) ? $agent['email'] : null,
                    $attendance['firstname'] . ' ' . $attendance['lastname'],
                    $notice['subject'],
                    $body,
                    null,
                    $course['id'],
                    $attendance['user_id'])
                ) {
                    $this->trace(LOG_DEBUG, 1, "__FUNCTION__", "INVIO OK");
                    // Sign notice sent Recipients
                    $query = "UPDATE tra_attendance a SET a.sentMailRegistration = 1 WHERE a.id = ".$attendance['id'];
                    $this->pdoExec($query);
                    $countEmailSent++;
                    $this->log("== Email processing END - Invio riuscito");
                    $recipientsArray["sent"][] = [
                        "name" => $attendance['firstname'] . ' ' . $attendance['lastname'],
                        "email" => $agent['email'],
                        "text" => $body,
                    ];
                    continue;
                }

                $countEmailFailed++;
                $this->log("== Email processing END - Invio fallito");
                $recipientsArray["failed"][] = [
                    "name" => $attendance['firstname'] . ' ' . $attendance['lastname'],
                    "email" => $agent['email']
                ];

            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
                $countEmailFailed++;
                $this->log("== Email processing END - Invio fallito");
                $recipientsArray["failed"][] = [
                    "name" => $attendance['firstname'] . ' ' . $attendance['lastname'],
                    "email" => $agent['email']
                ];
                continue;
            }

        }

        $this->log("===== BATCH END =====");
        $this->log("===== SOMMARIO: =====");
        $this->log("Destinatari totali: $countRecipients");
        $this->log("Invii riusciti: $countEmailSent");
        $this->log("Invii falliti: $countEmailFailed");
        $this->log("===== ELENCO INVII =====");
        $this->log(print_r($recipientsArray, 1));
    }

    protected function getAttendeeData($classroomId, $attendeeId)
    {
        $query = "select * from vw_tra_certificate as vtc where vtc.classroomId = ".$classroomId." and vtc.attendeeId = ". $attendeeId . " limit 1";
        $attendee = $this->pdoQuery($query)->fetch();

        if (! count($attendee)) {
            return null;
        }

        $object = new Certificate();
        $object->courseId = $attendee['courseId'];
        $object->classroomId = $attendee['classroomId'];
        $object->attendeeId = $attendee['attendeeId'];
        $object->courseTitle = $attendee['courseTitle'];
        $object->courseCredits = $attendee['courseCredits'];
        $object->courseData = json_decode($attendee['courseData']);
        $object->courseGroupamaType = $attendee['courseGroupamaType'];
        $object->classroomFirstDay = $attendee['classroomFirstDay'];
        $object->classroomCity = $attendee['classroomCity'];
        $object->attendeeFirstName = $attendee['attendeeFirstName'];
        $object->attendeeLastName = $attendee['attendeeLastName'];
        $object->attendeeEmail = $attendee['attendeeEmail'];
        $object->classroomTrainer = $attendee['classroomTrainer'];

        return $object;
    }

}
