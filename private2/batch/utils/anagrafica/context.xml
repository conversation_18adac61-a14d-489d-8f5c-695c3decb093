<?xml version="1.0" encoding="UTF-8"?>
<context namespace="batch.utils.anagrafica">
    <includes>
        <include namespace="system"/>
    </includes>
    <objects>
        <object id="batch.utils.anagrafica.Matrnx" class="batch\utils\anagrafica\Matrnx"></object>
        <object id="batch.utils.anagrafica.Nxca" class="batch\utils\anagrafica\Nxca"></object>
        <object id="batch.utils.anagrafica.Analyzer" class="batch\utils\anagrafica\Analyzer">
            <properties>
                <property name="nxca" type="object">batch.utils.anagrafica.Nxca</property>
            </properties>
        </object>
        <object id="batch.utils.anagrafica.Renderer" class="batch\utils\anagrafica\Renderer"></object>
    </objects>
</context>
