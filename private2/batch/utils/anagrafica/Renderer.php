<?php namespace batch\utils\anagrafica;


class Renderer {

    public function csv($data, $filename)
    {
        $outfile = fopen(UPLOAD_DIR . "anagrafica/{$filename}.csv", "w");

        foreach($data as $line) {
            fputcsv($outfile, $line, ";");
        }

        fclose($outfile);
    }

    public function excel($data, $filename)
    {
        $filepath = UPLOAD_DIR . "anagrafica/{$filename}.xls";

        $outfile = fopen($filepath, "w");

        fputs($outfile, "<table border='1'>");

        $row = 0;

        foreach($data as $line) {
            $rowStyle = "";

            if (! $row) {
                $rowStyle = "style='font-weight: bold;'";
            }

            fputs($outfile, "<tr $rowStyle>");

            $count = count($line);

            $severity = $line[0];

            $styles = [
                "style='font-weight: normal'",
                "style='font-weight: normal; background-color: #fff375'",
                "style='font-weight: normal; background-color: #f9acac'",
                "style='font-weight: normal; background-color: #ed5050'",
            ];

            for ($i = 0; $i<$count; $i++) {
                if ($i == 0 && $row) {
                    fputs($outfile, "<td align='center' {$styles[$severity]}>");
                } else {
                    fputs($outfile, "<td align='center'>");
                }

                fputs($outfile, $line[$i]);

                fputs($outfile, "</td>");
            }

            fputs($outfile, "</tr>");

            $row++;
        }

        fputs($outfile, "</table>");

        fclose($outfile);

        return $filepath;
    }

}