<?php namespace batch\utils\anagrafica;


class Analyzer {

    /**
     * @var Nxca
     */
    protected $nxca;

    public function check($valueAG, $valueGA)
    {
        $valueAG = trim(strtoupper(($valueAG)));
        $valueGA = trim(strtoupper(($valueGA)));

        similar_text(
            $valueAG,
            $valueGA,
            $perc
        );

        return $perc;
    }

    protected $config = [
        'agents.multiple' => [
            'enabled' => true,
            'callback' => 'checkMultipleAccount',
            'category' => 'Agente con multiple occorrenze in Agendo',
            'action' => "Verificare se necessario disattivare una delle utenze",
            'severity' => 3,
        ],

        'agents.username.multiple' => [
            'enabled' => true,
            'callback' => 'checkUsernameMultiple',
            'category' => 'Agente con multiple LOGIN',
            'action' => 'Da concordare con GROUPAMA',
            'severity' => 1,
        ],

        'agents.username.temp' => [
            'enabled' => true,
            'callback' => 'checkUsernameTemp',
            'category' => "Agente con LOGIN temporanea",
            'action' => 'Disattivare/annullare login temporanea (PAxxx)',
            'severity' => 2,
        ],

        'agents.username.temp.only' => [
            'enabled' => false,
            'callback' => null,
            'category' => "Agente solo con LOGIN temporanea",
            'action' => "Sollecitare l'agente ad aggiornare login definitiva",
            'severity' => 2,
        ],

        'agents.email' => [
            'enabled' => true,
            'callback' => 'checkEmail',
            'category' => "Possibile e-mail mancante o non coerente",
            'action' => "Verificare caso e correggere",
            'severity' => 2,
        ],

        'agents.credits' => [
            'enabled' => true,
            'callback' => 'checkCredits',
            'category' => "Ore formative su account diversi",
            'action' => "Contattare KEYASSOCIATI per chiedere unificazione crediti",
            'severity' => 3,
        ],

        'agents.name' => [
            'enabled' => true,
            'callback' => 'checkName',
            'category' => "Possibile errore nominativo",
            'action' => "Verificare caso e correggere",
            'severity' => 2,
        ],

        'agents.active' => [
            'enabled' => true,
            'callback' => 'checkActive',
            'category' => "Errore stato attivazione",
            'action' => "Verificare e attivare su Agendo",
            'severity' => 3,
        ],

        'agents.active.inverse' => [
            'enabled' => false,
            'callback' => null,
            'category' => "ATTIVO su Agendo non presente GROUPAMA",
            'action' => "Verificare e disattivare su Agendo",
            'severity' => 3,
        ],

        'agents.rui' => [
            'enabled' => true,
            'callback' => 'checkRUI',
            'category' => "Codice RUI errato",
            'action' => "Aggiornare Agendo con dato presente su file GROUPAMA",
            'severity' => 3,
        ],

        'agents.notFound' => [
            'enabled' => false,
            'callback' => null,
            'category' => "Agente non trovato in Agendo",
            'action' => "Attivare l'account su Agendo",
            'severity' => 2,
        ],

        'agents.notCreated' => [
            'enabled' => false,
            'callback' => null,
            'category' => "Agente non creato in Agendo",
            'action' => "Creare l'account su Agendo",
            'severity' => 2,
        ],

        'agents.updated' => [
            'enabled' => false,
            'callback' => null,
            'category' => "Aggiornato l'intermediario con lo stesso RUI",
            'action' => "Aggiornare l'account dell'intermediario su Agendo",
            'severity' => 2,
        ],
    ];

    public function checkRUI($agents, $row, $config, $source)
    {
        $result = $this->init();

        foreach ($agents as $agent) {
            if ($agent->rui && ($this->nxca->get('rui', $row) != $agent->rui)) {
                $result->success = false;

                $result->logs[] = $this->nxca->log(
                    "Agendo: {$agent->rui} GROUPAMA: " . $this->nxca->get('rui', $row),
                    $config,
                    $source,
                    $agent
                );
            }
        }

        return $result;
    }

    public function checkActive($agents, $row, $config, $source)
    {
        $result = $this->init();

        $active = $this->nxca->get('status', $row) == 'AT' ? 1 : 0;

        foreach ($agents as $agent) {
            if ($active != $agent->active) {
                $result->success = false;

                $result->logs[] = $this->nxca->log(
                    "Agendo: {$agent->active} GROUPAMA: " . $this->nxca->get('status', $row),
                    $config,
                    $source,
                    $agent
                );
            }
        }

        return $result;
    }

    public function checkName($agents, $row, $config, $source)
    {
        $result = $this->init();

        foreach ($agents as $agent) {
            $similarity = $this->check(
                "{$agent->cognome} {$agent->nome}",
                $source['nxName']
            );

            if ($similarity >= 95) {
                continue;
            }

            $result->success = false;

            $result->logs[] = $this->nxca->log(
                $similarity,
                $config,
                $source,
                $agent
            );
        }

        return $result;
    }

    public function checkCredits($agents, $row, $config, $source)
    {
        $result = $this->init();

        $ids = [];

        array_walk($agents, function($item) use(&$ids){
            if ($item->credits && $item->credits > 0) {
                $ids[] = $item->id;
            }
        });

        if (count($ids) > 1) {
            $result->success = false;

            $result->logs[] = $this->nxca->log(
                "ID: " . implode(", ", $ids),
                $config,
                $source
            );
        }

        return $result;
    }

    public function checkEmail($agents, $row, $config, $source)
    {
        $result = $this->init();

        foreach ($agents as $agent) {
            $emailSimilarity = $this->check(
                "{$agent->cognome}{$agent->nome}",
                $agent->email
            );

            if ($emailSimilarity < 30) {
                $result->success = false;

                $result->logs[] = $this->nxca->log(
                    $agent->email,
                    $config,
                    $source,
                    $agent
                );
            }
        }

        return $result;
    }

    protected function checkUsernameTempOnly($pa, $usernames, $agent, $source)
    {
        $count = 0;

        foreach ($usernames as $username) {
            if ($username) {
                $count++;
            }
        }

        if ($count == 1) {
            return $this->nxca->log(
                $pa,
                $this->config['agents.username.temp.only'],
                $source,
                $agent
            );
        }

        return null;
    }

    public function checkUsernameTemp($agents, $row, $config, $source)
    {
        $result = $this->init();

        foreach ($agents as $agent) {
            $usernames = [$agent->login, $agent->login1, $agent->login2];

            foreach($usernames as $username) {

                if (preg_match("/^PA.*/", $username)) {
                    $result->success = false;

                    $result->logs[] = $this->nxca->log(
                        $username,
                        $config,
                        $source,
                        $agent
                    );

                    if ($only = $this->checkUsernameTempOnly($username, $usernames, $agent, $source)) {
                        $result->logs[] = $only;
                    }

                    break;
                }
            }
        }

        return $result;
    }

    public function checkMultipleAccount($agents, $row, $config, $source)
    {
        $result = $this->init();

        if (($count = count($agents)) > 1) {
            $result->success = false;

            $result->logs[] = $this->nxca->log(
                implode(",", array_map(function($item){
                    return $item->agenzia_id;
                }, $agents)),
                $config,
                $source
            );
        }

        return $result;
    }

    public function checkUsernameMultiple($agents, $row, $config, $source)
    {
        $result = $this->init();

        foreach ($agents as $agent) {
            $usernames = [$agent->login, $agent->login1, $agent->login2];

            if ((!empty($agent->login)?1:0)+(!empty($agent->login1)?1:0)+(!empty($agent->login2)?1:0)) {
                $result->success = false;

                $result->logs[] = $this->nxca->log(
                    implode("|", array_filter($usernames, function($item){
                        return $item;
                    })),
                    $config,
                    $source,
                    $agent
                );
            }
        }

        return $result;
    }

    public function foo($name, $agents, $row, $source)
    {
        $config = $this->config[$name];

        if (! $config['enabled']) {
            return null;
        }

        if (! $result = $this->{$config['callback']}($agents, $row, $config, $source)) {
            throw new \Exception("Check $name failed");
        }

        $result->ids = array_map(function($item){
            return $item->id;
        }, $agents);

        return $result;
    }

    public function getConfig()
    {
        return $this->config;
    }

    protected function init($success = true, $logs = [])
    {
        return (object)[
            'success' => $success,
            'logs' => $logs,
            'ids' => [],
        ];
    }
}
