<?php namespace batch\utils\anagrafica;


class Matrnx implements InputFileInterface {

    //
    // CSV mappings
    //
    public function code($row)
    {
        $code = str_pad(
            substr($this->get('agenzia', $row), -3),
            3,
            "0",
            STR_PAD_LEFT
        );

        switch($this->get('line', $row)){
            case 'VERDE':
                return "G" . $code;
                break;

            case 'AZZURRA':
                return "N" . $code;
                break;

            default:
                return null;
        }
    }

    public function date($key, $row)
    {
        $date = \DateTime::createFromFormat('d/m/Y', $this->get('man_dt', $row));
        if (!$date) {
            return null;
        }
        return $date->format('Y-m-d');
    }

    protected $config = [
        'line' => 4,
        'agenzia' => 7,
        'name' => 8,
        'prog' => 9,
        'network' => 13,
        'active' => 17,
        'address' => 20,
        'cap' => 21,
        'location' => 22,
        'province' => 24,
        'tel1' => 29,
        'tel2' => 30,
        'tel3' => 31,
        'email' => 35,
        'area' => 41,
        'am' => 42,
        'district' => 43,
        'dm' => 44,
        'man_dt' => 52,
        'type' => 15,
        'area_code' => 39
    ];

    public function phone($row)
    {
        return (!empty($this->get('tel1', $row)) ? $this->get('tel1', $row) :
                    (!empty($this->get('tel2', $row)) ? $this->get('tel2', $row) :
                        (!empty($this->get('tel3', $row)) ? $this->get('tel3', $row) : null)));
    }

    public function get($key, $row)
    {
        if (! isset($this->config[$key])) {
            throw new \Exception("$key column not found" . print_r($this->config, 1));
        }

        if (! isset($row[$this->config[$key]])) {
            throw new \Exception("$key value not found");
        }

        return trim($row[$this->config[$key]]);
    }
}
