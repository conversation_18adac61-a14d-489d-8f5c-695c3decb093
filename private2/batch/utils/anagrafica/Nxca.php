<?php namespace batch\utils\anagrafica;

/**
 * Class Nxca
 * @package batch\utils\anagrafica
 *
 * NXCA6603
 */
class Nxca extends Matrnx {

    protected $config = [
        'agenzia' => 1,
        'status' => 3,
        'relation' => 21,
        'lastname' => 23,
        'name' => 24,
        'rui' => 32,
    ];

    protected $invalidCodes = [
        "000001",
        "000004",
        "000395",
        "000400",
        "000404",
        "000599",
        "000601",
        "000751",
        "000752",
        "000790",
        "000802",
        "000905",
        "000L00",
        "000L69",
        "000X01",
        "N00235",
        "N00997",
        "N00B09",
        "N00C61",
        "N00C69",
    ];

    public function code($row)
    {
        $code = $this->get('agenzia', $row);

        if ($code[0] != 'N') {
            return "G" . substr($code, 3);
        }

        return "N" . substr($code, 3);
    }

    public function get($key, $row)
    {
        return parent::get($key, $row);
    }

    public function log($notes, $config, $source, $agent = null)
    {
        return [
            $config['severity'],
            $source ? $source['nxCode'] : null,
            $agent ? $agent->agenzia_id : null,
            $agent && $agent->id ?$agent->id : null,
            $agent && $agent->active ? $agent->active ? 'ATTIVO' : 'NON ATTIVO' : null,
            $agent ? "{$agent->cognome} {$agent->nome}" : null,
            $source ? $source['nxName'] : null,
            $config['category'],
            $notes,
            $config['action'],
        ];
    }

    public function skip($row)
    {
        if (! in_array($this->get('relation', $row), ['MANDATARIO_FIS', 'DELEGATO'])) {
            return true;
        }

        if (in_array($this->get('agenzia', $row), $this->invalidCodes)) {
            return true;
        }

        return false;
    }
}
