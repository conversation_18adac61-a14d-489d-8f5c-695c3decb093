<?php

namespace batch\utils;

class SFTPFileDownload
{
    private $host;
    private $port;
    private $username;
    private $password;
    private $connection;
    private $sftp;

    public function __construct($host, $port, $username, $password)
    {
        $this->host = $host;
        $this->port = $port;
        $this->username = $username;
        $this->password = $password;
    }

    public function connect()
    {
        // Enable error reporting for debugging
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        // Try to resolve the hostname first
        $ip = gethostbyname($this->host);
        if ($ip === $this->host) {
            throw new \Exception("Failed to resolve hostname: {$this->host}");
        }
        
        // Add connection timeout
        $this->connection = @\ssh2_connect($this->host, $this->port, [], [
            'debug' => true,
            'timeout' => 10
        ]);
        
        if (!$this->connection) {
            throw new \Exception("Failed to connect to SFTP server: {$this->host}:{$this->port}");
        }

        if (!\ssh2_auth_password($this->connection, $this->username, $this->password)) {
            throw new \Exception("Failed to authenticate with SFTP server using credentials: {$this->username}");
        }

        $this->sftp = \ssh2_sftp($this->connection);
        if (!$this->sftp) {
            throw new \Exception("Failed to initialize SFTP subsystem");
        }

        return true;
    }

    public function downloadFile($remoteFile, $localFile)
    {
        if (!$this->sftp) {
            $this->connect();
        }

        $remoteStream = @fopen("ssh2.sftp://{$this->sftp}{$remoteFile}", 'r');
        if (!$remoteStream) {
            throw new \Exception("Failed to open remote file: $remoteFile");
        }

        $localStream = @fopen($localFile, 'w');
        if (!$localStream) {
            fclose($remoteStream);
            throw new \Exception("Failed to create local file: $localFile");
        }

        $result = \stream_copy_to_stream($remoteStream, $localStream);
        
        fclose($localStream);
        fclose($remoteStream);
        
        if ($result === false) {
            throw new \Exception("Failed to download file: $remoteFile");
        }

        return true;
    }

    /**
     * List files in a remote directory
     * @param string $remoteDir Remote directory path (default: '/')
     * @return array Array of files and directories
     * @throws \Exception
     */
    public function listFiles($remoteDir = '/')
    {
        if (!$this->sftp) {
            $this->connect();
        }

        $handle = @opendir("ssh2.sftp://{$this->sftp}{$remoteDir}");
        if (!$handle) {
            throw new \Exception("Failed to open remote directory: $remoteDir");
        }

        $files = [];
        while (false !== ($entry = readdir($handle))) {
            if ($entry != '.' && $entry != '..') {
                $path = $remoteDir . '/' . $entry;
                $stat = ssh2_sftp_stat($this->sftp, $path);
                $files[] = [
                    'name' => $entry,
                    'path' => $path,
                    'size' => $stat['size'],
                    'modified' => date('Y-m-d H:i:s', $stat['mtime']),
                    'is_dir' => is_dir("ssh2.sftp://{$this->sftp}{$path}")
                ];
            }
        }

        closedir($handle);
        return $files;
    }

    /**
     * Test connection with detailed diagnostics
     * @return array Connection test results
     */
    public function testConnection()
    {
        $results = [
            'host' => $this->host,
            'port' => $this->port,
            'username' => $this->username,
            'dns_resolution' => false,
            'port_open' => false,
            'connection' => false,
            'authentication' => false,
            'sftp_subsystem' => false,
            'error' => null
        ];
        
        try {
            // Test DNS resolution
            $ip = gethostbyname($this->host);
            if ($ip === $this->host) {
                $results['error'] = "DNS resolution failed for {$this->host}";
                return $results;
            }
            $results['dns_resolution'] = true;
            $results['resolved_ip'] = $ip;
            
            // Test if port is open (with timeout)
            $socket = @fsockopen($this->host, $this->port, $errno, $errstr, 5);
            if (!$socket) {
                $results['error'] = "Port {$this->port} is not open on {$this->host}: $errstr ($errno)";
                return $results;
            }
            fclose($socket);
            $results['port_open'] = true;
            
            // Try SSH connection
            $this->connection = @\ssh2_connect($this->host, $this->port);
            if (!$this->connection) {
                $results['error'] = "SSH connection failed to {$this->host}:{$this->port}";
                return $results;
            }
            $results['connection'] = true;
            
            // Try authentication
            if (!\ssh2_auth_password($this->connection, $this->username, $this->password)) {
                $results['error'] = "Authentication failed with username: {$this->username}";
                return $results;
            }
            $results['authentication'] = true;
            
            // Try SFTP subsystem
            $this->sftp = \ssh2_sftp($this->connection);
            if (!$this->sftp) {
                $results['error'] = "Failed to initialize SFTP subsystem";
                return $results;
            }
            $results['sftp_subsystem'] = true;
            
        } catch (\Exception $e) {
            $results['error'] = "Exception: " . $e->getMessage();
        }
        
        return $results;
    }
}
