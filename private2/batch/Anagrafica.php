<?php
namespace batch;
use batch\utils\anagrafica\Analyzer;
use batch\utils\anagrafica\Matrnx;
use batch\utils\anagrafica\Nxca;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class Anagrafica extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const FILE_LEAD = 'ANAGRAFICA_AGENZIE_NUOVE_AREE.csv';
	const FILE_GL93 = 'ANAGRAFICA_INTERMEDIARI_GL93.csv';
	/** Abi Service
	 * @var \service\Abi */
	protected $Abi;
	/** UsersAuthRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $UsersAuthRepository;

    /**
     * @var Matrnx
     */
    protected $matrnx;

    /**
     * @var Nxca
     */
    protected $nxca;

    /**
     * @var Analyzer
     */
    protected $analyzer;

	/**
	 * @batch(description="Anagrafica agenzie: update AREE S1 e S2")
	 * @param Request $Req
	 * @param Response $Res
	 * @throws \metadigit\lib\util\csv\Exception
	 */
	function agenzieS1S2Action(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ';', 'csvEnclosure' => '']);
		$sql = 'UPDATE agenzie SET area = :area_id, district = :district_id WHERE id = :agenzia_id';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute([
				'agenzia_id' => $values[0],
				'area_id' => $values[1],
				'district_id' => $values[2]
			]);
			return [true, null];
		};
		// process CSV
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . self::FILE_LEAD, 3, $processFn);
		$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach ($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
		}
	}

	/**
	 * @batch(description="Anagrafica intermediari: import intermediari GL93 Optima Italia")
	 * @param Request $Req
	 * @param Response $Res
	 * @throws \metadigit\lib\util\csv\Exception
	 */
	function importGL93Action(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ';', 'csvEnclosure' => '"']);
		$sql = 'INSERT INTO users SET type = :type, active = :active, nome = :nome, cognome = :cognome, email = :email, rui = :rui, area = :area, district = :district, agenzia_id = :agenzia_id, acl_elearning = :acl_elearning';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute([
				'type'			=> 'INTERMEDIARIO',
				'active'		=> 1,
				'nome'			=> trim($values[2]),
				'cognome'		=> trim($values[1]),
				'email'			=> trim($values[4]),
				'rui'			=> trim($values[5]),
				'area'			=> 99,
				'district'		=> 26244,
				'agenzia_id'	=> 'GL93',
				'acl_elearning'	=> 1
			]);
			return [true, null];
		};
		// process CSV
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . self::FILE_GL93, 6, $processFn);
		$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach ($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
		}
	}

	/**
	 * @batch(description="Anagrafica: call Abi API")
	 * @param Request $Req
	 * @param Response $Res
	 * @param integer $from first User ID
	 * @param integer $to last User ID
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function callAbiApiAction(Request $Req, Response $Res, $from, $to) {
		$criteriaExp = 'id,GTE,'.(int)$from.'|id,LTE,'.(int)$to;
		$users = $this->UsersAuthRepository->fetchAll(null, null, 'id.ASC', $criteriaExp);
		$ok = $err = $tot = 0;
		foreach ($users as $User) {
			$r = $this->Abi->callAPI($User, 'INSERT');
			$tot++;
			$ok += (int)$r;
			$err += (int)!$r;
			$this->log(sprintf('[%s] %s %s %s %s', ($r)?'OK':'ERR', $User->id, $User->nome, $User->cognome, $User->login1));
		}
		$this->log(sprintf('OK/ERR/TOT: %s/%s/%s', $ok, $err, $tot));
	}

    /**
     * @batch(description="Anagrafica: verifica anagrafica agenzie/am/dm")
     * @param Request $Req
     * @param Response $Res
     */
    function agenzieAction(Request $Req, Response $Res) {
        $file = file(UPLOAD_DIR . "MATRNX.csv");

        array_shift($file);

        $csv = array_map(function($line){
            return str_getcsv($line, ";");
        }, $file);

        $statementAgenzie = $this->pdoPrepare("SELECT * FROM vw_agenzie_am_dm_2 where id = :id");

        $result = [
            'OK' => 0,
            'ERR' => 0,
            'W' => 0,
        ];

        $output = [["AGENZIA", "ESITO CONTROLLO", "SOGGETTO", "VAL.AGENDO", "VAL.GROUPAMA", "NOTE"]];

        foreach($csv as $row) {
            if ($prog = $this->matrnx->get('prog', $row)) {
                //$this->log("Skip line (prog=$prog)");
                continue;
            }

            $code = $this->matrnx->code($row);

            $statementAgenzie->execute([':id' => $code]);

            $output = array_merge(
                $output,
                $this->checkAgenzia(
                    $statementAgenzie->fetch(\PDO::FETCH_OBJ),
                    $row,
                    $result
                )
            );
            //$this->log(print_r($agenzia, 1));
        }

        $this->log(print_r($result, 1));

        $outfile = fopen(UPLOAD_DIR . "ANAGRAFICA_CHECK.csv", "w");

        foreach($output as $line) {
            fputcsv($outfile, $line, ";");
        }

        fclose($outfile);

        /*$outcsv = array_map(function($value){
            return implode(";", $value);
        }, $output);
        $this->log(print_r($outcsv, 1));*/
    }

    /**
     * @batch(description="Anagrafica: verifica anagrafica agenti")
     * @param Request $Req
     * @param Response $Res
     */
    function agentiAction(Request $Req, Response $Res)
    {
        $file = file(UPLOAD_DIR . "NXCA6603.csv");

        array_shift($file);

        $csv = array_map(function($line){
            return str_getcsv($line, ";");
        }, $file);

        // INIT
        // @TODO: easy insert in prod.
        $this->pdoExec("truncate users_anag_check");
        $this->pdoExec("insert into users_anag_check select * from vw_anag_users");
        //

        //$statement = $this->pdoPrepare("SELECT * FROM vw_users where type ='AGENTE' and agenzia_id=:agenzia_id and (rui = :rui or MATCH(nome) AGAINST (:nome) and MATCH(cognome) AGAINST(:cognome))");

        // @TODO: easy loop in prod.
        $statement = $this->pdoPrepare("SELECT * FROM users_anag_check where type ='AGENTE' and agenzia_id=:agenzia_id and (rui = :rui or MATCH(nome) AGAINST (:nome) and MATCH(cognome) AGAINST(:cognome))");

        $result = [
            'OK' => 0,
            'ERR' => 0,
            'W' => 0,
        ];

        $ids = [];

        foreach($csv as $row) {
            if (! in_array($this->nxca->get('relation', $row), ['MANDATARIO_FIS', 'DELEGATO'])) {
                //$this->log("skip " . $this->nxca->get('relation', $row));
                continue;
            }

            $statement->execute([
                ':rui' => $this->nxca->get('rui', $row),
                ':nome' => $this->nxca->get('name', $row),
                ':cognome' => $this->nxca->get('lastname', $row),
                ':agenzia_id' => $this->nxca->code($row),
            ]);

            $nxName = $this->nxca->get('lastname', $row) . " " . $this->nxca->get('name', $row);

            if (! $agents = $statement->fetchAll(\PDO::FETCH_OBJ)) {
                $result['ERR']++;
                $this->log($this->nxca->code($row) . " $nxName non trovato");
                continue;
            }

            //$this->log(print_r($agents, 1));

            $count = count($agents);

            if ($count > 1) {
                $result['W']++;
                $this->log($this->nxca->code($row) . " $count occorrenze per $nxName");
                $this->log(print_r($agents, 1));
            }

            $similarity = 0;
            $credits = 0;

            foreach($agents as $agent) {
                $ids[] = $agent->id;

                if ($agent->rui == $this->nxca->get('rui', $row)) {
                    $result['OK']++;
                    continue;
                    //$this->log("match rui");
                    //
                }

                // Check username
                if ((!empty($agent->login)?1:0)+(!empty($agent->login1)?1:0)+(!empty($agent->login2)?1:0)) {
                    $result['ERR']++;
                    $this->log("{$agent->id} {$agent->cognome} {$agent->nome} MULTIPLE USERNAME");
                }

                foreach([$agent->login, $agent->login1, $agent->login2] as $username) {
                    if (preg_match("/PA.*/", $username)) {
                        $result['W']++;
                        $this->log("{$agent->id} {$agent->cognome} {$agent->nome} PA USERNAME $username");
                    }
                }

                // Check name
                $emailSimilarity = $this->analyzer->check(
                    "{$agent->cognome}{$agent->nome}",
                    $agent->email
                );

                if ($emailSimilarity < 30) {
                    $result['W']++;
                    $this->log("{$agent->id} {$agent->cognome} {$agent->nome} check email $agent->email");
                }

                $similarity = $this->analyzer->check(
                    "{$agent->cognome} {$agent->nome}",
                    $nxName
                );

                if ($agent->credits > 0) {
                    $credits++;
                }

                if ($similarity >= 95) {
                    $result['OK']++;
                    //$this->log("{$agent->id} {$agent->cognome} {$agent->nome} $nxName ok");
                    continue;
                }

                if ($similarity >= 80) {
                    $result['W']++;
                    $this->log($this->nxca->code($row) . " {$agent->id} {$agent->cognome} {$agent->nome} $nxName check");
                    continue;
                }

                if ($similarity <80) {
                    $result['ERR']++;
                    $this->log($this->nxca->code($row) . " {$agent->id} {$agent->cognome} {$agent->nome} $nxName bad");
                    continue;
                }
            }

            if ($count == 1 && $similarity >= 80) {
                // More checks
                $active = $this->nxca->get('status', $row) == 'AT' ? 1 : 0;

                if ($active != $agents[0]->active) {
                    $result['ERR']++;
                    $this->log("{$agent->id} {$agent->cognome} {$agent->nome} $nxName STATUS ERR");
                }

                if ($agents[0]->rui && ($this->nxca->get('rui', $row) != $agents[0]->rui)) {
                    $result['ERR']++;
                    $this->log("{$agent->id} {$agent->cognome} {$agent->nome} $nxName RUI ERR");
                }
            }

            if ($credits > 1) {
                $result['ERR']++;
                $this->log("{$agent->id} {$agent->cognome} {$agent->nome} FORMAZ");
            }

            /*$output = array_merge(
                $output,
                $this->checkAgenzia(
                    $statementAgenzie->fetch(\PDO::FETCH_OBJ),
                    $row,
                    $result
                )
            );*/

        }

        $statement = $this->pdoPrepare("select * from users_anag_check where id not in (:ids)");

        $statement->execute([':ids' => implode(",", $ids)]);

        foreach ($statement->fetchAll(\PDO::FETCH_OBJ) as $agent) {
            if (! $agent->active) {
                continue;
            }

            $result['ERR']++;
            $this->log("{$agent->id} {$agent->cognome} {$agent->nome} BAD ACTIVE");
        }

        $this->log(print_r($result, 1));
    }

    protected function checkAgente()
    {

    }

    protected function checkAgenzia($agenzia, $row, &$result)
    {
        $err = [];
        $warning = [];
        $output = [];

        if (! $agenzia) {
            $result['ERR']++;
            $this->log("Agenzia {$this->matrnx->get('agenzia', $row)} non trovata");

            $output[] = [
                "",
                'ERROR',
                'AGENZIA',
                "",
                $this->matrnx->get('agenzia', $row),
                "Agenzia non trovata in Agendo"
            ];

            return $output;
        }

        // @fixme => matrnx
        foreach($row as $key => $col) {
            $row[$key] = strtoupper(trim($col));
        }

        //
        //
        // Status
        //
        //

        if ($agenzia->status != "OFF" && $this->matrnx->get('active', $row) != 'AT') {
            $output[] = [
                $agenzia->id,
                'ERROR',
                'AGENZIA',
                $agenzia->status,
                $this->matrnx->get('active', $row),
                "Possibile chiusura agenzia non gestita"
            ];
            $err[] = "Errore stato agenzia KA:{$agenzia->status} / GA:" . $this->matrnx->get('active', $row);
        }

        /*$r = $this->analyzer->differ(
            $agenzia->nome,
            $this->matrnx->get('name', $row),
            $agenzia->id,
            "Errore denominazione agenzia"
        );

        if ($r) {
            $output[] = $r->result;
        }*/

        if (trim(strtoupper($agenzia->nome)) != $this->matrnx->get('name', $row)) {
            $output[] = [
                $agenzia->id,
                'ERROR',
                'AGENZIA',
                $agenzia->nome,
                $this->matrnx->get('name', $row),
                "Errore denominazione agenzia"
            ];
            $err[] = "Errore denominazione Agenzia KA:{$agenzia->nome} / GA:" . $this->matrnx->get('name', $row);
        };

        if (
            trim(strtoupper($agenzia->indirizzo)) != $this->matrnx->get('address', $row) ||
            trim(strtoupper($agenzia->localita)) != $this->matrnx->get('location', $row) ||
            trim(strtoupper($agenzia->cap)) != $this->matrnx->get('cap', $row)
        ) {
            $output[] = [
                $agenzia->id,
                'ERROR',
                'AGENZIA',
                "{$agenzia->indirizzo} {$agenzia->localita} {$agenzia->cap}",
                $this->matrnx->get('address', $row) . " " . $this->matrnx->get('location', $row). " " . $this->matrnx->get('cap', $row),
                "Errore indirizzo agenzia"
            ];
            $err[] = "Errore indirizzo Agenzia KA:{$agenzia->indirizzo} {$agenzia->localita} {$agenzia->cap} / GA:" . $this->matrnx->get('address', $row) . " " . $this->matrnx->get('location', $row). " " . $this->matrnx->get('cap', $row);
        }

        //
        //
        // AM/DM
        //
        //
        /*$r = $this->analyzer->differ(
            "{$agenzia->cognomeAM} {$agenzia->nomeAM}",
            $this->matrnx->get('am', $row),
            $agenzia->id,
            "Errore nominativo AM"
        );

        if ($r) {
            $output[] = $r->result;
            $err[] = "Errore nominativo AM KA:{$agenzia->cognomeAM} {$agenzia->nomeAM} / GA:" . $this->matrnx->get('am', $row);
        }*/

        if (trim(strtoupper("{$agenzia->cognomeAM} {$agenzia->nomeAM}")) != $this->matrnx->get('am', $row)) {
            $output[] = [
                $agenzia->id,
                'ERROR',
                'AM',
                "{$agenzia->cognomeAM} {$agenzia->nomeAM}",
                $this->matrnx->get('am', $row),
                "Errore di assegnazione agenzia/AM"
            ];
            $err[] = "Errore nominativo AM KA:{$agenzia->cognomeAM} {$agenzia->nomeAM} / GA:" . $this->matrnx->get('am', $row);
        };

        if ($agenzia->area != $this->matrnx->get('area', $row)) {
            $output[] = [
                $agenzia->id,
                'WARNING',
                'AREA',
                $agenzia->area,
                $this->matrnx->get('area', $row),
                ""
            ];
            $warning[] = "Errore codice area KA:{$agenzia->area} / GA:" . $this->matrnx->get('area', $row);
        }

        if (strtoupper("{$agenzia->cognomeDM} {$agenzia->nomeDM}") != $this->matrnx->get('dm', $row)) {
            $output[] = [
                $agenzia->id,
                'ERROR',
                'DM',
                "{$agenzia->cognomeDM} {$agenzia->nomeDM}",
                $this->matrnx->get('dm', $row),
                ""
            ];
            $err[] = "Errore nominativo DM KA:{$agenzia->cognomeDM} {$agenzia->nomeDM} / GA:" . $this->matrnx->get('dm', $row);
        };

        if ($agenzia->district != $this->matrnx->get('district', $row)) {
            $output[] = [
                $agenzia->id,
                'WARNING',
                'DISTRICT',
                $agenzia->district,
                $this->matrnx->get('district', $row),
                ""
            ];
            $warning[] = "Errore codice district KA:{$agenzia->district} / GA:" . $this->matrnx->get('district', $row);
        }

        //
        //
        // email
        //
        //

        if (! $err && ! $warning) {
            $result['OK']++;
            $this->log("{$agenzia->id} OK");
        }

        if ($err) {
            $result['ERR']++;
            $this->log("{$agenzia->id} ERR " . implode("|", $err));
        }

        if ($warning) {
            $result['W']++;
            $this->log("{$agenzia->id} W " . implode("|", $warning));
        }

        $this->log("===========================");

        return $output;
    }

    protected function convertCode($code) {
        return $this->matrnx->code($code);

        // Default
        if ($code[0] != 'N') {
            return "G" . substr($code, 3);
        }

        return "N" . substr($code, 3);
    }
}
