<?php
namespace batch;
use data\apps\cruscotti\Repository\AgenzieRepository;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response;
use metadigit\lib\util\csv\CsvProcessor;
use data\apps\cruscotti\Repository\AggregatorRepository;

class Cruscotti extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @var \Foothing\Kpi\Manager
     */
    protected $manager;

    /**
     * @var AgenzieRepository
     */
    protected $agenzie;

    /**
     * @var AggregatorRepository
     */
    protected $aggregators;

    protected $year = 2017;

	/**
	 * @batch(description="Cruscotti: ricalcolo")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function refreshAction(Request $Req, Response $Res) {
        try {
            $this->manager->refresh();
            $this->aggregators->touchTimestamps();
        }

        catch (\Exception $ex) {
            $this->log($ex->getMessage());
            $this->log($ex->getTraceAsString());
        }

	}

    /**
     * @batch(description="Cruscotti: import dataset")
     * @param Request $Req
     * @param Response $Res
     */
    public function importAction(Request $Req, Response $Res) {
        $processor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

        $query = <<<QUERY
INSERT INTO `kpi_dataset1`
(agenzia_id, time, auto_ac, re_ac, rs_ac, auto_costi_ac, re_costi_ac, rs_costi_ac, rca_pol_ptf, rca_pol_np, rca_pol_ptf_dic, rca_pol_ptf_autovet, rca_pol_np_autovet, rca_pol_ptf_dic_autovet, rca_pol_ptf_noautovet, rca_pol_np_noautovet, rca_pol_ptf_dic_noautovet, pol_ptf_re_dic, pol_np_re, pol_ptf_re, num_esp, n_colpa, freq, num_esp_prov, n_colpa_prov, freq_provincia, np_vita_gs, np_vita_unit, np_vita_tcm_pol, np_vita_tcm_premi)
VALUES
(:agenzia_id, :time, :auto_ac, :re_ac, :rs_ac, :auto_costi_ac, :re_costi_ac, :rs_costi_ac, :rca_pol_ptf, :rca_pol_np, :rca_pol_ptf_dic, :rca_pol_ptf_autovet, :rca_pol_np_autovet, :rca_pol_ptf_dic_autovet, :rca_pol_ptf_noautovet, :rca_pol_np_noautovet, :rca_pol_ptf_dic_noautovet, :pol_ptf_re_dic, :pol_np_re, :pol_ptf_re, :num_esp, :n_colpa, :freq, :num_esp_prov, :n_colpa_prov, :freq_provincia, :np_vita_gs, :np_vita_unit, :np_vita_tcm_pol, :np_vita_tcm_premi)
ON DUPLICATE KEY UPDATE
agenzia_id=:agenzia_id, time=:time, auto_ac=:auto_ac, re_ac=:re_ac, rs_ac=:rs_ac, auto_costi_ac=:auto_costi_ac, re_costi_ac=:re_costi_ac, rs_costi_ac=:rs_costi_ac, rca_pol_ptf=:rca_pol_ptf, rca_pol_np=:rca_pol_np, rca_pol_ptf_dic=:rca_pol_ptf_dic, rca_pol_ptf_autovet=:rca_pol_ptf_autovet, rca_pol_np_autovet=:rca_pol_np_autovet, rca_pol_ptf_dic_autovet=:rca_pol_ptf_dic_autovet, rca_pol_ptf_noautovet=:rca_pol_ptf_noautovet, rca_pol_np_noautovet=:rca_pol_np_noautovet, rca_pol_ptf_dic_noautovet=:rca_pol_ptf_dic_noautovet, pol_ptf_re_dic=:pol_ptf_re_dic, pol_np_re=:pol_np_re, pol_ptf_re=:pol_ptf_re, num_esp=:num_esp, n_colpa=:n_colpa, freq=:freq, num_esp_prov=:num_esp_prov, n_colpa_prov=:n_colpa_prov, freq_provincia=:freq_provincia, np_vita_gs=:np_vita_gs, np_vita_unit=:np_vita_unit, np_vita_tcm_pol=:np_vita_tcm_pol, np_vita_tcm_premi=:np_vita_tcm_premi
QUERY;

        $stmt = $this->pdoPrepare($query);

        $processFn = function($i, $line, $values) use ($stmt) {
            $values[3] = str_pad($values[3], 2, "0", STR_PAD_LEFT);
            $values[4] = str_pad($values[4], 2, "0", STR_PAD_LEFT);
            $values[5] = str_pad($values[5], 2, "0", STR_PAD_LEFT);
            $values[6] = str_pad($values[6], 2, "0", STR_PAD_LEFT);
            $vars = [
                'agenzia_id' => $values[1],
                'time' => "{$values[2]}{$values[3]}{$values[4]}{$values[5]}{$values[6]}",
                'auto_ac' => $values[7],
                're_ac' => $values[8],
                'rs_ac' => $values[9],
                'auto_costi_ac' => $values[10],
                're_costi_ac' => $values[11],
                'rs_costi_ac' => $values[12],
                'rca_pol_ptf' => $values[13],
                'rca_pol_np' => $values[14],
                'rca_pol_ptf_dic' => $values[15],
                'rca_pol_ptf_autovet' => $values[16],
                'rca_pol_np_autovet' => $values[17],
                'rca_pol_ptf_dic_autovet' => $values[18],
                'rca_pol_ptf_noautovet' => $values[19],
                'rca_pol_np_noautovet' => $values[20],
                'rca_pol_ptf_dic_noautovet' => $values[21],
                'pol_ptf_re_dic' => $values[22],
                'pol_np_re' => $values[23],
                'pol_ptf_re' => $values[24],
                'num_esp' => $values[25],
                'n_colpa' => $values[26],
                'freq' => $values[27],
                'num_esp_prov' => $values[28],
                'n_colpa_prov' => $values[29],
                'freq_provincia' => $values[30],
                'np_vita_gs' => $values[31],
                'np_vita_unit' => $values[32],
                'np_vita_tcm_pol' => $values[33],
                'np_vita_tcm_premi' => $values[34],
            ];
            $stmt->execute($vars);
            return [true, null];
        };
        list($success, $ok, $err, $skip, $tot, $stats, $errors) = $processor->processFile(UPLOAD_DIR . "STATICO_CRUSCOTTI_DATASET1.csv", 35, $processFn);

        $this->log(print_r($errors, true));
    }
}
