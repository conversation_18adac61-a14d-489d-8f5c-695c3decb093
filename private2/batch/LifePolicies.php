<?php

namespace batch;

use api\apps\incentive\rinnoviamoci\exceptions\ColumnsMismatchException;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\CoreTrait;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchTrait;
use PHPExcel_IOFactory;
use service\GroupamaUtils;
use batch\utils\SFTPFileDownload;

class LifePolicies
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{

    const CSV_DATA_COLS = 21;
    const CSV_MU30_COLS = 21;

    const SFTP_HOST = 'SFTPGA.groupama.it';
    const SFTP_PORT = 22;
    const SFTP_USER = 'keyassociatisftp';
    const SFTP_PASS = 'onr2tQgCLZRPNm!hG6ys';

    use PdoTrait, BatchTrait, CoreTrait;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    /**
     * @batch(description="Import file polizze Vita")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importFileAction(Request $Req, Response $Res)
    {

        if (\metadigit\core\ENVIRONMENT === 'PROD') {
            try {
                $sftpDownloader = new SFTPFileDownload(
                    self::SFTP_HOST,
                    self::SFTP_PORT,
                    self::SFTP_USER,
                    self::SFTP_PASS
                );

                $localPath = \metadigit\core\UPLOAD_DIR . "invita_det_incentiv.txt";
                $sftpDownloader->downloadFile("/invita_det_incentiv.txt", $localPath);
                $file = $localPath;

                /*$localPathMU30 = \metadigit\core\UPLOAD_DIR . "monitoraggio_mu30.csv";
                $sftpDownloader->downloadFile("/monitoraggio_mu30.csv", $localPathMU30);
                $fileMU30 = $localPathMU30;*/

            } catch (\Exception $e) {
                $this->l("SFTP Download failed: " . $e->getMessage());
                return false;
            }
        }
        else {
            if (!$file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "invita_det_incentiv.txt")) {
                $this->l("File not found for path: $path");
                return false;
            }
        }


        /*if (!$file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "invita_det_incentiv.txt")) {
            $this->l("File not found for path: $path");
            return false;
        }*/

        /*if (!$fileMU30 = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "monitoraggio_mu30.txt")) {
            $this->l("File not found for path: $path");
            return false;
        }*/
        
        // Check if both files are available
        if (!$file /*|| !$fileMU30*/) {
            $this->l("Uno dei 2 file non è stato trovato. Import fallito.");
            return false;
        }

        $this->pdoStExecute("START TRANSACTION");
        try {
            $this->pdoStExecute("delete from life_data");
            $this->pdoStExecute("ALTER TABLE life_data AUTO_INCREMENT = 1");
            $this->pdoStExecute("COMMIT");
        } catch (\Exception $e) {
            $this->pdoStExecute("ROLLBACK");
            throw $e;
        }

        $statement = $this->pdoPrepare("INSERT INTO life_data (
            periodo,
            dataOsservazione,
            agenzia_id,
            subagenziaDett,
            produttoreDett,
            polizza,
            codiceProdotto,
            nomeProdotto,
            CF_PIVA,
            dataContabile,
            dataScadenza,
            dataEffetto,
            dataEmissione,
            dataIncasso,
            tipoPortafoglio,
            tipoPremioQuietanza,
            tipoPremioPolizza,
            codiceConvenzione,
            percNoUnit,
            percUnit,
            frazionamento,
            premio
        ) VALUES (
            :periodo,
            :dataOsservazione,
            :agenzia_id,
            :subagenziaDett,
            :produttoreDett,
            :polizza,
            :codiceProdotto,
            :nomeProdotto,
            :CF_PIVA,
            :dataContabile,
            :dataScadenza,
            :dataEffetto,
            :dataEmissione,
            :dataIncasso,
            :tipoPortafoglio,
            :tipoPremioQuietanza,
            :tipoPremioPolizza,
            :codiceConvenzione,
            :percNoUnit,
            :percUnit,
            :frazionamento,
            :premio
        )");

        try {
            // Read and convert files
            $fileLines = $this->readFileWithEncodingConversion($file);
            $this->insert($statement, $fileLines, ";", [$this, 'parseLifeDataRow']);
            
            /*$fileLinesMU30 = $this->readFileWithEncodingConversion($fileMU30);
            
            // File aggiuntivo temporaneo specifico per prodotto MU30
            $this->insert($statement, $fileLinesMU30, ";", function($row, $lineNumber) {
                $data = $this->parseMU30DataRow($row, $lineNumber);
                // Cripta CF_PIVA con SHA256
                if (isset($data['CF_PIVA']) && !empty($data['CF_PIVA'])) {
                    $data['CF_PIVA'] = hash('sha256', $data['CF_PIVA']);
                }
                return $data;
            });*/

        } catch (\Exception $e) {
            $this->l("Error parsing file: " . $e->getMessage());
            throw $e;
        }
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        if (! ($count = count($files))) {
            $this->l("File $filepath not found.");
            return null;
        }

        if ($count != 1) {
            $this->l("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    public function l($data)
    {
        if (is_array($data) || is_object($data)) {
            $data = print_r($data, true);
        }

        $this->log($data);
    }

    protected function insert($statement, $data, $separator, $callback)
    {
        array_shift($data);
        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
            'AGENCY_NOT_FOUND' => 0,
            'DUPLICATE_ENTRY' => 0
        ];

        // Scan CSV file.
        foreach ($data as $row) {
            $rowInfo = null; // Initialize rowInfo before try block

            try {
                // Ask the application to parse the row.
                $rowInfo = call_user_func_array($callback, [str_getcsv($row, $separator), $line]);

                // Insert.
                $statement->execute($rowInfo);
                $result['OK']++;
            }
            catch (\PDOException $ex) {
                $result['KO']++;

                if ($ex->getCode() == 23000) {
                    // Check if it's a duplicate entry error
                    if (strpos($ex->getMessage(), 'Duplicate entry') !== false) {
                        $result['DUPLICATE_ENTRY']++;
                        $this->l("Row " . ($line + 1) . ": Duplicate entry");
                    } else {
                        // Agency not found error
                        $result['AGENCY_NOT_FOUND']++;
                        $this->l("Row " . ($line + 1) . ": Agency not found {$rowInfo['agenzia_id']}");
                    }
                }

                $this->l($rowInfo !== null ? print_r($rowInfo, true) : "Row parsing failed");
                $this->l($ex->getMessage());
            }
            catch (\Exception $ex) {
                $result['KO']++;

                if ($rowInfo !== null) {
                    $this->l("Row " . ($line + 1) . ": Skip agenzia {$rowInfo['agenzia_id']}");
                    $this->l(print_r($rowInfo, true));
                } else {
                    $this->l("Row " . ($line + 1) . ": Row parsing failed");
                }
                $this->l($ex->getMessage());
            }

            $line++;
        }

        $this->log("\nProcessing Summary:");
        $this->log("=====================================");
        $this->log("Total Rows Processed:\t$line");
        $this->log("Successful Imports:\t{$result['OK']}\t(" . round(($result['OK']/$line) * 100, 2) . "%)");
        $this->log("Failed Imports:\t\t{$result['KO']}\t(" . round(($result['KO']/$line) * 100, 2) . "%)");
        $this->log("  - Agency Not Found:\t{$result['AGENCY_NOT_FOUND']}");
        $this->log("  - Duplicate Entries:\t{$result['DUPLICATE_ENTRY']}");
        $this->log("=====================================");
    }

    protected function parseLifeDataRow(array $row, $lineNumber = null)
    {
        // Assert columns.
        if (($count = count($row)) < self::CSV_DATA_COLS) {
            throw new ColumnsMismatchException(
                "Columns mismatch: expected " . self::CSV_DATA_COLS . "/ found $count / row: " . print_r($row, true)
            );
        }

        // Trim columns.
        $row = array_map(function ($item) {
            return trim($item);
        }, $row);

        $agenzia_id = $this->utils->convertGroupamaCodeToAgendo(explode(' ', $row[2])[0]);

        return [
            'periodo' => $row[0],
            'dataOsservazione' => $row[1] ? $this->formatDate($row[1]) : null,
            'agenzia_id' => $agenzia_id,
            'subagenziaDett' => $row[3],
            'produttoreDett' => $row[4],
            'polizza' => $row[5],
            'codiceProdotto' => $row[6],
            'nomeProdotto' => $row[7],
            'CF_PIVA' => $row[8],
            'dataContabile' => $row[9] ? $this->formatDate($row[9]) : null,
            'dataScadenza' => $row[10] ? $this->formatDate($row[10]) : null,
            'dataEffetto' => $row[11] ? $this->formatDate($row[11]) : null,
            'dataEmissione' => $row[12] ? $this->formatDate($row[12]) : null,
            'dataIncasso' => $row[13] ? $this->formatDate($row[13]) : null,
            'tipoPortafoglio' => $row[14],
            'tipoPremioQuietanza' => $row[15],
            'tipoPremioPolizza' => $row[16],
            'codiceConvenzione' => $row[17],
            'percNoUnit' => $row[18],
            'percUnit' => $row[19],
            'frazionamento' => $row[20],
            'premio' => str_replace('.', '', $row[21])
        ];
    }

    /**
     * @throws ColumnsMismatchException
     * @throws \Exception
     */
    protected function parseMU30DataRow(array $row, $lineNumber = null)
    {
        // Assert columns.
        if (($count = count($row)) < self::CSV_MU30_COLS) {
            throw new ColumnsMismatchException(
                "Columns mismatch: expected " . self::CSV_MU30_COLS . "/ found $count / row: " . print_r($row, true)
            );
        }

        // Trim columns.
        $row = array_map(function ($item) {
            return trim($item);
        }, $row);

        try {
            $agenzia_id = $this->utils->convertGroupamaCode4CharactersToAgendo((string)$row[17]);
        } catch (\Exception $e) {
            $this->l("Error converting agency code at row " . ($lineNumber + 1) . ": " . (string)$row[17]);
            $this->l("Error message: " . $e->getMessage());
            throw $e; // Re-throw the exception to maintain the original error handling
        }

        return [
            'periodo' => null,
            'dataOsservazione' => null,
            'agenzia_id' => $agenzia_id,
            'subagenziaDett' => null,
            'produttoreDett' => null,
            'polizza' => $row[7],
            'codiceProdotto' => $row[8],
            'nomeProdotto' => "GROUPAMA INVESTIMENTO PROTETTO",
            'CF_PIVA' => $row[11],
            'dataContabile' => $row[19] ? $this->formatDate($this->removeTimeFromDate($row[19])) : null,
            'dataScadenza' => $row[19] ? $this->formatDate($this->removeTimeFromDate($row[19])) : null,
            'dataEffetto' => $row[19] ? $this->formatDate($this->removeTimeFromDate($row[19])) : null,
            'dataEmissione' => $row[19] ? $this->formatDate($this->removeTimeFromDate($row[19])) : null,
            'dataIncasso' => $row[19] ? $this->formatDate($this->removeTimeFromDate($row[19])) : null,
            'tipoPortafoglio' => null,
            'tipoPremioQuietanza' => "Perfezionamento",
            'tipoPremioPolizza' => "Premio Annuo",
            'codiceConvenzione' => null,
            'percNoUnit' => null,
            'percUnit' => null,
            'frazionamento' => null,
            'premio' => str_replace(',', '.', $row[1])
        ];
    }

    protected function formatDate($date)
    {
        if (empty($date)) {
            return null;
        }

        $dateObj = \DateTime::createFromFormat('d/m/Y', $date);
        if ($dateObj === false) {
            $this->l("Invalid date format: $date");
            return null;
        }

        return $dateObj->format('Y-m-d');
    }

    /**
     * Removes time portion from date string
     * @param string $dateString Date string in format like "27/5/2025 00:00:00"
     * @return string Date string without time portion
     */
    protected function removeTimeFromDate($dateString)
    {
        // Split by space and return only the date part
        $parts = explode(' ', $dateString);
        return $parts[0];
    }

    /**
     * @batch(description="Elenca i file attualmente nel server")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function listSftpFilesAction(Request $Req, Response $Res)
    {
        try {
            $sftpDownloader = new SFTPFileDownload(
                self::SFTP_HOST,
                self::SFTP_PORT,
                self::SFTP_USER,
                self::SFTP_PASS
            );

            // List files in root directory
            $files = $sftpDownloader->listFiles('/');

            // Print file information
            foreach ($files as $file) {
                $this->l(sprintf(
                    "File: %s | Size: %s bytes | Modified: %s | Type: %s",
                    $file['name'],
                    $file['size'],
                    $file['modified'],
                    $file['is_dir'] ? 'Directory' : 'File'
                ));
            }

        } catch (\Exception $e) {
            $this->l("SFTP List failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * @batch(description="Test SFTP connection with detailed diagnostics")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function testSftpConnectionAction(Request $Req, Response $Res)
    {
        try {
            $sftpDownloader = new SFTPFileDownload(
                self::SFTP_HOST,
                self::SFTP_PORT,
                self::SFTP_USER,
                self::SFTP_PASS
            );

            $results = $sftpDownloader->testConnection();

            // Print test results
            $this->l("SFTP Connection Test Results:");
            foreach ($results as $key => $value) {
                if ($key === 'password') continue; // Skip printing password
                $this->l("- $key: " . (is_bool($value) ? ($value ? 'Success' : 'Failed') : $value));
            }

        } catch (\Exception $e) {
            $this->l("SFTP Test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Reads a file and converts its encoding to UTF-8 if necessary
     * 
     * @param string $filePath Path to the file
     * @return array Array of lines from the file in UTF-8 encoding
     */
    protected function readFileWithEncodingConversion($filePath)
    {
        // Read the first few bytes to check for BOM
        $handle = fopen($filePath, 'rb');
        $bom = fread($handle, 2);
        rewind($handle);
        fclose($handle);
        
        $fileContent = file_get_contents($filePath);
        
        // Check for UTF-16 BOM markers
        $isUtf16LE = (substr($fileContent, 0, 2) === "\xFF\xFE");
        $isUtf16BE = (substr($fileContent, 0, 2) === "\xFE\xFF");
        
        if ($isUtf16LE) {
            $this->l("Detected UTF-16LE BOM marker");
            $encoding = 'UTF-16LE';
        } elseif ($isUtf16BE) {
            $this->l("Detected UTF-16BE BOM marker");
            $encoding = 'UTF-16BE';
        } else {
            $encoding = mb_detect_encoding($fileContent, ['UTF-8', 'UTF-16', 'UTF-16BE', 'UTF-16LE'], true);
            $this->l("Detected encoding for file $filePath: " . ($encoding ?: 'Unknown'));
        }
        
        // If encoding is still unknown, force UTF-16LE (common for Windows-generated files)
        if (!$encoding) {
            $this->l("Encoding detection failed. Forcing UTF-16LE conversion as fallback");
            $encoding = 'UTF-16LE';
        }
        
        if ($encoding !== 'UTF-8') {
            $this->l("Converting file from $encoding to UTF-8");
            $fileContent = mb_convert_encoding($fileContent, 'UTF-8', $encoding);
            // Remove BOM if present after conversion
            $fileContent = str_replace("\xEF\xBB\xBF", '', $fileContent);
            return explode("\n", $fileContent);
        } else {
            return file($filePath);
        }
    }

}
