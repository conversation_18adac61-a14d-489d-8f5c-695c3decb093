<?php namespace batch;

use data\AgenzieRepository;
use data\apps\accordo2\Models\Accordo;
use data\apps\cmsAgenzie\AgenciesDataRepository;
use data\apps\cmsAgenzie\EmployeesRepository;
use data\User;
use data\UsersRepository;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
    metadigit\core\db\orm\Repository;
use service\GroupamaUtils;

class CmsAgenzie extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait, \metadigit\core\CoreTrait;

    protected $COLS = 24;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    /**
     * @var AgenzieRepository
     */
    protected $agenzieLegacy;

    /**
     * @var AgenciesDataRepository
     */
    protected $agenzieCMS;

    /**
     * @var UsersRepository
     */
    protected $usersLegacy;

    /**
     * @var EmployeesRepository
     */
    protected $usersCMS;

	 // Disabilitato
	 // @batch(description="CMS Agenzie: Aggiornamento dati Agenzie da file")
	 // @param Request $Req
	 // @param Response $Res
	 // @throws \metadigit\lib\util\csv\Exception

	function _importAgencyDataAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "rete_agenziale*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->importAgencyData(file($files[0]));
    }

    protected function importAgencyData($data)
    {
        $rowsParsed = -1;
        $countAgenzieOn = $this->agenzieLegacy->count("status,EQ,ON");
        $countUpdatedLegacy = 0;
        $countUpdatedCMS = 0;
        $countAgenzieNonPresenti = 0;
        $countUpdateLegacyErrors = 0;
        $countUpdateCMSErrors = 0;
        $countInsertErrors = 0;

        foreach ($data as $row) {
            $rowsParsed++;

            $row = str_getcsv($row, ",");

            if ($row[0] === 'Codice') {
                // Skip csv headers row
                continue;
            }

            try {
                $id_agenzia = $this->utils->convertGroupamaCodeToAgendo($row[0]);
            } catch (\Exception $ex) {
                $this->log("Impossibile convertire codice Groupama: $row[0].");
                continue;
            }

            $agenziaLegacy = $this->parseRowAgenziaLegacy($row, $id_agenzia);
            $agenzieLegacyStatement = $this->statementAgenzieLegacy();

            // Controllo che Agenzia sia presente nel db legacy
            if (! $result = $this->agenzieLegacy->fetch($id_agenzia)) {
                $this->log("Agenzia: $id_agenzia non trovata. SKIP UPDATE.");
                $countAgenzieNonPresenti++;
                $this->log("========================================================================================");
                continue;
            }

            // Aggiornamento Agenzia legacy
            try {
                $this->pdoStExecute($agenzieLegacyStatement, $agenziaLegacy);
                $this->log("Agenzia legacy aggiornata: $id_agenzia");
                $countUpdatedLegacy++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($agenziaLegacy, 1));
                $this->log("Errore aggiornamento Agenzia legacy. Riga: $rowsParsed, Agenzia: $id_agenzia");
                $this->log($ex->getMessage());
                $countUpdateLegacyErrors++;
                $this->log("========================================================================================");
                continue;
            }

            $agenziaCMS = $this->parseRowAgenziaCMS($row, $id_agenzia);
            $agenzieCMSStatement = $this->statementAgenzieCMS();

            if ($agencyIsAlreadyRegistered = $this->agenzieCMS->fetch($id_agenzia)) {
                // Aggiornamento Agenzia CMS
                try {
                    $this->pdoStExecute($agenzieCMSStatement, $agenziaCMS);
                    $this->log("Agenzia CMS aggiornata: $id_agenzia");
                    $countUpdatedCMS++;
                } catch (\Exception $ex) {
                    $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($agenziaCMS, 1));
                    $this->log("Errore aggiornamento Agenzia CMS. Riga: $rowsParsed, Agenzia: $id_agenzia");
                    $this->log($ex->getMessage());
                    $countUpdateCMSErrors++;
                    $this->log("========================================================================================");
                    continue;
                }
                $this->log("========================================================================================");
                continue;
            }

            // Inserimento Agenzia CMS
            try {
                $this->pdoStExecute($agenzieCMSStatement, $agenziaCMS);
                $defaultSchedule = $this->getDefaultSchedule($id_agenzia);
                $scheduleStatement = $this->statementSchedule();
                foreach ($defaultSchedule as $day) {
                    $this->pdoStExecute($scheduleStatement, $day);
                }
                $this->log("Agenzia CMS aggiunta: $id_agenzia");
                $countUpdatedCMS++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($agenziaCMS, 1));
                $this->log("Errore aggiunta Agenzia CMS. Riga: $rowsParsed, Agenzia: $id_agenzia");
                $this->log($ex->getMessage());
                $countInsertErrors++;
                $this->log("========================================================================================");
                continue;
            }

            $this->log("========================================================================================");

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Righe analizzate nel file: $rowsParsed");
        $this->log("Agenzie attive (ON) nel DB: $countAgenzieOn");
        $this->log("Agenzie legacy aggiornate: $countUpdatedLegacy, errori: $countUpdateLegacyErrors");
        $this->log("Agenzie CMS aggiornate: $countUpdatedCMS, errori: $countUpdateCMSErrors");
        $this->log("Agenzie CMS inserite: $countUpdatedCMS, errori: $countInsertErrors");
        $this->log("Agenzie presenti nel file ma non trovate nel db: $countAgenzieNonPresenti");


    }

    protected function statementAgenzieLegacy()
    {
        return "UPDATE agenzie SET
            indirizzo = :indirizzo,
            telefono = :telefono,
            fax = :fax,
            email = :email
            WHERE id = :id
        ";
    }

    protected function parseRowAgenziaLegacy($row, $id)
    {
        return [
            'id' => $id,
            'indirizzo' => $row[8],
            'telefono' => $row[13],
            'fax' => $row[15],
            'email' => $row[22],
        ];
    }

    protected function statementAgenzieCMS()
    {
        return "
            INSERT INTO cms_agenzie_data
              (
              agenzia_id,
              agencyEntrancePhoto,
              pec,
              approved,
              url,
              whatsapp,
              description,
              text,
              showWeeklySchedule
              )
            values(
              :agenzia_id,
              :agencyEntrancePhoto,
              :pec,
              :approved,
              :url,
              :whatsapp,
              :description,
              :text,
              :showWeeklySchedule
            )
            on duplicate key update
              pec=:pec
        ";
    }

    protected function parseRowAgenziaCMS($row, $id)
    {
        return [
            'agenzia_id' => $id,
            'agencyEntrancePhoto' => null,
            'pec' => $row[23],
            'approved' => 1,
            'url' => null,
            'whatsapp' => null,
            'description' => null,
            'text' => null,
        ];
    }

    protected function statementSchedule()
    {
        return "
            INSERT INTO cms_agenzie_orari
              (
              agenzia_id,
              day,
              t1,
              t2,
              t3,
              t4,
              closed
              )
            values(
              :agenzia_id,
              :day,
              :t1,
              :t2,
              :t3,
              :t4,
              :closed
            )
        ";
    }

    protected function getDefaultSchedule($id) {
        return [
            [
                'agenzia_id' => $id,
                'day' => 1,
                't1' => '09:00',
                't2' => '13:00',
                't3' => '15:00',
                't4' => '18:00',
                'closed' => 0,
            ],
            [
                'agenzia_id' => $id,
                'day' => 2,
                't1' => '09:00',
                't2' => '13:00',
                't3' => '15:00',
                't4' => '18:00',
                'closed' => 0,
            ],
            [
                'agenzia_id' => $id,
                'day' => 3,
                't1' => '09:00',
                't2' => '13:00',
                't3' => '15:00',
                't4' => '18:00',
                'closed' => 0,
            ],
            [
                'agenzia_id' => $id,
                'day' => 4,
                't1' => '09:00',
                't2' => '13:00',
                't3' => '15:00',
                't4' => '18:00',
                'closed' => 0,
            ],
            [
                'agenzia_id' => $id,
                'day' => 5,
                't1' => '09:00',
                't2' => '13:00',
                't3' => '15:00',
                't4' => '18:00',
                'closed' => 0,
            ],
            [
                'agenzia_id' => $id,
                'day' => 6,
                't1' => null,
                't2' => null,
                't3' => null,
                't4' => null,
                'closed' => 1,
            ],
            [
                'agenzia_id' => $id,
                'day' => 7,
                't1' => null,
                't2' => null,
                't3' => null,
                't4' => null,
                'closed' => 1,
            ]
        ];
    }

     // Disabilitato
     // @batch(description="CMS Agenzie: trascrizione agenti e intermediari")
     // @param Request $Req
     // @param Response $Res
     // @throws \metadigit\lib\util\csv\Exception
     //
    function _transcribeUsersAction(Request $Req, Response $Res)
    {
        $countActiveUsers = $this->usersLegacy->count("active,EQ,1|type,IN,AGENTE,INTERMEDIARIO");
        $countSkippedUsers = 0;
        $countAddedUsers = 0;
        $countFailedUsers = 0;

        $activeUsers = $this->usersLegacy->fetchAll(null,null,null,"active,EQ,1|type,IN,AGENTE,INTERMEDIARIO", Repository::FETCH_ARRAY);
        $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, "DEBUG", $countActiveUsers);

        foreach ($activeUsers as $user) {

            if ($userIsAlreadyRegistered = $this->usersCMS->fetch($user['id'])) {
                $this->log("Utente già presente nel CMS. SKIP INSERT.");
                $this->log("========================================================================================");
                $countSkippedUsers++;
                continue;
            }

            $userData = $this->formatUserData($user);
            $userStatement = $this->statementUser();

            try {
                $this->pdoStExecute($userStatement, $userData);
                $this->log("Utente inserito: {$user['nome']} {$user['cognome']} - {$user['agenzia_id']}");
                $countAddedUsers++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($userData, 1));
                $this->log("Impossibile inserire utente: {$user['nome']} {$user['cognome']} - {$user['agenzia_id']}.");
                $this->log($ex->getMessage());
                $this->log("========================================================================================");
                $countFailedUsers++;
                continue;
            }

            $this->log("========================================================================================");

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Utenti attivi presenti nel db: $countActiveUsers");
        $this->log("Utenti già presenti nel cms (skippati): $countSkippedUsers");
        $this->log("Utenti inseriti: $countAddedUsers");
        $this->log("Inserimenti falliti: $countFailedUsers");

    }

    protected function formatUserData($data)
    {
        return [
            'user_id' => $data['id'],
            'agenzia_id' => $data['agenzia_id'],
            'photo' => null,
            'position' => null,
            'description' => null,
            'showOnFrontend' => 0,
            'privacy' => 'UNSET'
        ];
    }

    protected function statementUser()
    {
        return "
            INSERT INTO cms_agenzie_impiegati
              (
              user_id,
              agenzia_id,
              photo,
              position,
              description,
              showOnFrontend,
              privacy
              )
            values(
              :user_id,
              :agenzia_id,
              :photo,
              :position,
              :description,
              :showOnFrontend,
              :privacy
            )
        ";
    }

    // Disabilitato
     // @batch(description="CMS Agenzie: Rimuove dal cms le agenzie non presenti nel file Intrama")
     // @param Request $Req
     // @param Response $Res
     // @throws \metadigit\lib\util\csv\Exception

    function deleteClosedAgenciesAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "rete_agenziale*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->deleteAgencies(file($files[0]));
    }

    protected function deleteAgencies($fileData)
    {

        $foundAgencies = 0;
        $missingAgencies = 0;
        $cmsData = $this->agenzieCMS->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);

        $fileDataArray = array();
        foreach ($fileData as $row) {
            if ($row[0] === 'Codice') {
                // Skip csv headers row
                continue;
            }
            $fileDataArray[] = str_getcsv($row);
        }
        //$this->log(print_r($fileDataArray, 1));

        foreach ($cmsData as $agency) {

            try {
                $id_agenzia = $this->utils->convertAgencyCodeToGroupama($agency['id']);
            } catch (\Exception $ex) {
                $this->log("Impossibile convertire codice Groupama: {$agency['id']}.");
                continue;
            }

            if ( in_array($id_agenzia, $fileDataArray) ) {
                $foundAgencies++;
                continue;
            }

            $missingAgencies++;

            // @TODO: cancellare le agenzie che non sono state trovate dentro il file

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Agenzie trovate nel file: $foundAgencies");
        $this->log("Agenzie NON trovate nel file: $missingAgencies");

    }

    /**
     * @batch(description="CMS Agenzie: Inizializzazione status agenzia per nuova struttura versioning")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function importAgenciesStatusAction(Request $Req, Response $Res) {

        $cmsData = $this->pdoQuery("SELECT * FROM cms_agenzie_data")->fetchAll(Repository::FETCH_ARRAY);
        $countCmsAgencies = $this->pdoQuery("SELECT COUNT(*) FROM cms_agenzie_data")->execute();
        $countStatusCreation = 0;
        $countStatusCreationFailed = 0;
        $countDuplicatedAgencies = 0;
        $countDuplicatedAgenciesFailed = 0;

        foreach ($cmsData as $agency) {

            $agencyStatusData = $this->formatAgencyStatusData($agency);
            $agencyStatusStatement = $this->statementAgencyStatus();

            try {
                $this->pdoStExecute($agencyStatusStatement, $agencyStatusData);
                $this->log("Status Agenzia {$agency["agenzia_id"]} creato.");
                $countStatusCreation++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($agency, 1));
                $this->log("Impossibile creare status agenzia: {$agency["agenzia_id"]}");
                $this->log($ex->getMessage());
                $this->log("========================================================================================");
                $countStatusCreationFailed++;
                continue;
            }

            try {
                $agency["approved"] = ! $agency["approved"];
                $this->pdoStExecute($this->statementAgenzieCMS(), $agency);
                $this->log("Dato Agenzia {$agency["agenzia_id"]} duplicato.");
                $countDuplicatedAgencies++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($agency, 1));
                $this->log("Impossibile duplicare dato agenzia: {$agency["agenzia_id"]}");
                $this->log($ex->getMessage());
                $this->log("========================================================================================");
                $countDuplicatedAgenciesFailed++;
                continue;
            }

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Agenzie presenti nel cms: $countCmsAgencies");
        $this->log("Status agenzia creati: $countStatusCreation");
        $this->log("Status agenzia falliti: $countStatusCreationFailed");
        $this->log("Dati agenzia duplicati: $countDuplicatedAgencies");
        $this->log("Duplicamenti dati agenzia falliti: $countDuplicatedAgenciesFailed");

    }

    protected function formatAgencyStatusData($data)
    {
        return [
            'agenzia_id' => $data['agenzia_id'],
            'approved' => $data['approved'],
            'user_id' => null,
            'updated_at' => null,
        ];
    }

    protected function statementAgencyStatus()
    {
        return "
            INSERT INTO cms_agenzie_status
              (
              agenzia_id,
              approved,
              user_id,
              updated_at
              )
            values(
              :agenzia_id,
              :approved,
              :user_id,
              :updated_at
            )
        ";
    }

    /**
     * @batch(description="CMS Agenzie: Duplicazione membri team per inizializzazione versioning")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function employeesVersioningInitAction(Request $Req, Response $Res) {

        $employees = $this->pdoQuery("SELECT * FROM cms_agenzie_impiegati")->fetchAll(Repository::FETCH_ARRAY);
        $countEmployees = $this->pdoQuery("SELECT COUNT(*) FROM cms_agenzie_impiegati")->execute();
        $countDuplicatedEmployees = 0;
        $countFailedDuplicatedEmployees = 0;

        foreach ($employees as $employee) {

            $employeeData = $this->employeeData($employee);
            $employeeDataStatement = $this->statementEmployeeData();

            try {
                $this->pdoStExecute($employeeDataStatement, $employeeData);
                $this->log("Utente {$employee["user_id"]} duplicato.");
                $countDuplicatedEmployees++;
            } catch (\Exception $ex) {
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($employee, 1));
                $this->log("Impossibile duplicare utente: {$employee["user_id"]}");
                $this->log($ex->getMessage());
                $this->log("========================================================================================");
                $countFailedDuplicatedEmployees++;
                continue;
            }

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Impiegati presenti nel DB: $countEmployees");
        $this->log("Impiegati duplicati: $countDuplicatedEmployees");
        $this->log("Impiegati falliti: $countFailedDuplicatedEmployees");

    }

    protected function employeeData($data)
    {
        return [
            'user_id' => $data['user_id'],
            'agenzia_id' => $data['agenzia_id'],
            'approved' => 1,
            'photo' => $data['photo'],
            'position' => $data['position'],
            'description' => $data['description'],
            'showOnFrontend' => $data['showOnFrontend'],
            'privacy' => $data['privacy'],
            'privacyUpdatedAt' => $data['privacyUpdatedAt'],
        ];
    }

    protected function statementEmployeeData()
    {
        return "
            INSERT INTO cms_agenzie_impiegati
              (
                user_id,
                agenzia_id,
                approved,
                photo,
                position,
                description,
                showOnFrontend,
                privacy,
                privacyUpdatedAt
              )
            values(
                :user_id,
                :agenzia_id,
                :approved,
                :photo,
                :position,
                :description,
                :showOnFrontend,
                :privacy,
                :privacyUpdatedAt
            )
        ";
    }

    /**
     * @batch(description="CMS Agenzie: individuazione agenzie 'standard'")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function standardAgenciesInitAction(Request $Req, Response $Res) {

        $approvedAgencies = $this->pdoQuery("SELECT * FROM cms_agenzie_status WHERE approved = 1")->fetchAll(Repository::FETCH_ARRAY);
        $countAgencies = 0;
        $countPristineAgencies = 0;
        $countEditedAgencies = 0;

        foreach ($approvedAgencies as $agency) {
            $this->log("Analizzo agenzia: {$agency["agenzia_id"]}");
            $countAgencies++;

            $agencyData = $this->pdoQuery("SELECT * FROM cms_agenzie_data WHERE approved = 1 and agenzia_id = '{$agency["agenzia_id"]}'")->fetch(\PDO::FETCH_ASSOC);
            $agencyEmployees = $this->pdoQuery("SELECT * FROM cms_agenzie_impiegati WHERE approved = 1 and agenzia_id = '{$agency["agenzia_id"]}'")->fetchAll(Repository::FETCH_ARRAY);

            if ( $agencyDataIsPristine = $this->isAgencyDataPristine($agencyData) ) {
                $this->log("Dati cms standard.");
            }

            if ( $agencyEmployeesArePristine = $this->areAgencyEmployeesPristine($agencyEmployees) ) {
                $this->log("Tutti i membri del team standard.");
            }

            if ( $agencyDataIsPristine && $agencyEmployeesArePristine ) {
                $countPristineAgencies++;
                $this->log("Agenzia risulta standard.");
                $this->pdoQuery("UPDATE cms_agenzie_status SET standard = 1 WHERE agenzia_id = '{$agency["agenzia_id"]}'")->execute();
                $this->log("========================================================================================");
                continue;
            }

            $countEditedAgencies++;
            $this->log("Agenzia risulta modificata.");
            $this->log("========================================================================================");

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Agenzie analizzate: $countAgencies");
        $this->log("Agenzie standard: $countPristineAgencies");
        $this->log("Agenzie modificate: $countEditedAgencies");

    }

    protected function isAgencyDataPristine($data) : bool
    {
        // Ritorna true se tutti i campi del cms sono null e gli orari non sono visibili
        return
            empty($data['agencyEntrancePhoto']) &&
            empty($data['url']) &&
            empty($data['description']) &&
            empty($data['text']) &&
            empty($data['whatsapp']) &&
            $data['showWeeklySchedule'] == 0;
    }

    protected function areAgencyEmployeesPristine($employees)
    {
        $pristine = true;
        foreach ($employees as $employee) {
            if ($employee['privacy'] !== 'UNSET') {
                $pristine = false;
                break;
            }
        }
        return $pristine;
    }

    /**
     * @batch(description="CMS Agenzie: Conversione codici Agendo in codici Groupama")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function convertAgendoCodeToGroupamaAction(Request $Req, Response $Res) {

        $agencies = $this->pdoQuery("SELECT * FROM cms_agenzie_status")->fetchAll(Repository::FETCH_ARRAY);
        $countAgencies = count($agencies);
        $countConvertedAgencies = 0;

        foreach ($agencies as $agency) {

            $this->log("Converto Agenzia: {$agency["agenzia_id"]}");

            if ( ! $groupamaCode = $this->utils->convertAgencyCodeToGroupama($agency["agenzia_id"]) ) {
                $this->log("========================================================================================");
                continue;
            }

            $this->log("Nuovo codice Agenzia: {$groupamaCode}");
            $this->pdoStExecute("UPDATE cms_agenzie_status SET groupama_id = :converted_id WHERE agenzia_id = '{$agency["agenzia_id"]}'", ["converted_id" => $groupamaCode]);
            $countConvertedAgencies++;
            $this->log("========================================================================================");

        }

        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("========================================================================================");
        $this->log("SOMMARIO:");
        $this->log("Agenzie convertite: $countConvertedAgencies/$countAgencies");

    }

    /**
     * @batch(description="CMS Agenzie: Import link diretti Mypage da file")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function importMypageLinksAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "mypage-link.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->processMypageLinkData(file($files[0]));
    }

    protected function processMypageLinkData($data) {

        $rowInFile = count($data);
        $agenciesNotFound =  0;
        $agenciesFound =  0;
        $linksSaved = 0;
        $linksNotSaved = 0;

        foreach ($data as $row) {
            $explodedRow = explode(";", $row);

            $GACode = trim($explodedRow[1]);

            $cleanLink = preg_replace('/\s+/', '', $explodedRow[0]);
            $urlWithoutDomain = str_replace("https://www.groupama.it/trova-agente", "", $cleanLink);

            if (empty($GACode)) {
                $GACode = strtoupper(substr($cleanLink, -6));
            }

            $cleanShortLink = preg_replace('/\s+/', '', $explodedRow[2]);
            $shortLink = str_replace("https://www.groupama.it", "", $cleanShortLink);

            $this->log("Link: $cleanLink");
            $this->log("Short link: $shortLink");

            $agendoCode = $this->utils->convertGroupamaCodeToAgendo($GACode);
            $this->log("Codice GA estrapolato: $GACode convertito in: $agendoCode.");

            $agency = $this->pdoStExecute("SELECT * FROM cms_agenzie_status WHERE agenzia_id = '$agendoCode'");

            if ( ! $agency ) {
                $this->log("Agenzia: $agendoCode non trovata.");
                $this->log("=========================================");
                $agenciesNotFound++;
                continue;
            }

            $agenciesFound++;

            try {
                $this->pdoStExecute("UPDATE cms_agenzie_status SET link = '$urlWithoutDomain', shortLink = '$shortLink' WHERE agenzia_id = '$agendoCode'");
                $this->log("Link salvato per Agenzia: $agendoCode.");
                $this->log("=========================================");
                $linksSaved++;
            }
            catch(\Exception $Ex) {
                $this->log("Impossibile salvare link agenzia.");
                $this->log("Errore: {$Ex->getMessage()}");
                $this->log("=========================================");
                $linksNotSaved++;
            }

        }

        $this->log("=========================================");
        $this->log("=========================================");
        $this->log("=========================================");
        $this->log("SOMMARIO:");
        $this->log("Righe presenti nel csv: $rowInFile.");
        $this->log("Agenzie trovate: $agenciesFound.");
        $this->log("Agenzie NON trovate: $agenciesNotFound.");
        $this->log("Link salvati: $linksSaved.");
        $this->log("Link NON salvati: $linksNotSaved.");

    }

    /**
     * @batch(description="CMS Agenzie: Import ragione sociale Agenzie da file")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function importRagioneSocialeAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "mypage-import-ragione-sociale.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $data = file($files[0]);
        $fileRows = count($data);
        $agenciesNotFound = 0;
        $agenciesFound = 0;
        $agenciesUpdated = 0;
        $agenciesNotUpdated = 0;

        foreach ($data as $row) {

            $rowData = explode(";", $row);
            $agendoCode = $this->utils->convertGroupamaCodeToAgendo($rowData[1]);


            if (! $agency = $this->pdoStExecute("SELECT * FROM cms_agenzie_status WHERE agenzia_id = '$agendoCode'") ) {
                $this->log("Agenzia: $agendoCode non presente nel db.");
                $this->log("=========================================");
                $agenciesNotFound++;
                continue;
            }

            $agenciesFound++;
            $cleanedName = addslashes($rowData[0]);

            try {
                $this->pdoStExecute("UPDATE cms_agenzie_status SET ragioneSociale = '$cleanedName' WHERE agenzia_id = '$agendoCode'");
                $this->log("Agenzia: $agendoCode aggiornata.");
                $this->log("=========================================");
                $agenciesUpdated++;
            }
            catch(\Exception $Ex) {
                $this->log("Impossibile salvare ragione sociale agenzia.");
                $this->log("Errore: {$Ex->getMessage()}");
                $this->log("=========================================");
                $agenciesNotUpdated++;
            }

        }

        $this->log("=========================================");
        $this->log("=========================================");
        $this->log("=========================================");
        $this->log("SOMMARIO:");
        $this->log("Righe presenti nel csv: $fileRows.");
        $this->log("Agenzie trovate: $agenciesFound.");
        $this->log("Agenzie NON trovate: $agenciesNotFound.");
        $this->log("Agenzie aggiornate: $agenciesUpdated.");
        $this->log("Agenzie NON aggiornate: $agenciesNotUpdated.");

    }

}
