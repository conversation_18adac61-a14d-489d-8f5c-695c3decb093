<?php namespace batch;

use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\console\controller\ActionController;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchInterface;
use metadigit\lib\batch\BatchTrait;

class Neo extends ActionController implements BatchInterface {

    use PdoTrait, BatchTrait;

    protected static $PATH_PUBLIC = 'apps/neo/neo';
    protected static $PATH_PRIVATE = 'apps/neo/agenti';

    /**
     * @batch(description="Neo: importa materiale didattico")
     *
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     *
     * @return null
     */
    public function importAction(Request $Req, Response $Res) {
        $this->importPublic();
        $this->importPrivate();
    }

    protected function importPrivate() {
        $group = [
            $this->findFiles(static::$PATH_PRIVATE . '/attestato', 'CERT'),
            $this->findFiles(static::$PATH_PRIVATE . '/verbale', 'REPORT'),
            $this->findFiles(static::$PATH_PRIVATE . '/test', 'TEST', '/-correzione/', 0),
            $this->findFiles(static::$PATH_PRIVATE . '/test', 'RESULT', '/-correzione/'),
        ];

        foreach ($group as $files) {
            $this->insert($files);
        }
    }

    protected function importPublic() {
        $files = $this->findFiles(static::$PATH_PUBLIC, 'DOC');

        $this->insert($files);
    }

    protected function findFiles($root, $type, $pattern = null, $expectedCheck = 1) {
        $files = [];

        foreach (scandir(DOWNLOAD_DIR . $root) as $file) {
            if (in_array($file, ['.', '..'])) {
                continue;
            }

            if (is_dir($directory = DOWNLOAD_DIR . "$root/$file")) {
                $this->log("Skip directory $directory");
                continue;
            }

            if ($pattern && preg_match($pattern, $file) !== $expectedCheck) {
                //$this->log("Pattern doesnt match: $file");
                continue;
            }

            //$filename = preg_replace("/ /", "_", $file);

            if (preg_match("/ /", $file)) {
                $this->log("Errore nel nome del file $file");
            }

            $files[] = (object)[
                'hash' => md5($file),
                'title' => $file,
                'type' => $type,
                'uri' => "$root/$file",
                'time' => date('Y-m-d H:i:s')
            ];
        }

        return $files;
    }

    protected function insert($files) {
        $sql = <<<QUERY
INSERT INTO formaz_neo_doc (parent_id, hash, title, type, uri, uploadedAt)
    VALUES (:parent_id, :hash, :title, :type, :uri, :time)
ON DUPLICATE KEY UPDATE
    hash = :hash,
    title = :title,
    uploadedAt = :time
QUERY;

        $statement = $this->pdoPrepare($sql);

        $fetchStatement = $this->pdoPrepare("select id from formaz_neo_doc where hash = :hash");

        foreach ($files as $file) {
            $params = [
                ':hash' => $file->hash,
                ':title' => $file->title,
                ':type' => $file->type,
                ':uri' => $file->uri,
                ':time' => date('Y-m-d H:i:s'),
                ':parent_id' => null,
            ];

            if ($file->type == 'RESULT') {
                $hash = md5(preg_replace("/-correzione/", "", $file->title));
                //$this->log("Find for parent $hash");
                $fetchStatement->execute([':hash' => $hash]);

                if ($parent = $fetchStatement->fetchColumn(0)) {
                    $params[':parent_id'] = $parent;
                    //$this->log("Parent: $parent");
                }
            }

            $statement->execute($params);
        }
    }
}
