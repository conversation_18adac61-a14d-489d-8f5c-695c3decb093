<?php

namespace batch;


use data\apps\eventmanager\EnrollmentRepository;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\core\mail\Mailer;
use metadigit\core\util\Date;

class EventManager
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
    use \metadigit\lib\batch\BatchTrait, PdoTrait;

    /**
     * @var Mailer
     */
    protected $mailer;

    const MAIL_REGISTRATION_BODY = (__DIR__ . "/tpl/email-registration-body.phtml");
    const MAIL_REMINDER_BODY = (__DIR__ . "/tpl/email-reminder-body.phtml");

    public $mailData = [
        "registration" => [
            "title" => "Roadshow 2025 - Conferma prenotazione",
            "body" => self::MAIL_REGISTRATION_BODY,
        ],
        "reminder" => [
            "title" => "Sei pronto a partecipare a Roadshow 2025?",
            "body" => self::MAIL_REMINDER_BODY,
        ]
    ];

    /**
     * Get pending recipients and send email
     *
     * @batch(description="Gestore eventi: invia email di conferma a tutti i neo registrati")
     * @throws \Exception
     */
    function sendRegistrationNotificationAction(Request $Req, Response $Res)
    {

        $countRecipients = 0;
        $countSentSuccessful = 0;
        $countSentFailed = 0;
        $countUnableToSend = 0;

        $this->log("==== START =============");

        $queryRecipients = "SELECT * FROM vw_gem_enrollment WHERE sentRegistration = 0";
        $recipients = $this->pdoQuery($queryRecipients)->fetchAll(Repository::FETCH_ARRAY);
        $countRecipients = count($recipients);

        if ( !$recipients ) {
            $this->log("Nessun invio previsto per oggi.");
            $this->log("==== END =============");
            return;
        }

        foreach ($recipients as $recipient) {

            $this->log("Previsto invio per: {$recipient["nome"]} {$recipient["cognome"]} ({$recipient["agenzia_id"]})");

            if ( ! $recipient['email'] ) {
                $countUnableToSend++;
                $this->log("Nessuna email associata all'utente.");
                $this->log("-----------------------------------");
                continue;
            }

            if (! $stageData = $this->pdoQuery("SELECT * FROM vw_gem_stage WHERE id = {$recipient["stage_id"]}")->fetch(Repository::FETCH_ARRAY) ) {
                $countUnableToSend++;
                $this->log("Luogo non trovato.");
                $this->log("-----------------------------------");
                continue;
            }

            $stageData['address'] = json_decode($stageData['data'])->address;

            if ( $this->sendEmail($recipient, $stageData, "registration") ) {
                $countSentSuccessful++;
                $this->pdoQuery("UPDATE gem_enrollment SET sentRegistration = 1 WHERE id = {$recipient["id"]}");
                $this->log("Email inviata.");
                $this->log("-----------------------------------");
                continue;
            }

            $this->log("Invio fallito.");
            $this->log("-----------------------------------");
            $countSentFailed++;

        }

        $this->log("SOMMARIO:");
        $this->log("Destinatari totali: $countRecipients");
        $this->log("Invii riusciti: $countSentSuccessful");
        $this->log("Invii falliti: $countSentFailed");
        $this->log("Invii impossibili: $countUnableToSend");
        $this->log("==== END =============");

    }

    /**
     * Get pending recipients and send email
     *
     * @batch(description="Gestore eventi: ivia email di promemoria a chi di dovere")
     * @throws \Exception
     */
    function sendReminderNotificationAction(Request $Req, Response $Res)
    {

        $countRecipients = 0;
        $countSentSuccessful = 0;
        $countSentFailed = 0;
        $countUnableToSend = 0;

        $this->log("==== START =============");

        $queryStages = "SELECT * FROM vw_gem_stage WHERE day > CURDATE()";
        $upcomingStages = $this->pdoQuery($queryStages)->fetchAll(Repository::FETCH_ARRAY);
        $stages = $this->findRemindableStages($upcomingStages);

        if ( !$stages ) {
            $this->log("Nessun invio previsto per oggi.");
            $this->log("==== END =============");
            return;
        }

        foreach ($stages as $stage) {

            $this->log("Previsto invio per evento: {$stage["name"]} - {$stage["city"]} del " . date("d-m-Y", strtotime($stage["day"])));

            $recipients = $this->pdoQuery("SELECT * FROM vw_gem_enrollment WHERE stage_id = {$stage["id"]} AND sentReminder = 0")->fetchAll(Repository::FETCH_ARRAY);
            $stage['address'] = json_decode($stage['data'])->address;

            if ( !$recipients ) {
                $this->log("Tutti gli iscritti a questo evento hanno già ricevuto promemoria.");
                $this->log("-----------------------------------");
                continue;
            }

            foreach ($recipients as $recipient) {

                $countRecipients++;

                $this->log("Previsto invio per: {$recipient["nome"]} {$recipient["cognome"]} ({$recipient["agenzia_id"]})");

                if ( ! $recipient['email'] ) {
                    $countUnableToSend++;
                    $this->log("Nessuna email associata all'utente.");
                    $this->log("-----------------------------------");
                    continue;
                }

                if ( $this->sendEmail($recipient, $stage, "reminder") ) {
                    $countSentSuccessful++;
                    $this->pdoQuery("UPDATE gem_enrollment SET sentReminder = 1 WHERE id = {$recipient["id"]}");
                    $this->log("Email inviata.");
                    $this->log("-----------------------------------");
                    continue;
                }

                $countSentFailed++;

            }

            $this->log("-----------------------------------");

        }

        $this->log("SOMMARIO:");
        $this->log("Destinatari totali: $countRecipients");
        $this->log("Invii riusciti: $countSentSuccessful");
        $this->log("Invii falliti: $countSentFailed");
        $this->log("Invii impossibili: $countUnableToSend");
        $this->log("==== END =============");

    }

    protected function sendEmail($recipientData, $stageData, $type)
    {

        $mail = $this->mailer->newMessage();

        $data = [ $recipientData["nome"], $recipientData["cognome"], date("d/m/Y", strtotime($stageData["day"])), $stageData["name"], $stageData["address"]];

        $compiledBody = $this->compileBody($type, $data);

        $address = $this->isMailTest() ? "<EMAIL>" : $recipientData['email'];

        $mail->setSubject($this->mailData[$type]["title"])
            ->setTo($address)
            ->setFrom(['<EMAIL>' => 'ROBOT PortaleAgendo'])
            ->setReplyTo('<EMAIL>')
            ->setReturnPath('<EMAIL>')
            ->setContentType('text/html')
            ->setBody($compiledBody);

        return $this->mailer->send($mail);

    }


    protected function compileBody($type, $data)
    {
        $body = file_get_contents($this->mailData[$type]["body"]);

        return str_replace(["[NOME]", "[COGNOME]", "[DATA]", "[LUOGO]", "[INDIRIZZO]"], $data, $body);

    }

    /**
     * Check if email inside will be sent from testing environment
     *
     * @return bool
     */
    public function isMailTest()
    {
        return \metadigit\core\ENVIRONMENT != 'PROD';
    }

    protected function findRemindableStages($stages)
    {
        $filteredArray = [];
        foreach ($stages as $stage) {

            $datetime = new \DateTime();
            $date = $datetime::createFromFormat('Y-m-d', $stage["day"]);
            $adjustedDate = $date->sub(new \DateInterval('P3D'));

            /* Se il giorno per l'invio del memo cade di sabato o domenica, anticipo al venerdì */
            if ( $adjustedDate->format("D" ) === 'Sat' ) {
                $adjustedDate = $date->sub(new \DateInterval('P1D'));
            }
            if ( $adjustedDate->format("D" ) === 'Sun' ) {
                $adjustedDate = $date->sub(new \DateInterval('P2D'));
            }

            if ( date('Y-m-d') >= $adjustedDate->format("Y-m-d" ) ) {
                $filteredArray[] = $stage;
            }

        }

        return $filteredArray;

    }

}
