<?php namespace batch;

use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Stats
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @batch(description="Statistiche: trasferimento dati dalla tabella provvisoria a quella definitiva")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    function transferTempStatsAction(Request $Req, Response $Res)
    {
        if (! $stats = $this->pdoQuery("SELECT * FROM stats_users_actions_temp")->fetchAll() ) {
            $this->log("Tabella stats vuota..");
            return;
        }

        $statsCounter = count($stats);

        $this->log("Trovate $statsCounter righe.");
        $this->log("\n");

        $this->pdoBeginTransaction();

        foreach($stats as $stat) {
            $this->log("Processo riga: {$stat['date']}, {$stat['user_id']}, {$stat['l1']}, {$stat['l2']}, {$stat['l3']}, {$stat['n']}");
            $statement = $this->pdoPrepare("INSERT INTO stats_users_actions (date, user_id, l1, l2, l3, n) VALUES(:date, :user_id, :l1, :l2, :l3, :n) ON DUPLICATE KEY UPDATE n = n + 1");
            $statement->execute([
                'date' => $stat['date'],
                'user_id' => $stat['user_id'],
                'l1' => $stat['l1'],
                'l2' => $stat['l2'],
                'l3' => $stat['l3'],
                'n' => $stat['n'],
            ]);
            $this->log("=============================================================================================================");
            $this->log("\n");
        }

        $this->log("Ripulisco tabella temporanea.");
        $this->log("\n");
        $this->pdoQuery("TRUNCATE stats_users_actions_temp")->execute();

        $this->pdoCommit();
        $this->log("Operazione conclusa.");
    }

}
