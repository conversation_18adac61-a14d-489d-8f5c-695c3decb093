<?php namespace batch;

use data\UsersRepository;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\console\controller\ActionController;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchInterface;
use metadigit\lib\batch\BatchTrait;

class Webinar extends ActionController implements BatchInterface {

    use PdoTrait, BatchTrait;

    /**
     * @batch(description="Abi: fetch webinar")
     *
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     *
     * @return null
     */
    public function fetchAction(Request $Req, Response $Res) {
        $response = $this->request(
            'https://groupama.forassurance.it/esystem/asp/fun/elencowebinar.asp',
            'zakkwylde',
            date('Ymd'),
            15
        );

        if (! $response['success']) {
            $this->log("Request error for " . $response['querystring']);
            return null;
        }

        //$this->log($response['response']);

        $this->process($response['response']);
    }

    protected function request($endpoint, $accessToken, $start, $length) {
        if (! $accessToken) {
            $this->log('Token is empty');
            return null;
        }

        // Invocazione servizio remoto
        $querystring = $endpoint . "?d0=$start&numgiorni=$length&accessToken=$accessToken";

        $options = array(
            CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4, // force IPv4
            CURLOPT_URL => $querystring,
            CURLOPT_FRESH_CONNECT => 1,
            CURLOPT_TIMEOUT => 15,
            CURLOPT_RETURNTRANSFER => 1
        );

        $ch = curl_init();
        curl_setopt_array($ch, $options);
        $response = curl_exec($ch);
        $status = curl_getinfo($ch);
        curl_close($ch);

        $this->log("Request $querystring");

        // Parse response & build result
        if ($success = $status['http_code'] != 200) {
            $this->log("HTTP error {$status['http_code']}");

            return ['success' => false, 'response' => null];
        }

        return ['response' => $response, 'success' => true];
    }

    protected function process($response) {
        libxml_use_internal_errors(true);

        $xml = simplexml_load_string($response);

        $errors = libxml_get_errors();

        if (! empty($errors)) {
            foreach ($errors as $err) {
                $errorsArray[] = $err->message;
            }
            $this->log('Parse error:' . implode(', ', $errorsArray));

            return null;
        }

        if (! $xml) {
            $this->log("Xml is empty");
            return null;
        }

        $status = array(
            'code' => (string)$xml->response->status,
            'statusMessage' => (string)$xml->response->statusmessage,
        );

        if (! $xml->response->webinars) {
            return [
                'webinars' => null,
                'status' => $status
            ];
        }

        $queryEdition = "INSERT INTO app_webinars_edition (code, webinarCode, title, length, dateStart) VALUES (:code, :webinarCode, :title, :length, :dateStart) ON DUPLICATE KEY UPDATE id = id";
        $queryBooking = "INSERT INTO app_webinars_bookings (edition_id, user_id) VALUES (:edition_id, :user_id) ON DUPLICATE KEY UPDATE id = id";
        $queryUsers = "SELECT id from users_auth where login=:login or login1=:login or login2=:login";

        $statementEdition = $this->pdoPrepare($queryEdition);
        $statementFetch = $this->pdoPrepare("SELECT id FROM app_webinars_edition WHERE code=:code");
        $statementBooking = $this->pdoPrepare($queryBooking);
        $statementUsers = $this->pdoPrepare($queryUsers);

        foreach ($xml->response->webinars->children() as $child) {
            $webinar = [
                'code' => $child['codice'],
                'webinarCode' => $child['codicewebinar'],
                'title' => $child['title'],
                'length' => $child['durata'],
                'dateStart' => $this->parseDate($child['data'], $child['oraini']),
            ];

            $this->log($webinar['dateStart']);

            $statementEdition->execute($webinar);
            if (! $editionId = $this->pdo()->lastInsertId()) {
                $statementFetch->execute(['code' => $webinar['code']]);
                $editionId = $statementFetch->fetchColumn(0);
                $this->log("Edition id for code {$webinar['code']}: $editionId");
            }

            if (! $editionId) {
                $this->log("Error: cannot find edition id for code {$webinar['code']}");
                continue;
            }

            foreach($child->children() as $booking) {
                $statementUsers->execute(['login' => (string)$booking->matricola]);

                if (! $users = $statementUsers->fetchAll()) {
                    $this->log("User " . (string)$booking->matricola . " not found");
                    continue;
                }

                $statementBooking->execute(['edition_id' => $editionId, 'user_id' => $users[0]['id']]);
            }
        }

        return null;
    }

    protected function parseDate($date, $time) {
        return substr($date, 0, 4) . "-" . substr($date, 4, 2) . "-" . substr($date, 6, 2) . " " . $time;
    }
}
