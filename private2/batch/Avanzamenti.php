<?php

namespace batch;

use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Avanzamenti extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    const COLS = 19;
    const COLS2 = 19;

    /** GroupamaPDNotificator
     * @var \service\GroupamaPDNotificator */
    protected $GroupamaPDNotificator;

    /**
     * @batch(description="ObjEco: import avanzamenti")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "STATICO_AVANZAMENTO*.csv");
        $this->log($count = count($files) . " files found.");

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $query = "INSERT INTO pianiagz_agz_avanzamento (
            year,
            month,
            agenzia_id,
            ramiPrefIncassi,
            ramiPrefL1Rappel,
            ramiPrefL2Rappel,
            ramiPrefNuovaProd,
            vitaIncassi,
            vitaL1Rappel,
            vitaL2Rappel,
            vitaNuovaProdAnnui,
            vitaNuovaProdUnici,
            clientiTassoEqPrivati,
            clientiTassoEqAziende,
            vriNpLimit,
            vriNpYear,
            vriNpUnique,
            vriNpTotal,
            vriRappel
        ) values (
            :year,
            :month,
            :agenzia_id,
            :ramiPrefIncassi,
            :ramiPrefL1Rappel,
            :ramiPrefL2Rappel,
            :ramiPrefNuovaProd,
            :vitaIncassi,
            :vitaL1Rappel,
            :vitaL2Rappel,
            :vitaNuovaProdAnnui,
            :vitaNuovaProdUnici,
            :clientiTassoEqPrivati,
            :clientiTassoEqAziende,
            :vriNpLimit,
            :vriNpYear,
            :vriNpUnique,
            :vriNpTotal,
            :vriRappel
        ) ON DUPLICATE KEY UPDATE
            ramiPrefIncassi = :ramiPrefIncassi,
            ramiPrefL1Rappel = :ramiPrefL1Rappel,
            ramiPrefL2Rappel = :ramiPrefL2Rappel,
            ramiPrefNuovaProd = :ramiPrefNuovaProd,
            vitaIncassi = :vitaIncassi,
            vitaL1Rappel = :vitaL1Rappel,
            vitaL2Rappel = :vitaL2Rappel,
            vitaNuovaProdAnnui = :vitaNuovaProdAnnui,
            vitaNuovaProdUnici = :vitaNuovaProdUnici,
            clientiTassoEqPrivati = :clientiTassoEqPrivati,
            clientiTassoEqAziende = :clientiTassoEqAziende,
            vriNpLimit = :vriNpLimit,
            vriNpYear = :vriNpYear,
            vriNpUnique = :vriNpUnique,
            vriNpTotal = :vriNpTotal,
            vriRappel = :vriRappel
        ";

        $statement = $this->pdoPrepare($query);

        foreach ($files as $file) {
            $this->log("Processing $file");

            $data = file($file);
            foreach ($data as $row) {
                $rowInfo = $this->parseRow(str_getcsv($row, ";"));

                try {
                    $statement->execute($rowInfo);
                    $this->GroupamaPDNotificator->avanzamentiUpdate(
                        null,
                        $rowInfo['agenzia_id'],
                        $rowInfo
                    );
                }

                catch (\PDOException $ex) {
                    if ($ex->getCode() == 23000) {
                        $this->log("Not found {$rowInfo['agenzia_id']}");
                    } else {
                        $this->log(print_r($rowInfo, true));
                        $this->log($ex->getMessage());
                    }
                }

                catch (\Exception $ex) {
                    $this->log("Skip agenzia {$rowInfo['agenzia_id']}");
                    $this->log(print_r($rowInfo, true));
                    $this->log($ex->getMessage());
                }
            }

            //$this->backup($file);
        }
    }

    protected function backup($filepath) {
        return unlink($filepath);

        // Maybe some day.
        /*
        $pdaBackupDirectory = \org\metadigit\BACKUP_DIR . 'CSV/Avanzamenti';

        if (! file_exists($pdaBackupDirectory)) {
            if (! mkdir($pdaBackupDirectory)) {
                $this->log("Can't create $pdaBackupDirectory, archive aborted.");
                return;
            }
        }

        $archiveDirectory = $pdaBackupDirectory . '/' . date('Ymd');
        if (! file_exists($archiveDirectory)) {
            if (! mkdir($archiveDirectory)) {
                $this->log("Can't create $archiveDirectory, archive aborted.");
                return;
            }
        }

        $filename = basename($filepath);
        if (copy($filepath, $archiveDirectory . '/' . date('Ymd-His') . "-$filename")) {
            unlink($filepath);
        } else {
            $this->log("Can't copy $filename, archive aborted.");
        }
        */
    }

    /**
     * @param $row
     * @return array
     * @throws \Exception
     */
    protected function parseRow($row) {
        if ($count = count($row) != self::COLS) {
            throw new \Exception("Found $count columns");
        }

        return [
            'year' => $row[0],
            'month' => $row[1],
            'agenzia_id' => $row[2],
            'ramiPrefIncassi' => $row[3],
            'ramiPrefL1Rappel' => $row[4],
            'ramiPrefL2Rappel' => $row[5],
            'ramiPrefNuovaProd' => $row[6],
            'vitaIncassi' => $row[7],
            'vitaL1Rappel' => $row[8],
            'vitaL2Rappel' => $row[9],
            'vitaNuovaProdAnnui' => $row[10],
            'vitaNuovaProdUnici' => $row[11],
            'clientiTassoEqPrivati' => $row[12],
            'clientiTassoEqAziende' => $row[13],
            // VRI
            'vriNpLimit' => $row[14],
            'vriNpYear' => $row[15],
            'vriNpUnique' => $row[16],
            'vriNpTotal' => $row[17],
            'vriRappel' => $row[18],
        ];
    }


    /**
     * @batch(description="ObjEco2: import polizze avanzamenti")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importPolicyDetailAction(Request $Req, Response $Res)
    {
        $files = glob(\metadigit\core\UPLOAD_DIR . "STATICO_POLIZZE_AVANZAMENTO*.csv");
        $this->log($count = count($files) . " files found.");

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        // Truncate table
        try {
            $deleteQuery = "TRUNCATE objeco_policy_avanzamento";
            $deleteStmt = $this->pdoPrepare($deleteQuery);
            $deleteStmt->execute();
        } catch (\PDOException $ex) {
            $this->log("Truncate exception");
            $this->log($ex->getMessage());
            exit;
        }

        // Insert new data
        $query = "INSERT INTO objeco_policy_avanzamento (
            year,
            agenzia_id,
            areaIncentivazione,
            policyNumber,
            ramo,
            tar,
            dtemis,
            dtdeco,
            dtgc,
            premperfez,
            versagg,
            npComp,
            descrizione,
            famiglia,
            grp,
            famigliaDescr,
            aliqObj,
            flgEsito,
            flg
        ) values (
            :year,
            :agency_id,
            :area,
            :policy_number,
            :ramo,
            :tar,
            :dtemis,
            :dtdeco,
            :dtgc,
            :premperfez,
            :versagg,
            :np_comp,
            :descrizione,
            :famiglia,
            :grp,
            :famiglia_descr,
            :aliq_obj,
            :flg_esito,
            :flg
        )";

        $statement = $this->pdoPrepare($query);

        foreach ($files as $file) {
            $this->log("Processing $file");

            $data = file($file);
            foreach ($data as $row) {

                //$this->log(print_r($row, true));

                try {
                    $rowInfo = $this->parseRowPolicyDetail(str_getcsv($row, ";"));

                    $statement->execute($rowInfo);
                }

                catch (\PDOException $ex) {
                    if ($ex->getCode() == 23000) {
                        $this->log("Not found {$rowInfo[0]}");
                    } else {
                        $this->log(print_r($rowInfo, true));
                        $this->log($ex->getMessage());
                    }
                }

                catch (\Exception $ex) {
                    //$this->log("Skip agenzia {$rowInfo[0]}");
                    //$this->log(print_r($rowInfo, true));
                    $this->log(print_r($row, true));
                    $this->log($ex->getMessage());
                }
            }
        }
    }

    /**
     * @param $row
     * @return array
     * @throws \Exception
     */
    protected function parseRowPolicyDetail($row)
    {
        // Check columns number
        if ($count = count($row) != self::COLS2) {
            throw new \Exception("Found $count columns");
        }

        // Check agency_code format
        if (strlen($row[0]) != 4) {
            throw new \Exception("Agency code format is not valid (".strlen($row[0])."): $row[0]");
        }

        // Check area
        if (! empty($row[1]) && ! in_array($row[1], [ 'TCM', 'RIP' ])) {
            throw new \Exception("Area is not valid: $row[2]");
        }

        // Check policy number
//        if (empty($row[4])) {
//            throw new \Exception("Policy number can't be empty, agency code $row[0]");
//        }

        // Check dates
        try {
            $dtemis = \DateTime::createFromFormat('d/m/Y', $row[6]);
        } catch (\Exception $ex) {
            $this->log("Date invalid {$row[6]}");
            $dtemis = null;
        }

        try {
            $dtdeco = \DateTime::createFromFormat('d/m/Y', $row[7]);
        } catch (\Exception $ex) {
            $this->log("Date invalid {$row[7]}");
            $dtdeco = null;
        }

        try {
            $dtgc = \DateTime::createFromFormat('d/m/Y', $row[8]);
        } catch (\Exception $ex) {
            $this->log("Date invalid {$row[8]}");
            $dtgc = null;
        }

        return [
            'agency_id' => $row[0],
            'area' => $row[1],
            'year' => $row[2],
            'ramo' => $row[3],
            'policy_number' => $row[4],
            'tar' => $row[5],
            'dtemis' => ($dtemis) ? $dtemis->format('Y-m-d') : null,
            'dtdeco' => ($dtdeco) ? $dtdeco->format('Y-m-d') : null,
            'dtgc' => ($dtgc) ? $dtgc->format('Y-m-d') : null,
            'premperfez' => $row[9],
            'versagg' => $row[10],
            'np_comp' => $row[11],
            'descrizione' => $row[12],
            'famiglia' => $row[13],
            'grp' => $row[14],
            'famiglia_descr' => $row[15],
            'aliq_obj' => $row[16],
            'flg_esito' => $row[17],
            'flg' => $row[18]
        ];
    }
}
