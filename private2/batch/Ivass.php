<?php namespace batch;

use data\apps\ivass\Repository\Table5Repository;
use data\apps\ivass\Repository\Table7Repository;
use data\UsersRepository;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Ivass extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @var UsersRepository
     */
    protected $users;

    /**
     * @var Table5Repository
     */
    protected $table5;

    /**
     * @var Table7Repository
     */
    protected $table7;

    /**
     * @batch(description="Ivass: aggiorna conteggio agenti")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     */
    public function updateAgentsAction(Request $Req, Response $Res) {
        $this->log("Batch disabled due IVASS 2021");
        return;

        $year = date('Y');

        try {
            $count5 = $this
                ->pdo()
                ->query("
                SELECT id from users where type='AGENTE' and active = 1 and agenzia_id in(
                SELECT agenzia_id FROM users where type='INTERMEDIARIO' AND ruolo='NEO' group by agenzia_id)")
                ->rowCount();

            $count7 = $this->users->count("type,EQ,AGENTE|active,EQ,1");

            if (! $this->table5->updateAgentsCount($year, $count5)) {
                $this->log("Cannot update table 5.");
            }

            if (! $this->table7->updateAgentsCount($year, $count7)) {
                $this->log("Cannot update table 7.");
            }
        }

        catch (\Exception $ex) {
            $this->log($ex->getMessage());
        }
    }
}
