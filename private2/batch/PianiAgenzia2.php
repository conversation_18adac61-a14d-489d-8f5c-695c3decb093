<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\core\util\csv\CsvWriter,
	metadigit\lib\util\csv\CsvProcessor;

class PianiAgenzia2 extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @batch(description="Percorsi Crescita: aggiornamento nuove agenzie")
     *
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     *
     * @return null
     */
    public function updateAction(Request $Req, Response $Res) {
        $this->pdoExec("
        INSERT INTO pianiag2_piani(agenzia_id, anno)
        SELECT a.id, YEAR(NOW())
        FROM agenzie a LEFT JOIN pianiag2_piani p ON p.agenzia_id = a.id AND p.anno = YEAR(NOW())
        WHERE p.id IS NULL");
    }
}
