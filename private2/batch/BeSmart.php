<?php namespace batch;

use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class BeSmart extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    /**
     * @var \api\apps\besmart\BeSmart
     */
    protected $besmart;

    protected $date;

    /**
     * @batch(description="BeSmart: import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile($this->besmart->getPath())) {
            $this->log("File not found");
            return;
        }

        if (! $id = $this->besmart->parseId($file)) {
            $this->log("ID not found");
            return;
        }

        $this->log("Processing $file");

        $this->pdoBeginTransaction();

//$this->pdoQuery("delete from besmart_docs where iniziativa_id=$id");

        $statement = $this->pdoPrepare($this->besmart->getDataStatement());

        $this->date = $this->besmart->parseDate($file);

        $this->insertData($statement, file($file), $id);

        // @TODO: post-insert hook
        $this->pdoQuery("update besmart set lastUpdate='" . date('Y-m-d H:i:s') . "',lastUpdateLabel={$this->date} where id = $id");

        $this->pdoCommit();

        //$this->backupFile($file);
    }

    protected function insertData($statement, $data, $id, $separator = ";")
    {
        $statusStatement = $this->pdoPrepare("update besmart_agenzie
            set bonus=:bonus, bonusFactor=:bonusFactor, paid=:paid
            where iniziativa_id = :iniziativa_id
            and agenzia_id = :agenzia_id");

        foreach ($data as $row) {
            $rowInfo = $this->besmart->parseDataRow(str_getcsv($row, $separator), $id);

            $rowInfo['date'] = $this->date;

            try {
                $statement->execute($rowInfo);

                $statusStatement->execute($this->besmart->getStatus($rowInfo));
            }

            catch (\PDOException $ex) {
                if ($ex->getCode() == 23000) {
                    $this->log("Not found {$rowInfo['agenzia_id']}");
                }

                $this->log(print_r($rowInfo, true));
                $this->log($ex->getMessage());
            }

            catch (\Exception $ex) {
                $this->log("Skip agenzia {$rowInfo['agenzia_id']}");
                $this->log(print_r($rowInfo, true));
                $this->log($ex->getMessage());
            }
        }
    }

    /**
     * @batch(description="BeSmart: static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile($this->besmart->getPath(true))) {
            $this->log("File not found");
            return;
        }

        if (! $id = $this->besmart->parseId($file)) {
            $this->log("ID not found");
            return;
        }

        $this->log("Processing $file");

        $this->pdoBeginTransaction();

        $statement = $this->pdoPrepare("
        insert into besmart_agenzie
        (iniziativa_id,agenzia_id,paid,prevYear,active)
        values(:iniziativa_id,:agenzia_id,:paid,:prevYear,:active)
        on duplicate key update
        paid=:paid,
        prevYear=:prevYear,
        active=:active
        ");

        $this->date = $this->besmart->parseDate($file);

        $this->insertStatic($statement, file($file), $id, ",");

        $this->pdoCommit();

        //$this->backupFile($file);
    }

    protected function backupFile($file)
    {
        $destination = \metadigit\core\BACKUP_DIR . "CSV/BeSmart/" . basename($file);

        if (! rename($file, $destination)) {
            $this->log("WARNING: cannot backup $file");

            return false;
        }

        $this->log("File has been backed up.");

        return true;
    }

    protected function insertStatic($statement, $data, $id, $separator = ";")
    {
        foreach ($data as $row) {
            try {
                $rowInfo = $this->besmart->parseStaticRow(str_getcsv($row, $separator), $id);

                $statement->execute($rowInfo);
            }

            catch (\PDOException $ex) {
                if ($ex->getCode() == 23000) {
                    $this->log("Not found {$rowInfo['agenzia_id']}");
                    $this->log($ex->getMessage());
                } else {
                    $this->log(print_r($rowInfo, true));
                    $this->log($ex->getMessage());
                }
            }

            catch (\Exception $ex) {
                $this->log("Skip agenzia {$rowInfo['agenzia_id']}");
                $this->log(print_r($rowInfo, true));
                $this->log($ex->getMessage());
            }
        }
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        if (! ($count = count($files))) {
            $this->log("File $filepath not found.");
            return null;
        }

        if ($count != 1) {
            $this->log("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    protected function float($string)
    {
        return floatval(preg_replace("/,/", ".", $string));
    }

    protected function bonus($row)
    {
        // @WARNING precision loss at 3rd decimal position.
        $bonus = $this->float($row[24]) * 0.1;

        if (in_array($row[26], ["001863", "001867"])) {
            return $bonus;
        }

        if (in_array($row[17], ['CA18', 'DA18', '001021', '169'])) {
            return $bonus;
        }

        return 0;
    }

    protected function getCategory($row)
    {
        if (in_array($row[17], ['001021', '169'])) {
            return "MYPROTECTION";
        }

        if (in_array($row[17], ['CA18', 'DA18'])) {
            return "TCM";
        }

        return "DANNI";
    }
}
