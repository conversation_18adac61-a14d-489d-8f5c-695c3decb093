<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\core\util\csv\CsvWriter,
	metadigit\lib\util\csv\CsvProcessor;

class WelcomeBack extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const CURRENT_YEAR = 2015;
	const FILE = 'WELCOME_BACK_2015.csv';
	const ANAGRAFICA_GOLD = 'WELCOME_BACK_GOLD_ANAGRAFICA.csv';
	const DAY_S1 = 15;
	const DAY_S2 = 25;
	const DAY_S3 = 15;
	const DAY_END = 1;

	/**
	 * @batch(description="WelcomeBack: import CSV file")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function importcsvAction(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		/*
		$sql = 'INSERT INTO welcomeback_clienti ( year, id, agenzia_id, intermediario, dataUscita, nominativo, dataNascita, indirizzo, cap, localita, comune, provincia, codiceFisc, eta, segmento, numPolizze, anzianita, punteggio, classe, premioRCA, premioARD, targa )'.
				'VALUES ( :year, :id, :agenzia_id, :intermediario, :dataUscita, :nominativo, :dataNascita, :indirizzo, :cap, :localita, :comune, :provincia, :codiceFisc, :eta, :segmento, :numPolizze, :anzianita, :punteggio, :classe, :premioRCA, :premioARD, :targa )'.
				'ON DUPLICATE KEY UPDATE agenzia_id = :agenzia_id, intermediario = :intermediario, dataUscita = :dataUscita, nominativo = :nominativo, dataNascita = :dataNascita, indirizzo = :indirizzo, cap = :cap, localita = :localita, comune = :comune, provincia = :provincia, codiceFisc = :codiceFisc, eta = :eta, segmento = :segmento, numPolizze = :numPolizze, anzianita = :anzianita, punteggio = :punteggio, classe = :classe, premioRCA = :premioRCA, premioARD = :premioARD ';
		*/
		$sql = 'INSERT IGNORE INTO welcomeback_clienti ( year, id, agenzia_id, intermediario, dataUscita, nominativo, dataNascita, indirizzo, cap, localita, comune, provincia, codiceFisc, eta, segmento, numPolizze, anzianita, punteggio, classe, premioRCA, premioARD, targa )'.
				'VALUES ( :year, :id, :agenzia_id, :intermediario, :dataUscita, :nominativo, :dataNascita, :indirizzo, :cap, :localita, :comune, :provincia, :codiceFisc, :eta, :segmento, :numPolizze, :anzianita, :punteggio, :classe, :premioRCA, :premioARD, :targa )';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$id = $values[0];
			$nominativo = $values[1];
			$agenziaID = (($values[2][0] == 'N') ? 'N' : 'G').substr($values[2], 3, 3);
			$intermediario = $values[3];
			$dataUscita = $values[4];
			$dataNascita = $values[5];
			$indirizzo = $values[6];
			$cap = $values[7];
			$localita = $values[8];
			$comune = $values[9];
			$provincia = $values[10];
			$codiceFisc  = $values[11];
			$eta = $values[12];
			$segmento = $values[13];
			$numPolizze = $values[14];
			$anzianita = $values[15];
			$punteggio = $values[16];
			$classe = $values[17];
			$premioRCA = $values[18];
			$premioARD = $values[19];
			$targa = $values[20];
			$pdoSt->execute([
				'year'=>self::CURRENT_YEAR,
				'id'=>$id,
				'agenzia_id'=>$agenziaID,
				'intermediario'=>$intermediario,
				'dataUscita'=>$dataUscita,
				'nominativo'=>$nominativo,
				'dataNascita'=>$dataNascita,
				'indirizzo'=>$indirizzo,
				'cap'=>$cap,
				'localita'=>$localita,
				'comune'=>$comune,
				'provincia'=>$provincia,
				'codiceFisc'=>$codiceFisc,
				'eta'=>$eta,
				'segmento'=>$segmento,
				'numPolizze'=>$numPolizze,
				'anzianita'=>$anzianita,
				'punteggio'=>$punteggio,
				'classe'=>$classe,
				'premioRCA'=>$premioRCA,
				'premioARD'=>$premioARD,
				'targa'=>$targa
			]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE, 21, $processFn);
	}

	/**
	 * @batch(description="WelcomeBack Gold: import CSV file")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function WBGoldimportcsvAction(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		$sql = 'INSERT INTO welcomeback_gold ( agenzia_id, nome, ragSociale, indirizzo, cap, localita, provincia, telefono, telefono2, fax, email )'.
				'VALUES ( :agenzia_id, :nome, :ragSociale, :indirizzo, :cap, :localita, :provincia, :telefono, :telefono2, :fax, :email )'.
				'ON DUPLICATE KEY UPDATE nome = :nome2, ragSociale = :ragSociale2, indirizzo = :indirizzo2, cap = :cap2, localita = :localita2, provincia = :provincia2, telefono = :telefono_2, telefono2 = :telefono2_2, fax = :fax2, email = :email2 ';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = (($values[0][0] == 'N') ? 'N' : 'G').substr($values[0], 3, 3);
			$nome = $values[1];
			$ragSociale = $values[2];
			$indirizzo = $values[3];
			$cap = $values[4];
			$provincia = $values[5];
			$localita = $values[6];
			$telefono  = $values[7];
			$telefono2 = $values[8];
			$fax = $values[9];
			$email = $values[10];
			$pdoSt->execute([
				'agenzia_id'=>$agenziaID,
				'nome'=>$nome, 'nome2'=>$nome,
				'ragSociale'=>$ragSociale, 'ragSociale2'=>$ragSociale,
				'indirizzo'=>$indirizzo, 'indirizzo2'=>$indirizzo,
				'cap'=>$cap, 'cap2'=>$cap,
				'localita'=>$localita, 'localita2'=>$localita,
				'provincia'=>$provincia, 'provincia2'=>$provincia,
				'telefono'=>$telefono, 'telefono_2'=>$telefono,
				'telefono2'=>$telefono2, 'telefono2_2'=>$telefono2,
				'fax'=>$fax, 'fax2'=>$fax,
				'email'=>$email, 'email2'=>$email
			]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::ANAGRAFICA_GOLD, 11, $processFn);
	}

	/**
	 * @batch(description="WelcomeBack Gold: export CSV summary")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function WBGoldSummaryAction(Request $Req, Response $Res) {
		$sql = 'SELECT COUNT(*) FROM welcomeback_clienti WHERE agenzia_id = :agenzia_id AND YEAR(dataScadenza) = :year AND MONTH(dataScadenza) = :month';
		$pdoSt = $this->pdoPrepare($sql);

		$sql = 'SELECT agenzia_id FROM welcomeback_gold';
		$agenzieArray = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_COLUMN);
		$data = [];
		$i = 0;
		foreach($agenzieArray as $agenziaID) {
			$data[$i][0] = $agenziaID;
			for($m=1; $m<=12; $m++) {
				$pdoSt->execute(['agenzia_id'=>$agenziaID, 'year'=>self::CURRENT_YEAR, 'month'=>$m]);
				$count = $pdoSt->fetch(\PDO::FETCH_COLUMN);
				$data[$i][$m] = $count;
			}
			$i++;
		}

		$CsvWriter = new CsvWriter();
		$CsvWriter->setData($data)
				->addColumn('Agenzia', 0)
				->addColumn('Gen', 1)
				->addColumn('Feb', 2)
				->addColumn('Mar', 3)
				->addColumn('Apr', 4)
				->addColumn('Mag', 5)
				->addColumn('Giu', 6)
				->addColumn('Lug', 7)
				->addColumn('Ago', 8)
				->addColumn('Set', 9)
				->addColumn('Ott', 10)
				->addColumn('Nov', 11)
				->addColumn('Dic', 12);
		$CsvWriter->write(UPLOAD_DIR.'WELCOME_BACK_GOLD_'.self::CURRENT_YEAR.'-SUMMARY.csv');
	}

	/**
	 * @batch(description="WelcomeBack Gold: export mailing CSV")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function WBGoldMailingAction(Request $Req, Response $Res) {
		$sql = 'SELECT ag.agenzia_id, ag.nome, ag.ragSociale, ag.indirizzo, ag.cap, ag.localita, ag.provincia, ag.telefono, ag.telefono2, ag.fax, ag.email, cl.id, cl.nominativo, cl.indirizzo, cl.cap, cl.localita, cl.comune, cl.provincia FROM welcomeback_clienti cl LEFT JOIN welcomeback_gold ag ON(cl.agenzia_id = ag.agenzia_id) WHERE cl.agenzia_id IN (SELECT agenzia_id FROM welcomeback_gold) AND YEAR(cl.dataScadenza) = :year AND MONTH(cl.dataScadenza) = :month';
		$pdoSt = $this->pdoPrepare($sql);

		$CsvWriter = (new CsvWriter())
				->addColumn('Codice', 0)
				->addColumn('Agenzia', 1)
				->addColumn('AgzRagSoc', 2)
				->addColumn('AgzIndirizzo', 3)
				->addColumn('AgzCAP', 4)
				->addColumn('AgzLocalita', 5)
				->addColumn('AgzProvincia', 6)
				->addColumn('AgzTelefono', 7)
				->addColumn('AgzTelefono2', 8)
				->addColumn('AgzFax', 9)
				->addColumn('AgzEmail', 10)
				->addColumn('Cliente', 11)
				->addColumn('ClNome', 12)
				->addColumn('ClIndirizzo', 13)
				->addColumn('ClCAP', 14)
				->addColumn('ClLocalita', 15)
				->addColumn('ClComune', 16)
				->addColumn('ClProvincia', 17);

		for($m=1; $m<=12; $m++) {
			$pdoSt->execute(['year'=>self::CURRENT_YEAR, 'month'=>$m]);
			$data = $pdoSt->fetchAll(\PDO::FETCH_NUM);
			$CsvWriter->setData($data)->write(UPLOAD_DIR.'WELCOME_BACK_GOLD_'.self::CURRENT_YEAR.'-'.$m.'.csv');
		}
	}

	/**
	 * @batch(description="WelcomeBack: stats")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function statsAction(Request $Req, Response $Res) {
		$sql = 'CALL welcomeback_stats(:year, :month)';
		$pdoSt = $this->pdoPrepare($sql);

		for($m=1; $m<=12; $m++) {
			$pdoSt->execute(['year'=>self::CURRENT_YEAR, 'month'=>$m]);
		}
	}

	/**
	 * @batch(description="WelcomeBack: S1 start")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @throws \Exception
	 */
	function step1Action(Request $Req, Response $Res) {
		if(date('d') != self::DAY_S1) throw new \Exception('This batch must be launched ONLY the '.self::DAY_S1.' of each month!');
		$month = date('m', strtotime('first day of +2 month'));
		$this->pdoExec(sprintf('CALL welcomeback_step1(%d, %d)', self::CURRENT_YEAR, $month));
	}

	/**
	 * @batch(description="WelcomeBack: S2 start")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @throws \Exception
	 */
	function step2Action(Request $Req, Response $Res) {
		if(date('d') != self::DAY_S2) throw new \Exception('This batch must be launched ONLY the '.self::DAY_S2.' of each month!');
		$year = date('Y', strtotime('first day of +2 month'));
		$month = date('m', strtotime('first day of +2 month'));

		$this->pdoExec(sprintf('CALL welcomeback_step2(%d, %d)', self::CURRENT_YEAR, $month));

		$CsvWriter = (new CsvWriter())->setDelimiter(';')->setEnclosure('')
				->addColumn('Cod.Cliente', 0)
				->addColumn('Agenzia', 1)
				->addColumn('Nominativo', 2)
				->addColumn('Indirizzo', 3)
				->addColumn('CAP', 4)
				->addColumn('Localita', 5)
				->addColumn('Provincia', 6)
				->addColumn('Cod.Fisc.', 7)
				->addColumn('Data Scad.', 8)
				->addColumn('GOLD', 9, function($v) { return (empty($v)) ? '':'GOLD'; })
		;
		$sql = sprintf('SELECT cl.id, cl.agenzia_id, cl.nominativo, cl.indirizzo, cl.cap, cl.localita, cl.provincia, cl.codiceFisc, cl.dataScadenza, g.agenzia_id AS gold  FROM welcomeback_clienti cl LEFT JOIN welcomeback_gold g ON(cl.agenzia_id = g.agenzia_id) WHERE year  = %d AND MONTH(cl.dataScadenza) = %d AND step = 2', self::CURRENT_YEAR, $month);
		$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_NUM);
		$CsvWriter->setData($data)->write(UPLOAD_DIR.'WELCOME_BACK_POSTALIZZAZIONE_'.$year.'_'.$month.'.csv');
	}

	/**
	 * @batch(description="WelcomeBack: S3 start")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @throws \Exception
	 */
	function step3Action(Request $Req, Response $Res) {
		if(date('d') != self::DAY_S3) throw new \Exception('This batch must be launched ONLY the '.self::DAY_S3.' of each month!');
		$month = date('m', strtotime('first day of +1 month'));
		$this->pdoExec(sprintf('CALL welcomeback_step3(%d, %d)', self::CURRENT_YEAR, $month));
	}

	/**
	 * @batch(description="WelcomeBack: S4 end")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @throws \Exception
	 */
	function step4Action(Request $Req, Response $Res) {
		if(date('d') != self::DAY_END) throw new \Exception('This batch must be launched ONLY the '.self::DAY_END.' of each month!');
		$month = date('m', strtotime('first day of -3 month'));
		$this->pdoExec(sprintf('CALL welcomeback_end(%d, %d)', self::CURRENT_YEAR, $month));
	}
}
