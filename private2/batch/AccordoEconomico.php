<?php namespace batch;

use data\apps\accordo2\Models\Accordo;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
    metadigit\core\db\orm\Repository;

class AccordoEconomico extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait, \metadigit\core\CoreTrait;

    protected $COLS = 33;
    protected $COLS_FIX = 32;

    /**
     * @var Repository
     */
    protected $accordi;

    /**
     * @var Repository
     */
    protected $revisioni;

	/**
	 * @batch(description="Accordo Economico: import obiettivi")
	 * @param Request $Req
	 * @param Response $Res
	 * @throws \metadigit\lib\util\csv\Exception
	 */
	function importAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "STATICO_ACCORDOECO_2025*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->import(file($files[0]));
    }

    protected function import($data)
    {
        $count = -1;

        foreach ($data as $row) {
            $count++;

            $row = str_getcsv($row, ";");

            $statementAccordo = $this->statementAccordo();

            $statementRevision = $this->statementRevision();

            if (($cols = count($row)) != $this->COLS) {
                $this->log("Linea $count - colonne attese:{$this->COLS} colonne trovate $cols");
            }

            // Inserimento Accordo
            try {
                $accordo = $this->parseRowAccordo($row);

                // Accordo
                $this->pdoStExecute($statementAccordo, $accordo);

                $id = $this->pdoLastInsertId();

            } catch (\Exception $ex) {
                $this->log("Errore inserimento Accordo. Linea $count " . ((isset($row[2])) ? $row[2] : ""));
                $this->log($ex->getMessage());
                continue;
            }

            // Inserimento Revisione
            try {
                // Revision
                $this->pdoStExecute($statementRevision, $this->parseRowRevision($row, $id));

                $this->insertFascia($accordo['year'], $accordo['fasciaRamiPref'], 'rp');
                $this->insertFascia($accordo['year'], $accordo['fasciaVita'], 'vita');

            } catch (\Exception $ex) {
                $this->log("Errore inserimento Revisione. Linea $count " . ((isset($row[2])) ? $row[2] : ""));
                $this->log($ex->getMessage());
                if ($id) {
                    $this->pdoQuery("delete from pianiagz_obj_accordo where id = $id");
                }
                continue;
            }
        }
    }

    protected function statementRevision()
    {
        return "INSERT INTO pianiagz_obj_accordo_rev
                (
                    accordo_id,
                    user_id,
                    mode,
                    isLatest,
                    status,
                    updatedAt,
                    ramiPrefAnnL1IncassiPrev,
                    ramiPrefAnnL1Obj,
                    ramiPrefAnnL1Incremento,
                    ramiPrefAnnL1IncassiCur,
                    ramiPrefAnnL1Rappel,
                    ramiPrefAnnL2Obj,
                    ramiPrefAnnL2Incremento,
                    ramiPrefAnnL2IncassiCur,
                    ramiPrefAnnL2Rappel,
                    ramiPrefAnnL2ObjPersonal,
                    ramiPrefAnnL2IncrementoPersonal,
                    ramiPrefAnnL2IncassiCurPersonal,
                    ramiPrefAnnL2RappelPersonal,
                    ramiPrefSemIncassiPrev,
                    ramiPrefSemObj,
                    ramiPrefSemIncremento,
                    ramiPrefSemIncassiCur,
                    ramiPrefSemRappel,
                    vitaBaseCalcoloObj,
                    vitaAnnL1Obj,
                    vitaAnnL1ObjPezzi,
                    vitaAnnL1IncassiCur,
                    vitaAnnL1Rappel,
                    vitaAnnL2Obj,
                    vitaAnnL2IncassiCur,
                    vitaAnnL2ExtraRappel,
                    statusChangeMessage
                )
                VALUES
                (
                    :accordo_id,
                    :user_id,
                    :mode,
                    :isLatest,
                    :status,
                    :updatedAt,
                    :ramiPrefAnnL1IncassiPrev,
                    :ramiPrefAnnL1Obj,
                    :ramiPrefAnnL1Incremento,
                    :ramiPrefAnnL1IncassiCur,
                    :ramiPrefAnnL1Rappel,
                    :ramiPrefAnnL2Obj,
                    :ramiPrefAnnL2Incremento,
                    :ramiPrefAnnL2IncassiCur,
                    :ramiPrefAnnL2Rappel,
                    :ramiPrefAnnL2ObjPersonal,
                    :ramiPrefAnnL2IncrementoPersonal,
                    :ramiPrefAnnL2IncassiCurPersonal,
                    :ramiPrefAnnL2RappelPersonal,
                    :ramiPrefSemIncassiPrev,
                    :ramiPrefSemObj,
                    :ramiPrefSemIncremento,
                    :ramiPrefSemIncassiCur,
                    :ramiPrefSemRappel,
                    :vitaBaseCalcoloObj,
                    :vitaAnnL1Obj,
                    :vitaAnnL1ObjPezzi,
                    :vitaAnnL1IncassiCur,
                    :vitaAnnL1Rappel,
                    :vitaAnnL2Obj,
                    :vitaAnnL2IncassiCur,
                    :vitaAnnL2ExtraRappel,
                    :statusChangeMessage
                )
            ";
    }

    protected function statementAccordo()
    {
        return "INSERT INTO pianiagz_obj_accordo
                (
                    year,
                    partecipante,
                    agenzia_id,
                    fasciaRamiPref,
                    fasciaVita,
                    status,
                    type,
                    statusChangeMessage,
                    reminderSent
                )
                VALUES
                (
                    :year,
                    :partecipante,
                    :agenzia_id,
                    :fasciaRamiPref,
                    :fasciaVita,
                    :status,
                    :type,
                    :statusChangeMessage,
                    :reminderSent
                )
            ";
    }

    protected function parseRowAccordo($row)
    {
        return [
            'year' => $row[0],
            'partecipante' => $row[1],
            'agenzia_id' => $row[2],
            'fasciaRamiPref' => trim(strtoupper($row[3])),
            'fasciaVita' => trim($row[4]),
            'status' => $this->parseStatus($row[30]),
            'type' => $this->parseType($row[31]),
            'statusChangeMessage' => $row[32],
            'reminderSent' => 0,
        ];
    }

    protected function parseRowRevision($row, $id)
    {
        $statusMessage = [];
        if ($row[32]) {
            $statusMessage = [
                [
                    "timestamp" => date("d/m/Y H:i"),
                    "author" => "Compagnia",
                    "userType" => "AMMINISTRATORE",
                    "text" => $row[32],
                ]
            ];
        }

        return [
            'accordo_id' => $id,
            'user_id' => null,
            'mode' => 'BATCH',
            'isLatest' => 1,
            'status' => 'DA_LAVORARE',
            'updatedAt' => date('Y-m-d H:i:s'),
            'ramiPrefAnnL1IncassiPrev' => $row[5],
            'ramiPrefAnnL1Obj' => $row[6],
            'ramiPrefAnnL1Incremento' => $row[7],
            'ramiPrefAnnL1IncassiCur' => $row[8],
            'ramiPrefAnnL1Rappel' => $row[9],
            'ramiPrefAnnL2Obj' => $row[10],
            'ramiPrefAnnL2Incremento' => $row[11],
            'ramiPrefAnnL2IncassiCur' => $row[12],
            'ramiPrefAnnL2Rappel' => $row[13],
            'ramiPrefAnnL2ObjPersonal' => $row[14],
            'ramiPrefAnnL2IncrementoPersonal' => $row[15],
            'ramiPrefAnnL2IncassiCurPersonal' => $row[16],
            'ramiPrefAnnL2RappelPersonal' => $row[17],
            'ramiPrefSemIncassiPrev' => $row[18],
            'ramiPrefSemObj' => $row[19],
            'ramiPrefSemIncremento' => $row[20],
            'ramiPrefSemIncassiCur' => $row[21],
            'ramiPrefSemRappel' => $row[22],
            'vitaBaseCalcoloObj' => $row[23],
            'vitaAnnL1ObjPezzi' => $row[24],
            'vitaAnnL1IncassiCur' => $row[25],
            'vitaAnnL1Rappel' => $row[26],
            'vitaAnnL1Obj' => '',
            'vitaAnnL2Obj' => $row[27],
            'vitaAnnL2IncassiCur' => $row[28],
            'vitaAnnL2ExtraRappel' => $row[29],
            'statusChangeMessage' => $row[32] ? json_encode($statusMessage) : '',
        ];
    }

    protected function insertFascia($year, $name, $type)
    {
        $id = $this->pdoStExecute("
            select
            id
            from pianiagz_obj_fascia_{$type}
            where
            name=:name
            and year=:year", [
            'name' => $name,
            'year' => $year,
        ])->fetchColumn(0);

        if ($id) {
            return;
        }

        $query = "insert into pianiagz_obj_rappel_{$type} (
            fascia_id,
            pers,
            obj,
            rappelMax
        )
            values (
            :fascia_id,
            :pers,
            :obj,
            :rappelMax
        )";

        $this->pdoExec("insert into pianiagz_obj_fascia_{$type}
        (year, name, lowerBound, upperBound)
        values ($year, '$name', 0, 0)");

        $id = $this->pdoLastInsertId();

        for ($i = 1; $i <= 4; $i++) {
            $this->pdoStExecute($query, [
                'fascia_id' => $id,
                'pers' => $i,
                'obj' => 0,
                'rappelMax' => 0,
            ]);
        }
    }

    protected function parseStatus($status) {
        $status = trim(strtoupper($status));
        switch($status) {
            case 'DL': return Accordo::$STATUS_DA_LAVORARE;
            case 'IL': return Accordo::$STATUS_IN_LAVORAZIONE;
            case 'DR': return Accordo::$STATUS_DA_RILAVORARE;
            case 'AR': return Accordo::$STATUS_ATTESA_RICALCOLO;
            case 'SO': return Accordo::$STATUS_SOTTOSCRITTO;
            default: return -1;
        }
    }

    protected function parseType($type) {
        $type = trim(strtoupper($type));
        switch($type) {
            case 'ST': return Accordo::$TYPE_STANDARD;
            case 'RP': return Accordo::$TYPE_PERSONALIZZATO_RAMIPREF;
            case 'VT': return Accordo::$TYPE_PERSONALIZZATO_VITA;
            case 'CO': return Accordo::$TYPE_PERSONALIZZATO_COMBO;
            default: return -1;
        }
    }

     // @batch(description="Accordo Economico: fix Obiettivi 2022 con riduzione del 15%")
     // @param Request $Req
     // @param Response $Res
     // @throws \metadigit\lib\util\csv\Exception

    function _fixObiettivi2022Action(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "ACCORDOECO_FIX_OBIETTIVI_2022*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->fixObiettivi2022(file($files[0]));
    }

    protected function fixObiettivi2022($data)
    {
        $count = -1;

        foreach ($data as $row) {
            $count++;

            $row = str_getcsv($row, ";");

            // Check su numero di colonne
            if (($cols = count($row)) != $this->COLS_FIX) {
                TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "Colonne non corrispondenti. Salto riga.");
                $this->log("Linea $count - colonne attese:{$this->COLS_FIX} colonne trovate $cols");
                continue;
            }

            $queryAccordi = "SELECT * FROM pianiagz_obj_accordo where agenzia_id = '$row[2]' and year = 2022";

            // Trova Accordo per Agenzia e anno
            if (! $accordo = $this->pdoQuery($queryAccordi)->fetch() ) {
                $this->log("Accordo non trovato per Agenzia $row[2] su anno 2022.");
                continue;
            }

            $queryRevisioni = "SELECT * FROM pianiagz_obj_accordo_rev where accordo_id = {$accordo['id']} and isLatest = 1";

            // Trova ultima revisione per ID Accordo
            if (! $revisione = $this->pdoQuery($queryRevisioni)->fetch() ) {
                $this->log("Revisione non trovata per Agenzia $row[2].");
                continue;
            }

            //$newRevisione = $revisione;
            $newRevisione = $this->parseRevisionWithAlteredObj($accordo, $revisione, $row);

            // Setto islatest a 0 su vecchia revisione
            try {
                $revisione['isLatest'] = 0;

                $queryUpdateOldRevision = "UPDATE pianiagz_obj_accordo_rev SET isLatest = 0 WHERE id = {$revisione['id']}";
                $this->pdoQuery($queryUpdateOldRevision)->execute();

            } catch (\Exception $ex) {
                $this->log("Errore durante aggiornamento revisione {$revisione['id']}. Linea $count " . ((isset($row[2])) ? $row[2] : ""));
                $this->log($ex->getMessage());
                continue;
            }

            try {
                $sqlStatement = $this->statementRevision();
                $this->pdoStExecute($sqlStatement, $newRevisione);

            } catch (\Exception $ex) {
                $this->log("Errore durante inserimento nuova revisione. Linea $count " . ((isset($row[2])) ? $row[2] : ""));
                $this->log($ex->getMessage());
                continue;
            }

        }
    }

    protected function parseRevisionWithAlteredObj($accordo, $revisione, $row)
    {
        return [
            'accordo_id' => $accordo['id'],
            'user_id' => null,
            'mode' => 'FIX_OBJ_2022',
            'isLatest' => 1,
            'status' => $revisione['status'],
            'updatedAt' => date('Y-m-d H:i:s'),
            'ramiPrefAnnL1IncassiPrev' => $revisione['ramiPrefAnnL1IncassiPrev'],
            'ramiPrefAnnL1Obj' => $row[6],
            'ramiPrefAnnL1Incremento' => $row[7],
            'ramiPrefAnnL1IncassiCur' => $row[8],
            'ramiPrefAnnL1Rappel' => $revisione['ramiPrefAnnL1Rappel'],
            'ramiPrefAnnL2Obj' => $row[10],
            'ramiPrefAnnL2Incremento' => $row[11],
            'ramiPrefAnnL2IncassiCur' => $row[12],
            'ramiPrefAnnL2Rappel' => $revisione['ramiPrefAnnL2Rappel'],
            'ramiPrefAnnL2ObjPersonal' => $row[14],
            'ramiPrefAnnL2IncrementoPersonal' => $row[15],
            'ramiPrefAnnL2IncassiCurPersonal' => $row[16],
            'ramiPrefAnnL2RappelPersonal' => $revisione['ramiPrefAnnL2RappelPersonal'],
            'ramiPrefSemIncassiPrev' => $revisione['ramiPrefSemIncassiPrev'],
            'ramiPrefSemObj' => $revisione['ramiPrefSemObj'],
            'ramiPrefSemIncremento' => $revisione['ramiPrefSemIncremento'],
            'ramiPrefSemIncassiCur' => $revisione['ramiPrefSemIncassiCur'],
            'ramiPrefSemRappel' => $revisione['ramiPrefSemRappel'],
            'vitaBaseCalcoloObj' => $revisione['vitaBaseCalcoloObj'],
            'vitaAnnL1ObjPezzi' => $revisione['vitaAnnL1ObjPezzi'],
            'vitaAnnL1IncassiCur' => $revisione['vitaAnnL1IncassiCur'],
            'vitaAnnL1Rappel' => $revisione['vitaAnnL1Rappel'],
            'vitaAnnL1Obj' => $revisione['vitaAnnL1Obj'],
            'vitaAnnL2Obj' => $revisione['vitaAnnL2Obj'],
            'vitaAnnL2IncassiCur' => $revisione['vitaAnnL2IncassiCur'],
            'vitaAnnL2ExtraRappel' => $revisione['vitaAnnL2ExtraRappel'],
            'statusChangeMessage' => $revisione['statusChangeMessage'],
        ];
    }

     // @batch(description="Accordo Economico: aggiorna fasce vita")
     // @param Request $Req
     // @param Response $Res
     // @throws \metadigit\lib\util\csv\Exception

    function _fixFasceVitaAction(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "ACCORDOECO_FIX_FASCE_2023*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->fixFascieVita(file($files[0]));
    }

    protected function fixFascieVita($data)
    {
        $count = -1;

        foreach ($data as $row) {
            $count++;

            $row = str_getcsv($row, ";");

            $queryAccordi = "SELECT * FROM pianiagz_obj_accordo where agenzia_id = '$row[2]' and year = 2023";

            // Trova Accordo per Agenzia e anno
            if (! $accordo = $this->pdoQuery($queryAccordi)->fetch() ) {
                $this->log("Accordo non trovato per Agenzia $row[2] su anno 2023.");
                continue;
            }

            // Se la fascia sull'accordo è già corretta skip e va avanti
            if ( trim($row[4]) === $accordo['fasciaVita'] ) {
                $this->log("Fascia per accordo {$accordo['agenzia_id']} è già corretto. ROW SKIP.");
                continue;
            }

            $queryUpdateFascia = "UPDATE pianiagz_obj_accordo SET fasciaVita = trim($row[4]) WHERE agenzia_id = '$row[2]' and year = 2023";

            try {
                $this->pdoQuery($queryUpdateFascia)->execute();
                $this->log("Aggiornata accordo agenzia $row[2]. Fascia cambiata: {$accordo['fasciaVita']} -> $row[4]");

            } catch (\Exception $ex) {
                $this->log("Errore durante aggiornamento fascia agenzia $row[2]. Linea $count " . ((isset($row[2])) ? $row[2] : ""));
                $this->log($ex->getMessage());
                continue;
            }

        }
    }

    protected function parseRowAccordoFasciaFix($row)
    {
        return [
            'year' => $row[0],
            'partecipante' => $row[1],
            'agenzia_id' => $row[2],
            'fasciaRamiPref' => trim(strtoupper($row[3])),
            'fasciaVita' => trim($row[4]),
            'status' => $this->parseStatus($row[30]),
            'type' => $this->parseType($row[31]),
            'statusChangeMessage' => $row[32],
            'reminderSent' => 0,
        ];
    }

    /**
     * @batch(description="Accordo Economico: fix Obiettivi 2024")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\lib\util\csv\Exception
     */
    function fixObiettivi2024Action(Request $Req, Response $Res) {
        $files = glob(\metadigit\core\UPLOAD_DIR . "STATICO_ACCORDOECO_FIX_OBIETTIVI_2024*.csv");

        if (($count = count($files)) > 1) {
            $this->log("Found $count files (exit).");
            return;
        }

        if (! $count) {
            $this->log("No input files.");
            return;
        }

        $this->fixObiettivi2024(file($files[0]));
    }

    protected function fixObiettivi2024($data)
    {
        $count = -1;

        foreach ($data as $row) {
            $count++;

            $row = str_getcsv($row, ";");

            // Check su numero di colonne
            if (($cols = count($row)) != 4) {
                TRACE and $this->trace(LOG_DEBUG, 1, __FUNCTION__, "Colonne non corrispondenti. Salto riga.");
                $this->log("Linea $count - colonne attese:{$this->COLS_FIX} colonne trovate $cols");
                continue;
            }

            $queryAccordi = "SELECT * FROM pianiagz_obj_accordo where agenzia_id = '$row[0]' and year = 2024";

            // Trova Accordo per Agenzia e anno
            if (! $accordo = $this->pdoQuery($queryAccordi)->fetch() ) {
                $this->log("Accordo non trovato per Agenzia $row[0] su anno 2024.");
                continue;
            }

            $queryRevisioni = "SELECT * FROM pianiagz_obj_accordo_rev where accordo_id = {$accordo['id']} and isLatest = 1";

            // Trova ultima revisione per ID Accordo
            if (! $revisione = $this->pdoQuery($queryRevisioni)->fetch() ) {
                $this->log("Revisione non trovata per Agenzia $row[0].");
                continue;
            }

            //$newRevisione = $revisione;
            $newRevisione = $this->parseRevisionWithAlteredObj2024($accordo, $revisione, $row);

            // Setto islatest a 0 su vecchia revisione
            try {
                $revisione['isLatest'] = 0;

                $queryUpdateOldRevision = "UPDATE pianiagz_obj_accordo_rev SET isLatest = 0 WHERE id = {$revisione['id']}";
                $this->pdoQuery($queryUpdateOldRevision)->execute();

            } catch (\Exception $ex) {
                $this->log("Errore durante aggiornamento revisione {$revisione['id']}. Linea $count " . ((isset($row[0])) ? $row[0] : ""));
                $this->log($ex->getMessage());
                continue;
            }

            try {
                $sqlStatement = $this->statementRevision();
                $this->pdoStExecute($sqlStatement, $newRevisione);

            } catch (\Exception $ex) {
                $this->log("Errore durante inserimento nuova revisione. Linea $count " . ((isset($row[0])) ? $row[0] : ""));
                $this->log($ex->getMessage());
                continue;
            }

        }
    }

    protected function parseRevisionWithAlteredObj2024($accordo, $revisione, $row)
    {
        return [
            'accordo_id' => $accordo['id'],
            'user_id' => null,
            'mode' => 'FIX_OBJ_2024',
            'isLatest' => 1,
            'status' => $revisione['status'],
            'updatedAt' => date('Y-m-d H:i:s'),

            'ramiPrefSemIncassiPrev' => $revisione['ramiPrefSemIncassiPrev'],
            'ramiPrefSemObj' => $revisione['ramiPrefSemObj'],
            'ramiPrefSemIncremento' => $row[1] > 0 ? $row[1] : $revisione['ramiPrefSemIncremento'],
            'ramiPrefSemIncassiCur' => $row[1] > 0 ? $revisione['ramiPrefSemIncassiPrev'] + $row[1] : $revisione['ramiPrefSemIncassiCur'],
            'ramiPrefSemRappel' => $revisione['ramiPrefSemRappel'],

            'ramiPrefAnnL1IncassiPrev' => $revisione['ramiPrefAnnL1IncassiPrev'],
            'ramiPrefAnnL1Obj' => $revisione['ramiPrefAnnL1Obj'],
            'ramiPrefAnnL1Incremento' => $row[2] > 0 ? $row[2] : $revisione['ramiPrefAnnL1Incremento'],
            'ramiPrefAnnL1IncassiCur' => $row[2] > 0 ? $revisione['ramiPrefAnnL1IncassiPrev'] + $row[2] : $revisione['ramiPrefAnnL1IncassiCur'],
            'ramiPrefAnnL1Rappel' => $revisione['ramiPrefAnnL1Rappel'],

            'ramiPrefAnnL2Obj' => $revisione['ramiPrefAnnL2Obj'],
            'ramiPrefAnnL2Incremento' => $row[3] > 0 ? $row[3] : $revisione['ramiPrefAnnL2Incremento'],
            'ramiPrefAnnL2IncassiCur' => $row[3] > 0 ? $revisione['ramiPrefAnnL1IncassiPrev'] + $row[3] : $revisione['ramiPrefAnnL2IncassiCur'],
            'ramiPrefAnnL2Rappel' => $revisione['ramiPrefAnnL2Rappel'],
            'ramiPrefAnnL2ObjPersonal' => $revisione['ramiPrefAnnL2ObjPersonal'],
            'ramiPrefAnnL2IncrementoPersonal' => $revisione['ramiPrefAnnL2IncrementoPersonal'],
            'ramiPrefAnnL2IncassiCurPersonal' => $revisione['ramiPrefAnnL2IncassiCurPersonal'],
            'ramiPrefAnnL2RappelPersonal' => $revisione['ramiPrefAnnL2RappelPersonal'],

            'vitaBaseCalcoloObj' => $revisione['vitaBaseCalcoloObj'],
            'vitaAnnL1ObjPezzi' => $revisione['vitaAnnL1ObjPezzi'],
            'vitaAnnL1IncassiCur' => $revisione['vitaAnnL1IncassiCur'],
            'vitaAnnL1Rappel' => $revisione['vitaAnnL1Rappel'],
            'vitaAnnL1Obj' => $revisione['vitaAnnL1Obj'],
            'vitaAnnL2Obj' => $revisione['vitaAnnL2Obj'],
            'vitaAnnL2IncassiCur' => $revisione['vitaAnnL2IncassiCur'],
            'vitaAnnL2ExtraRappel' => $revisione['vitaAnnL2ExtraRappel'],
            'statusChangeMessage' => $revisione['statusChangeMessage'],
        ];
    }


     // @batch(description="Accordo Economico: rimuove ultima revisione e ripristina la penultima come latest")
     // @param Request $Req
     // @param Response $Res
     // @throws \metadigit\lib\util\csv\Exception

    function _restoreLatestAction(Request $Req, Response $Res) {

        $queryRevisioni = "SELECT * FROM pianiagz_obj_accordo_rev where mode = 'FIX_OBJ_2024'";
        $revisioni = $this->pdoQuery($queryRevisioni)->fetchAll(Repository::FETCH_ARRAY);

        foreach ($revisioni as $rev) {
            $accordo_id = $rev['accordo_id'];
            $updateRevisionQuery = "UPDATE pianiagz_obj_accordo_rev SET isLatest = 1 WHERE mode != 'FIX_OBJ_2024' AND accordo_id = $accordo_id order by id desc limit 1";
            $this->pdoQuery($updateRevisionQuery)->execute();
            $removeFixQuery = "DELETE FROM pianiagz_obj_accordo_rev r WHERE r.accordo_id =  $accordo_id AND r.mode = 'FIX_OBJ_2024'";
            $this->pdoQuery($removeFixQuery)->execute();
        }

    }

}
