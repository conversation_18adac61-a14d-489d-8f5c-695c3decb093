<?php

namespace batch;


use data\apps\formazione\Managers\ELearningManager;
use data\apps\formazione\Models\Attendance;
use data\apps\formazione\Models\ELearningFetchStatus;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response;
use service\AbiService;

class WeekELearningFetchRemote
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
	use \metadigit\core\db\PdoTrait;
	use \metadigit\lib\batch\BatchTrait;
	use \metadigit\core\CoreTrait;

    /**
     * Endpoint URL
     */
    public $endpoint;

    /**
     * Token
     */
    public $accessToken;

    /**
     * Data iniziale di default
     * @var string
     */
    public $fetchDateStart;

    /**
     * Data finale di default
     * @var string
     */
    public $fetchDateEnd;

    /**
     * Intervallo di acquisizione, in giorni
     * @var int
     */
    public $fetchDaysInterval;
    /**
     * TRUE:  calcola le date ma non invia
     * FALSE: procede normalmente
     * @var bool
     */
    protected $pretend;

    /**
     * Log vars
     */
    protected $logXml;
    protected $logDirectory;

    /**
     * Blocca l'acquisizione all'anno impostato.
     * @var int
     */
    protected $lockYear;

    /**
     * @var array
     * 	[0]: DateTime - Data iniziale di acquisizione, computata
     * 	[1]: DateTime - Data finale di acquisizione, computata
     */
    protected $dateInterval;

    /**
     * TRUE:  calcola le date ma non salva nulla, scrive nel log quello che avrebbe fatto
     * FALSE: procede normalmente
     * @var bool
     */
    protected $debug;


    /**
     * Retrieve the data from the E-Learning Platform
     *
     * @batch(description="Formazione: recupero dei dati dalla piattaforma e-learning week")
     * @throws \Exception
     */
    public function runAction(Request $Req, Response $Res)
    {
        $matches = array();
        $this->dateInterval = array();
        $this->config();

        // Init date
        $this->fetchDateStart = new \DateTime($this->fetchDateStart);
        // End date
        $this->fetchDateEnd = new \DateTime($this->fetchDateEnd);

        // Init log
        $this->log('Setup service');

        // Determina gli intervalli di lettura
        $this->dateInterval = $interval = $this->getNextInterval($this->fetchDaysInterval);
        if (empty($this->dateInterval)) {
            $this->log($this->dateError);
            return;
        }
        $this->log("Start fetch interval: " . $interval[0]->format(DATE_ISO8601));
        $this->log("Start end interval  : " . $interval[1]->format(DATE_ISO8601));

        // Init Manager
        $serviceManager = new ELearningManager();
        $abiService = new AbiService();

        // Init status
        $fetchStatus = new ELearningFetchStatus();
        $fetchStatus->date = date(DATE_ISO8601);
        $fetchStatus->dateInterval0 = $this->dateInterval[0]->format(DATE_ISO8601);
        $fetchStatus->dateInterval1 = $this->dateInterval[1]->format(DATE_ISO8601);
        $fetchStatus->success = FALSE;
        $fetchStatus->message = '';

        try {
            // Invocazione e controllo della response
            $from = $interval[0]->format('Ymd');
            $to = $interval[1]->format('Ymd');

            $response = $abiService->partecipationReport($from, $to);
            if (!$response) {
                $fetchStatus->message = 'Network error, remote read failed';
                throw new \Exception('Network error, remote read failed');
            }
            // Parse della response.
            $result = $serviceManager->parseResponse($response);

            $countSubscriptions = 0;
            $countCorsi = 0;

            if ($this->pretend) {
                $this->log("Exit because of pretend=TRUE.");
                $this->log(print_r($result, TRUE));
                return;
            }

            // Init transazione
            $this->pdoBeginTransaction();

            $matches = [
                'no' => [],
                'dupes' => [],
                'neo' => [],
                'ok' => []
            ];

            // Iterazioni di scrittura.
            foreach ($result as $id => $remoteCorso) {
                $this->log("Remote Course ID ". $id);

                if (!in_array($remoteCorso['instance']->tipo, [ 'NORM', 'SSPR', 'TECN', 'GIUR', 'TECA', 'ADMG', 'INFO', 'ACOP' ])) {
                    throw new \Exception("Tipo Corso is not valid!");
                }

                $localCorso = $this->getCourse($id, $this->getYear());
                if (!$localCorso) {
                    if ($this->debug) {
                        $this->log("DEBUG: Creo il corso " . $remoteCorso);
                        $this->log("DEBUG: Creo le classi associate al corso" . $remoteCorso);
                        $this->log("DEBUG: Creo le partecipazioni associate al corso" . $remoteCorso);
                        continue;
                    }
                    $res = $this->createCourse($id, $remoteCorso);
                    if (!$res) {
                        continue;
                    }
                    $localCorso = $this->getCourse($id, $this->getYear());
                }
                if (!$localCorso) {
                    $this->log("Course ID not found". $id);
                    continue;
                }

                if ($this->debug) {
                    $this->log("DEBUG: Esiste il corso " . $remoteCorso);
                }

                $abstractId = $localCorso['id'];
                $countIterationSubscriptions = 0;
                foreach ($remoteCorso['users'] as $subscription) {
                    $user = $this->getUserAuth($subscription['id_utente_agendo']);
                    if (empty($user)) {
                        $this->log("User {$subscription['id_utente_agendo']} doesn't match.");
                        $matches['no'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];
                        continue;
                    }

                    if (!$user = $this->dealWithDuplicates($user, $subscription['id_utente_agendo'])) {
                        $matches['dupes'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];
                        continue;
                    }

                    // Aggiorno l'array di reportistica
                    $matches['ok'][$subscription['id_utente_agendo']] = $subscription['id_utente_agendo'];

                    // Update 30/10/2018: im alcuni casi sembra che dal sistema
                    // remoto vengano acquisite partecipazioni multiple di un
                    // utente ad uno stesso corso. Facciamo un check di sicurezza
                    // ed eventualmente saltiamo per evitare di interrompere
                    // l'intera procedura per un vincolo FK saltato.
                    if ($checkPartecipazione = $this->getAttendance($abstractId, $user['id'])) {

                        if ($this->debug) {
                            $this->log("DEBUG: Esiste la partecipazione dell'utente " . $user['id'] . " al corso " . $remoteCorso);
                            $this->log("DEBUG: Controllo se ci sono differenze su crediti, punteggio e data di completamento.");
                        }

                        // Check differences
                        $credits = $score = $completedAt = false;
                        if ($checkPartecipazione['credits'] != $localCorso['credits']) {
                            $credits = true;
                        }
                        if ($checkPartecipazione['score'] != $subscription['punteggio']) {
                            $score = true;
                        }
                        $completedDate = (new \DateTime($checkPartecipazione['completedAt']))->format('Ymd');
                        if ($completedDate != $subscription['dataCompletamento']) {
                            $completedAt = true;
                        }
                        // Log
                        if ($credits || $score || $completedAt) {
                            $this->log("Different DUPLICATE corso $abstractId user: {$subscription['id_utente_agendo']}");
                            $this->log("CREDITS: before {$checkPartecipazione['credits']}, after {$localCorso['credits']}");
                            $this->log("SCORE: before {$checkPartecipazione['score']}, after {$subscription['punteggio']}");
                            $this->log("COMPLETED AT: before {$checkPartecipazione['completedAt']}, after {$subscription['dataCompletamento']}");
                        } else {
                            $this->log("Skip DUPLICATE corso $abstractId user: {$user['id']} {$subscription['id_utente_agendo']}");
                        }
                        continue;
                    }

                    if (!$classroomId = $this->getClassroom($abstractId)['id']) {
                        if ($this->debug) {
                            $this->log("DEBUG: Creo la classe " . $abstractId . " associata al corso " . $remoteCorso);
                        } else {
                            $this->createClassroom($abstractId);
                            $classroomId = $this->pdoLastInsertId();
                        }
                    }

                    if ($this->debug) {
                        $this->log("DEBUG: Creo la partecipazione per l'utente " . $user['id'] . " nella classe " . $abstractId . " associata al corso " . $remoteCorso);
                    } else {
                        $abstractPartecipazione = new Attendance();
                        $abstractPartecipazione->setUserId($user['id']);
                        $abstractPartecipazione->setCourseId($abstractId);
                        $abstractPartecipazione->setClassId($classroomId);
                        $abstractPartecipazione->setResult('ok');
                        $abstractPartecipazione->setCredits($localCorso['credits']);
                        $abstractPartecipazione->setRole($this->setRole($user));
                        $abstractPartecipazione->setScore($subscription['punteggio']);
                        $abstractPartecipazione->setCompletedAt($subscription['dataCompletamento']);
                        $abstractPartecipazione->setCreatedAt((new \DateTime('now'))->format('Y-m-d H:i:s'));
                        $abstractPartecipazione->setUpdatedAt((new \DateTime('now'))->format('Y-m-d H:i:s'));
                        $abstractPartecipazione->setState('signedup');
                        $abstractPartecipazione->setAuthorId(null);
                        $abstractPartecipazione->setElearningProvider('WEMOLE');

                        $this->createAttendance($abstractPartecipazione);
                    }

                    $countSubscriptions++;
                    $countIterationSubscriptions++;
                }
                if ($countIterationSubscriptions) {
                    $countCorsi++;
                }
            }
            // Stats
            $matchesOk = count($matches['ok']);
            $matchesNo = count($matches['no']);
            $countUsers = $matchesOk + $matchesNo;

            // Set status
            $fetchStatus->count = $countSubscriptions;
            $fetchStatus->success = TRUE;
            $fetchStatus->message .= "$matchesNo fails.";

            // Separo la logica della commit dati da quella
            // della scrittura dello status.
            $this->pdoCommit();
        } catch (\Exception $ex) {
            $this->trace(LOG_DEBUG, 1, 'TRY CATCH', __FUNCTION__, $ex->getMessage());

            $fetchStatus->success = FALSE;
            $this->log('Exception ' . $ex->getMessage());
            $this->pdoRollback();
            trigger_error($ex->getMessage());
        }

        $this->log("=============================");
        $this->log("Corsi acquisiti: $countCorsi");
        $this->log("Partecipazioni acquisite: $countSubscriptions");
        $this->log("Utenti: $countUsers");
        $this->log("Utenti matchati: $matchesOk (" . (number_format(($countUsers !== 0) ? $matchesOk/$countUsers*100 : 0, 2)) . "%)");
        $this->log("Utenti non matchati: $matchesNo (" . (number_format(($countUsers !== 0) ? $matchesNo/$countUsers*100 : 0, 2)) . "%)");
        $this->log("Utenti matchati duplicati non risolvibili: " . count($matches['dupes']));
        $this->log("Dettaglio utenti matchati duplicati non risolvibili: " . implode(", ", $matches['dupes']));

        // } Finally { :p
        //	@todo Log response XML in caso di errori.
        $this->saveFetchStatus($fetchStatus);

        // Delego la gestione dei match.
//$this->emailErrors($countSubscriptions, $matches, $response);
    }

    private function config()
    {
        // Service config
        $this->endpoint = 'https://[da-definire]/webservice/rest/server.php{report_partecipazione}';
        $this->accessToken = 'zakkwylde';
        $this->fetchDateStart = '2024-05-01T00:00:00';
        $this->fetchDateEnd = '2024-05-01T23:59:59';
        $this->fetchDaysInterval = '7';
        $this->pretend = false;
        $this->debug = false;

        // Local config
        $this->logXml = TRUE;
        $this->logDirectory = '/storage/portaleagendo.it/data/upload';

        // Locko le acquisizioni sull'anno hard-coded
        // per evitare accavallamenti.
        //$this->lockYear = 2024;
    }

    /**
     *	Calcola il prossimo intervallo di lettura dati.
     *	Se non esiste nessuna sessione pregressa utilizza come base
     *	di partenza la proprietà $this->fetchDaysInterval, altrimenti
     *	il dateInterval1 dell'ultima sessione andata a buon fine.
     *
     *	@param int $timespan
     *		Intervallo di lettura espresso in giorni
     *	@return array
     *		Se la selezione va a buon fine restituisce array:
     *			0: data iniziale (DateTime)
     *			1: data finale (DateTime)
     *		altrimenti array vuoto.
     */
    protected function getNextInterval($timespan)
    {
        // Leggo la data di partenza.
        $status = $this->getStartDate();

        if (empty($status)) {
            $this->log('Fetch status not found, starting from fetchDateStart');
            $dateStart = $this->fetchDateStart;
        } else {
            $dateStart = new \DateTime($status[0]['dateInterval1']);
            $dateStart->add(new \DateInterval('PT1S'));
        }

        // Leggo la data di fine.
        $statusF = $this->getEndDate();

        if (empty($statusF)) {
            $this->log('Fetch statusF not found, ending from fetchDateStart');
            $dateEnd = $this->fetchDateEnd;
        } else {
            $dateEnd = new \DateTime($statusF[0]['dateInterval1']);
            $dateEnd = $dateEnd->sub(new \DateInterval("P{$timespan}D"));
        }

        // Se la data di partenza è maggiore della data di fine
        // devo interrompere e rimandare l'acquisizione.
        if ($dateStart > $dateEnd) {
            $this->dateError = "La data di partenza è superiore alla data di fine, acquisizione interrotta.";
            return array();
        }

        // Se la data di partenza è maggiore della data odierna
        // devo interrompere e rimandare l'acquisizione.
        if ($dateStart > new \DateTime(('now'))) {
            $this->dateError = "La data di partenza è superiore alla data odierna, acquisizione interrotta.";
            return array();
        }

        // Se la data di fine è maggiore della data odierna
        // la sovrascrivo con DataOdierna - 1D.
        if ($dateEnd > $now = new \DateTime(('now'))) {
            $dateEnd = new \DateTime($now->format('Y') . '-' .
                $now->format('m') . '-' .
                $now->format('d') .
                'T23:59:59+00:00');
            $dateEnd->sub(new \DateInterval('P1D'));
            $this->log("La data di fine è superiore alla data odierna, data corretta in " . $dateEnd->format('Y-m-d H:i:s'));
        }

        return array ($dateStart, $dateEnd);
    }

    protected function dealWithDuplicates($users, $userId)
    {
        if (count($users) == 1) {
            return $users[0];
        }

        $this->log("Analyze matches for {$userId}...");

        $result = [];
        foreach ($users as $user) {
            if ($user['active'] && $user['agenzia_id'] && $user['type']) {
                $result[] = $user;
                continue;
            }

            $this->log("{$userId} is invalid");
        }

        $count = count($result);

        if ($count == 1) {
            $this->log("{$userId} resolved to ".$result[0]['id']);
            return $result[0];
        } elseif ($count == 0 || $count > 1) {
            $this->log("{$userId} ".$result[0]['id']." cannot be resolved");
        }

        return null;
    }

    /**
     * @return int
     * L'anno dell'acquisizione corrente, o NULL se l'intervallo
     * di acquisizione (dateInterval) non è stato valorizzato
     * propriamente.
     */
    protected function getYear()
    {
        if (empty($this->dateInterval) || empty($this->dateInterval[1])) {
            return NULL;
        }
        return $this->dateInterval[1]->format('Y');
    }

    private function getOne($query)
    {
        $result = $this->pdoQuery($query)->fetch();

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    private function getAll($query)
    {
        $result = $this->pdoQuery($query)->fetchAll();

        if (empty($result)) {
            return null;
        }

        return $result;
    }

    private function getCourse($remoteId, $year)
    {
        $query = "SELECT * FROM tra_course c WHERE c.remoteId = '$remoteId' AND c.year = '$year'";
        return $this->getOne($query);
    }

    private function getUserAuth($id)
    {
        $query = "SELECT * FROM vw_users_auth WHERE id = '".$id."'";
        return $this->getAll($query);
    }

    private function getUser($id)
    {
        $query = "SELECT * FROM users WHERE id = '".$id."'";
        return $this->getOne($query);
    }

    private function createCourse($remoteId, $remoteCorso)
    {

        $isGdpr = 0;
        $isAnti = 0;

        if ($remoteId === 'GDPR_RETE_AG' || $remoteId === 'GDPR_RETE_CL') {
            $isGdpr = 1;
        }

        if ($remoteId === 'e1185c40') {
            $isAnti = 1;
        }

        if (!$remoteCorso['instance']->year) {
            return null;
        }

        try {
            $query = "INSERT INTO tra_course ( title, code, data, type, credits, groupamaType, tipo, status, year, filters, remoteId, gdpr, antiriciclaggio, modalita, erogato )
                  VALUES (:title, :code, :data, :type, :credits, :groupamaType, :tipo, :status, :year, :filters, :remoteId, :gdpr, :antiriciclaggio, :modalita, :erogato)";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'title' => $remoteCorso['instance']->title,
                'code' => '',
                'data' => '',
                'type' => 'register',
                'credits' => $remoteCorso['instance']->credits,
                'groupamaType' => 'e-learning',
                'tipo' => $remoteCorso['instance']->tipo,
                'status' => 'on',
                'year' => $remoteCorso['instance']->year,
                'filters' => null,
                'remoteId' => $remoteId,
                'gdpr' => $isGdpr,
                'antiriciclaggio' => $isAnti,
                'modalita' => 'DIST',
                'erogato' => 'DIREZ'
            ]);

            $this->log("Nuovo corso inserito. Codice remoto: ".$remoteId);
            $this->log("Crediti assegnati: ".$remoteCorso['instance']->credits);

            return $result;
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());

            return null;
        }
    }

    private function getStartDate()
    {
        $query = "SELECT * FROM tra_elearning_week_fetch_session WHERE success = 1 ORDER BY dateInterval1 DESC";
        return $this->getAll($query);
    }

    private function getEndDate()
    {
        $query = "SELECT * FROM tra_elearning_fetch_session WHERE success = 1 ORDER BY dateInterval1 DESC";
        return $this->getAll($query);
    }

    protected function logXmlResponse($xml)
    {
        if (!$this->logXml) {
            return;
        }
        $xmlfile = $this->getXmlFilename();
        file_put_contents($xmlfile, $xml);
    }

    protected function getXmlFilename()
    {
        return $this->logDirectory . "/elearning_".date('Y_m_d_H-i').".xml";
    }

    private function getAttendance($courseId, $userId)
    {
        $query = "SELECT * FROM vw_tra_attendance WHERE course_id = '".$courseId."' AND user_id = '".$userId."'";
        return $this->getOne($query);
    }

    private function createAttendance(Attendance $attendance)
    {
        try {
            $query = "INSERT INTO `tra_attendance` ( `user_id`, `course_id`, `class_id`, `result`, `data`, `role`, `createdAt`, `updatedAt`, `state`, `author_id`, `score`,`completedAt`,`credits`, `elearningProvider`)
                  VALUES (:userId, :courseId, :classId, :result, null, :role, :createdAt, :updatedAt, :state, :authorId, :score, :completedAt, :credits, 'WEMOLE')";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'userId' => $attendance->getUserId(),
                'courseId' => $attendance->getCourseId(),
                'classId' => $attendance->getClassId(),
                'result' => $attendance->getResult(),
                'role' => $attendance->getRole(),
                'createdAt' => $attendance->getCreatedAt(),
                'updatedAt' => $attendance->getUpdatedAt(),
                'state' => $attendance->getState(),
                'authorId' => $attendance->getAuthorId(),
                'score' => $attendance->getScore(),
                'completedAt' => $attendance->getCompletedAt(),
                'credits' => $attendance->getCredits()
            ]);

            $this->log("Nuova partecipazione inserita.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'createAttendance', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function saveFetchStatus($fetchStatus)
    {
        try {
            $query = "INSERT INTO `tra_elearning_week_fetch_session` ( `date`, `dateInterval0`, `dateInterval1`, `count`, `success`, `message`, `elearningProvider` )
                  VALUES (:date, :dateInterval0, :dateInterval1, :count, :success, :message, 'WEMOLE')";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'date' => $fetchStatus->date,
                'dateInterval0' => $fetchStatus->dateInterval0,
                'dateInterval1' => $fetchStatus->dateInterval1,
                'count' => $fetchStatus->count,
                'success' => $fetchStatus->success,
                'message' => $fetchStatus->message,
            ]);

            $this->log("Fetch status salvato.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'saveFetchStatus', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function getClassroom($courseId)
    {
        $query = "SELECT * FROM tra_class WHERE course_id = '".$courseId."' AND status = 'active'";
        return $this->getOne($query);
    }

    private function createClassroom($courseId)
    {
        try {
            $query = "INSERT INTO `tra_class` ( `course_id`, `status` )
                  VALUES (:course_id, :status)";
            $pdoSt = $this->pdoPrepare($query);
            $result = $pdoSt->execute([
                'course_id' => $courseId,
                'status' => 'active'
            ]);

            $this->log("Nuova classe salvata.");

            return $result;
        } catch (\Exception $ex) {
            $this->trace(LOG_ERR, 1, 'createClassroom', __FUNCTION__, $ex->getMessage());
            return null;
        }
    }

    private function setRole($user)
    {
        if ($user['type'] == 'AGENTE') {
            return $user['type'];
        }

        if ($user['type'] == 'INTERMEDIARIO' && $user['ruolo'] == 'NEO') {
            return $user['ruolo'];
        }

        if ($user['type'] == 'INTERMEDIARIO' && $user['ruolo'] != 'NEO') {
            return $user['type'];
        }
    }
}
