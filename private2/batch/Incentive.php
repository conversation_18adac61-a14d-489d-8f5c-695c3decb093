<?php namespace batch;

use api\apps\incentive\IncentiveInterface;
use api\apps\incentive\rinnoviamoci\exceptions\ColumnsMismatchException;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;
use metadigit\core\CoreTrait;

class Incentive extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait, CoreTrait;

    //
    //
    //  Config properties.
    //
    //

    const CSV_DATA_COLS = 35;

    /**
     * @var IncentiveInterface
     */
    protected $incentive;

    /**
     * @boolean
     */
    protected $dataHasHeader;

    /**
     * @boolean
     */
    protected $staticHasHeader;

    /**
     * @char
     */
    protected $dataSeparator;

    /**
     * @char
     */
    protected $staticSeparator;

    /**
     * @bool
     */
    protected $enableMemProfiler;

    /**
     * @bool
     */
    protected $debug;

    //
    //
    //  Local properties.
    //
    //

    /**
     * @var Previous memory read
     */
    protected $prevMem;

    /**
     * @var Parsed filename.
     */
    protected $parsed;

    /**
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile($path = $this->incentive->getDataPath())) {
            $this->l("File not found for path: $path");
            return;
        }

        if (! $this->parsed = $this->parseFilename($file)) {
            $this->l("Cannot parse $file");
            return;
        }

        //$this->pdoBeginTransaction();

        // Init stage.
        if ($initStatement = $this->incentive->getDataInitStatement()) {
            $this->pdoStExecute($initStatement, ["id" => $this->parsed->id]);
        }

        // Insert statement.
        $statement = $this->pdoPrepare($this->incentive->getDataStatement());

        $this->dumpMemory("Init");

        //$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'test', print_r($statement));

        // Insert loop.
        $this->insert(
            $statement,
            file($file),
            $this->parsed->id,
            $this->dataHasHeader,
            $this->dataSeparator,
            [$this->incentive, 'parseDataRow']
        );

        // Incentive data "general" table (for policies detail download)
        if (in_array($this->parsed->id, [293, 294, 295, 300, 304, 305, 311, 324, 325, 329])) {
            $incentiveDataInitStatement = $this->getIncentiveDataInitStatement($this->parsed->id);
            $this->pdoStExecute($incentiveDataInitStatement);
            $incentiveDataStatement = $this->pdoPrepare($this->getIncentiveDataStatement());

            $this->insert(
                $incentiveDataStatement,
                file($file),
                $this->parsed->id,
                $this->dataHasHeader,
                $this->dataSeparator,
                [$this, 'parseIncentiveDataRow']
            );
        }

        $this->dumpMemory("End");

        // Post insert hook.
        $this->incentive->postInsert($this->parsed);

        // Timestamps.
        $this->pdoStExecute(
            "update incentive set lastUpdate = :lastUpdate, lastUpdateLabel = :lastUpdateLabel where id = :id",
            [
                ':lastUpdate' => date("Y-m-d H:i:s"),
                ':lastUpdateLabel' => $this->parsed->date,
                ':id' => $this->parsed->id,
            ]
        );

        //$this->pdoCommit();
    }

    /**
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile($path = $this->incentive->getStaticPath())) {
            $this->l("File not found for path: $path");
            return;
        }

        if (! $this->parsed = $this->parseFilename($file)) {
            $this->l("Cannot parse $file");
            return;
        }

        //$this->pdoBeginTransaction();

        // Init stage.
        if ($initStatement = $this->incentive->getStaticInitStatement()) {
            $this->pdoStExecute($initStatement, ["id" => $this->parsed->id]);
        }

        // Insert statement.
        $statement = $this->pdoPrepare($this->incentive->getStaticStatement());

        // Insert loop.
        $this->insert(
            $statement,
            file($file),
            $this->parsed->id,
            $this->staticHasHeader,
            $this->staticSeparator,
            [$this->incentive, "parseStaticRow"]
        );

        //$this->pdoCommit();
    }

    protected function insert($statement, $data, $id, $hasHeader, $separator, $callback)
    {
        //$this->dumpMemory("Before loop");

        if ($hasHeader) {
            array_shift($data);
        }

        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
        ];

        // Scan CSV file.
        foreach ($data as $row) {

            try {
                // Ask the application to parse the row.
                $rowInfo = call_user_func_array($callback, [str_getcsv($row, $separator), $id]);

                //$this->dumpMemory("Loop: after parse");

                // Insert.
                $statement->execute($rowInfo);

                //$this->dumpMemory("Loop: after insert");

                $result['OK']++;
            }

            catch (\PDOException $ex) {
                $result['KO']++;
                //$this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, $ex->getMessage());

                if ($this->debug) {
                    if ($ex->getCode() == 23000) {
                        $this->l("Not found {$rowInfo['agenzia_id']}");
                    }

                    $this->l(print_r($rowInfo, true));
                    $this->l($ex->getMessage());
                }
            }

            catch (\Exception $ex) {
                $result['KO']++;
                //$this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, $ex->getMessage());

                if ($this->debug) {
                    $this->l("Skip agenzia {$rowInfo['agenzia_id']}");
                    $this->l(print_r($rowInfo, true));
                    $this->l($ex->getMessage());
                }
            }

            $line++;
        }

        $this->log("TOT\t$line");

        foreach ($result as $key => $value) {
            $this->log("$key\t$value\t" . (($value/$line) * 100) . "%");
        }
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        if (! ($count = count($files))) {
            $this->l("File $filepath not found.");
            return null;
        }

        if ($count != 1) {
            $this->l("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    protected function parseFilename($filename)
    {

        $this->parsed = (object)[
            'id' => null,
            'date' => null,
        ];

        try {
            return $this->parsed = (object)[
                'id' => $this->incentive->parseId($filename),
                'date' => $this->incentive->parseDate($filename),
            ];
        } catch (\Exception $ex) {
            return null;
        }
    }

    protected function dumpMemory($label = null)
    {
        if (! $this->enableMemProfiler) {
            return;
        }

        $current = memory_get_usage();

        $output = [
            "Peak:",
            $this->formatMemory(memory_get_peak_usage()),
            "Peak (allocated):",
            $this->formatMemory(memory_get_peak_usage(true)),
            "Total:",
            $this->formatMemory($current),
            "Delta:",
        ];

        if ($this->prevMem) {
            $output[] = $this->formatMemory($current - $this->prevMem);
        } else {
            $this->prevMem = $current;
        }

        if ($label) {
            array_unshift($output, $label);
        }

        return $this->log(implode("\t", $output));
    }

    protected function formatMemory($mem)
    {
        return number_format($mem / 1024 / 1024, 5) . "MB";
    }

    public function l($data)
    {
        if (is_array($data) || is_object($data)) {
            $data = print_r($data, true);
        }

        $this->log($data);
    }

    protected function getIncentiveDataInitStatement($incentive_id)
    {
        return "delete from incentive_data where incentive_id = $incentive_id";
    }

    protected function getIncentiveDataStatement()
    {
        return "INSERT INTO incentive_data (
            ptf,
            incentive_id,
            dataEstrazione,
            dataEmissione,
            dataIncasso,
            dataEffetto,
            agenzia_id,
            ramo,
            intermediario,
            delega,
            sezione,
            polizza,
            codiceIdBene,
            nomeCliente,
            codiceCliente,
            CF_PIVA,
            codiceProdotto,
            motivo,
            famigliaProdotto,
            premioAnnuo,
            premioUnico,
            versamAgg,
            deltaPremio,
            premioComputabile,
            codiceCampagna,
            codiceConvenzione,
            codicePartnership,
            tipoCollettiva,
            numPolizzaMadre,
            modulareVita,
            OTP,
            FEA,
            trasmissioneElettr,
            polizzaDigitale
        ) values (
            :ptf,
            :incentive_id,
            :dataEstrazione,
            :dataEmissione,
            :dataIncasso,
            :dataEffetto,
            :agenzia_id,
            :ramo,
            :intermediario,
            :delega,
            :sezione,
            :polizza,
            :codiceIdBene,
            :nomeCliente,
            :codiceCliente,
            :CF_PIVA,
            :codiceProdotto,
            :motivo,
            :famigliaProdotto,
            :premioAnnuo,
            :premioUnico,
            :versamAgg,
            :deltaPremio,
            :premioComputabile,
            :codiceCampagna,
            :codiceConvenzione,
            :codicePartnership,
            :tipoCollettiva,
            :numPolizzaMadre,
            :modulareVita,
            :OTP,
            :FEA,
            :trasmissioneElettr,
            :polizzaDigitale
        )";
    }

    protected function parseIncentiveDataRow(array $row, $incentiveId)
    {
        // Assert columns.
        if (($count = count($row)) < self::CSV_DATA_COLS) {
            throw new ColumnsMismatchException(
                "Columns mismatch: expected " . self::CSV_DATA_COLS . "/ found $count / row: " . print_r($row, true)
            );
        }

        // Trim columns.
        $row = array_map(function ($item) {
            return trim($item);
        }, $row);

        $agenzia_id = $row[6] . str_pad($row[8], 3, "0", STR_PAD_LEFT);

        return [
            'ptf' => $row[0],
            'incentive_id' => $row[1],
            'dataEstrazione' => $row[2],
            'dataEmissione' => $row[3],
            'dataIncasso' => $row[4],
            'dataEffetto' => $row[5],
            'agenzia_id' => $agenzia_id,
            'ramo' => $row[7],
            'intermediario' => $row[9],
            'delega' => $row[10],
            'sezione' => $row[11],
            'polizza' => $row[12],
            'codiceIdBene' => $row[13],
            'nomeCliente' => $row[14],
            'codiceCliente' => $row[15],
            'CF_PIVA' => $row[16],
            'codiceProdotto' => $row[17],
            'motivo' => $row[18],
            'famigliaProdotto' => $row[19],
            'premioAnnuo' => $row[20],
            'premioUnico' => $row[21],
            'versamAgg' => $row[22],
            'deltaPremio' => $row[23],
            'premioComputabile' => $row[24],
            'codiceCampagna' => $row[25],
            'codiceConvenzione' => $row[26],
            'codicePartnership' => $row[27],
            'tipoCollettiva' => $row[28],
            'numPolizzaMadre' => $row[29],
            'modulareVita' => $row[30],
            'OTP' => $row[31],
            'FEA' => $row[32],
            'trasmissioneElettr' => $row[33],
            'polizzaDigitale' => $row[34],
        ];
    }

}
