<?php

namespace batch;


use data\apps\formazione\Managers\MailerManager;
use data\apps\formazione\Managers\StudentManager;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class FormazioneStatusMail
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
    use \metadigit\lib\batch\BatchTrait;

    /**
     * @var MailerManager
     */
    protected $mailerManager;


    /**
     * Per invio ad una selezione di agenzie impostare
     * - reportThreshold = null
     * - testAgenzieData
     *
     * @batch(description="FormazioneStatusMail: invio ad una selezione di agenzie impostare")
     * @throws \Exception
     */
    function runAction(Request $Req, Response $Res)
    {
        $config = [
            // Invia solo ad indirizzi KA.
            'forceDebug' => true,

            // Massimo numero di invii in Bcc quando debug = false.
            'maxBccEmails' => 15,

            // Massimo numero di email da inviare in modalità debug.
            'maxDebugEmails' => 10,

            // Genera report csv.
            'report' => false,

            // Genera report ed esce senza effettuare alcun invio.
            'reportOnly' => false,

            // Soglia percentuale per la generazione del report di test.
            // null o 0 disabiltano la soglia.
            'reportThreshold' => 0.10,

            // Quanti record includere nel report csv.
            // Questo valore è aggiornato automaticamente
            // in base alla soglia reportThreshold.
            'reportLimit' => 0,

            // Abilita ordinamento random ed array testAgenzieData.
            'testMode' => true,

            // Array di agenzie su cui effettuare l'invio (in modalità debug = 0).
            //'testAgenzieData' => ["'N904'", "'G306'", "'N977'", "'G906'", "'NF49'", "'G375'", "'N330'", "'G849'", "'N268'", "'G429'", "'G287'", "'N479'",],
            'testAgenzieData' => [],
        ];

        $this->log("BEGIN::send email");

        $result = $this->fetchData(1000, $config);

        // Init report stats.
        $reports = array(
            'success' => array(),
            'empty' => array(),
            'exception' => array(),
            'noAgente' => array(),
            'records' => count($result),
        );

        $countRecords = count($result);
        $devCounter = 0;

        if ($config['reportThreshold'] > 0) {
            $config['reportLimit'] = ceil($countRecords * $config['reportThreshold']);
        }

        $this->log("$countRecords record");
        $this->log("Limit: " . $config['reportLimit']);

        // Riordino i risultati raggruppando per agenzia.
        $agData = array();
        foreach ($result as $row) {
            if ($row->type == 'AGENTE') {
                $agData[$row->agenzia_id]['users']['agenti'][] = $row;
            } else {
                $agData[$row->agenzia_id]['users']['altri'][] = $row;
            }

            if ($config['report'] || $config['reportOnly']) {
                $this->log("{$row->agenzia_id};{$row->type};{$row->uid};{$row->cognome};{$row->nome};{$row->sumOreFormativeDirez};{$row->sumOreFormativeAree};{$row->sumOreFormativeELearning};{$row->sumOreFormative}");
            }

            if ($config['forceDebug'] && $config['reportLimit'] > 0 && $devCounter++ > $config['reportLimit']) {
                break;
            }
        }

        if ($config['reportOnly']) {
            $this->log("Report done.");
            return;
        }

        // Dev + test vars.
        $devCounter = 0;
        $pretend = array();

        // Oggetto.
        $subject = 'Situazione ore formative ai fini IVASS';

        foreach ($agData as $row) {
            // Non ci sono agenti nel raggruppamento, probabilmente
            // una agenzia senza agenti.
            if (!isset($row['users']['agenti'])) {
                $reports['noAgente'][] = $row;
                continue;
            }

            // Ciclo di composizione mail.
            foreach ($row['users']['agenti'] as $agente) {
                // Dati
                $name = "{$agente->nome} {$agente->cognome}";
                $message = $this->mailerManager->plainMessage();
                $message->setSubject($subject);

                $body = $this->getBody($row, $agente);

                // Message
                $message->setFrom(array('<EMAIL>'=>'ROBOT PortaleAgendo'))
                    ->setReplyTo('<EMAIL>')
                    ->setReturnPath('<EMAIL>')
                    ->setContentType('text/html')
                    //->setBcc(array('<EMAIL>' => 'debug'))
                    ->setBody('<html><headr></head><body><table border="0" width="562px" style="text-align:justify">
                            <tr><td>'.$body.'</td></tr>
                            <tr><td><small></small></td></tr>
                            </table></body></html>');

                if (! $config['forceDebug'] && $devCounter < $config['maxBccEmails']) {
                    $message->setBcc(array('<EMAIL>' => 'debug'));
                }

                try {
                    // Skip gli utenti con indirizzo vuoto.
                    if (empty($agente->email) && empty($agente->agzEmail)) {
                        $reports['empty'][] = $agente;
                        $this->log("{$agente->agenzia_id} {$agente->nome} {$agente->cognome} non ha un indirizzo valido.");
                        continue;
                    }

                    $this->log("Processing $agente->agenzia_id $agente->nome $agente->cognome");

                    if($this->mailerManager->isMailTest() || $config['forceDebug']){
                        if ($devCounter < $config['maxDebugEmails']) {
                            $this->mailerManager->sendMessagePlain($message, array($agente->email => $name), $config['forceDebug']);
                        }
                    }

                    else {
                        // Invio ad agente ed indirizzo agenzia.
                        if (!empty($agente->email)) {
                            $this->mailerManager->sendMessagePlain($message, array($agente->email => $name), $config['forceDebug']);
                        } elseif (!empty($agente->agzEmail)) {
                            $this->mailerManager->sendMessagePlain($message, array($agente->agzEmail => $name), $config['forceDebug']);
                        }
                    }

                    // Se non ci sono state eccezioni loggo il destinatario.
                    $reports['success'][] = $row;
                    $pretend[] = "Destinatario: {$agente->agenzia_id} $name {$agente->email} | CC: {$agente->agzEmail}";

                } catch (\Exception $ex) {
                    $reports['exception'][] = $agente;
                }

                $devCounter++;
            }
        }

        unset($agData);
        $this->report($reports);

        $this->log("END::send email");
    }

    public function fetchData($ore, $config)
    {
        $manager = new StudentManager();
        $manager->setTestMode($config['testMode']);
        $manager->setAgenzieBatch($config['testAgenzieData']);

        return $manager->doSearchByOre($ore);
    }

    protected function report($reports)
    {
        $this->log('Sintesi');
        $this->log('============================================================');
        $this->log("Record     :\t\t\t\t\t\t" . $reports['records']);
        $this->log("Inviati    :\t\t\t\t\t\t" . count($reports['success']));
        $this->log("Eccezioni  :\t\t\t\t\t\t" . count($reports['exception']));
        $this->log("Email vuote:\t\t\t\t\t\t" . count($reports['empty']));
        $this->log("No Agente  :\t\t\t\t\t\t" . count($reports['noAgente']));
        $this->log('');

        $this->log('Errori di invio');
        $this->log('============================================================');
        foreach ($reports['exception'] as $agente) {
            $this->log("EX: {$agente->agenzia_id} {$agente->uid} {$agente->email} {$agente->cognome} {$agente->nome}");
        }
        $this->log('');

        $this->log('Email vuote');
        $this->log('============================================================');
        foreach ($reports['empty'] as $agente) {
            $this->log("EMPTY: {$agente->agenzia_id} {$agente->uid} {$agente->email} {$agente->cognome} {$agente->nome}");
        }
        $this->log('');

        $this->log('No agente (INT in agenzie dove gli agenti sono active=0)');
        $this->log('============================================================');
        foreach ($reports['noAgente'] as $ex) {
            foreach ($ex['users']['altri'] as $user) {
                $this->log("ACTIVE0: {$user->agenzia_id} {$user->uid} {$user->email} {$user->cognome} {$user->nome}");
            }
        }

        $this->log('Mail inviate');
        $this->log('============================================================');
        foreach ($reports['success'] as $recipientsGroup){
            foreach($recipientsGroup['users']['agenti'] as $sent) {
                $this->log("SENT: {$sent->email}|{$sent->agzEmail}");
            }
        }
    }

    protected function getBody($data, $agente)
    {
        $templateList = "
            <li><strong>%d ore di formazione di AULA</strong></li>
            <li><strong>%d ore di formazione mediante i percorsi in e-learning</strong></li>
        ";

        $collabs = "";

        $tableStyle = "border='0' cellpadding='2'";
        $headerStyle = "style='background: #f3f3f3'";
        $footerStyle = "style='background: #f3f3f3'";
        $oddStyle = "style='background: #f3f3f3'";
        $evenStyle = "style='background: #ffffff'";


        if (!empty($data['users']['altri'])) {
            $collabs = "<table $tableStyle><tr $headerStyle><td>NOMINATIVO</td><td>DIREZIONE</td><td>AREA</td><td>E-LEARNING</td><td>CREDITI ACCUMULATI</td></tr>";
            $rowCounter = 0;
            foreach ($data['users']['altri'] as $collab) {
                $rowStyle = $rowCounter++%2 == 0 ? $evenStyle : $oddStyle;
                $collabs .= "<tr $rowStyle>";
                $collabs .= "<td>{$collab->cognome} {$collab->nome}</td>";
                $collabs .= "<td align='center'>$collab->sumOreFormativeDirez</td>";
                $collabs .= "<td align='center'>$collab->sumOreFormativeAree</td>";
                $collabs .= "<td align='center'>$collab->sumOreFormativeELearning</td>";
                $collabs .= "<td align='center'>$collab->sumOreFormative</td>";
                $collabs .= "</tr>";
            }
            $collabs .= "</table>";
        }

//        $endDt = (new \DateTime('first day of this month'))->sub(new \DateInterval('P1M'));
//        $endDate = $endDt->format('t F Y');
//        $endDt = (new \DateTime('today'));
//        $endDate = $endDt->format('d F Y');

        $today = date("Y-m-d");
        setlocale(LC_TIME, "it_IT.utf8");
        $endDate = ucwords(strftime("%d %B %Y", strtotime($today)));

        $body = "
            Gentile {$agente->nome} {$agente->cognome}, <br>
            le inviamo la situazione formativa relativa all'<strong>aggiornamento professionale</strong> suo e 
            dei suoi collaboratori e cogliamo l'occasione per porre l'attenzione sull'importanza della 
            <strong>formazione tecnica di prodotto.</strong>
            <br><br>
            <strong>Aggiornamento professionale</strong>
            <br><br>
            Le inviamo il report della situazione formativa relativa ai corsi sostenuti da lei e dai suoi collaboratori 
            nel periodo 1 Gennaio – ". $endDate .". Le ricordiamo che dal 2019 le nuove normativa IVASS prevedono 
            l’accumulo minino di 30 ore di crediti formativi su base annuale, senza più distinzione tra ore formative in 
            aula, ore formative da e-learning e webinar.
            <br><br>
            Nel dettaglio, la specifica dei crediti formativi accumulati nei corsi in presenza di aula (erogati dalla 
            Direzione o dall'Area di sua competenza) in modalità e-learning, o nel corso dei webinar
            <br><br>
            <table border='0' cellpadding='2'>
                <tr style='background: #f3f3f3'>
                    <td>TIPOLOGIA CORSO</td>
                    <td align='right'>CREDITI ACCUMULATI</td>
                </tr>
                <tr>
                    <td>Corsi da Direzione</td>
                    <td align='center'>$agente->sumOreFormativeDirez</td>
                </tr>
                <tr style='background: #f0f0f0'>
                    <td>Corsi da Area</td>
                    <td align='center'>$agente->sumOreFormativeAree</td>
                </tr>
                <tr>
                    <td>Corsi in e-learning/webinar</td>
                    <td align='center'>$agente->sumOreFormativeELearning</td>
                </tr>
                <tr style='background: #f0f0f0'>
                    <td>Corsi esterni (attestati)</td>
                    <td align='center'>$agente->sumOreFormativeAttestati</td>
                </tr>
                <tr>
                    <td>Corsi esterni autocertificati</td>
                    <td align='center'>$agente->sumOreFormativeEsterne</td>
                </tr>
                <tr style='background: #f3f3f3'>
                    <td align='right'>TOTALE</td>
                    <td align='center'>$agente->sumOreFormative</td>
                </tr>
            </table>

            <br><br>LEGENDA
            <ul>
                <li>
                    Corsi da Direzione: sono i corsi messi a disposizione sul Portale Agendo dalla Formazione Rete Di Vendita.
                </li>
                <li>
                    Corsi da Area: sono i corsi organizzati presso la sua Area Commerciale dal suo District Manager, dallo Specialista Vita e Finanza o da partner della Compagnia.
                </li>
                <li>
                    Corsi in e-learning: sono i corsi erogati attraverso la piattaforma di e-learning
                </li>
                <li>
                    Crediti accumulati: sono i crediti formativi complessivi dati dalla somma delle ore accreditate in tutti i corsi svolti con esito positivo
                </li>
                <li>
                    Corsi esterni (attestati): sono i corsi fruiti con realtà esterne a Groupama Assicurazioni per i quali è stato caricato il relativo attestato
                </li>
                <li>
                    Corsi esterni autocertificati: sono i corsi fruiti con realtà esterne a Groupama Assicurazioni per i quali è stato caricato un documento di autocertificazione complessivo
                </li>
            </ul>
            <br>
            <br>
            <table border='0' cellpadding='0'>
                <tr>
                    <td style='padding: 15px; font-weight: bold; border: 1px solid #000000; font-size: 12px'>
                        <span style='font-size: 14px'>Nota Bene:</span>
                        <br>
                        <ul>
                            <li>È possibile che si verifichi un ritardo nell’acquisizione dei corsi svolti nelle Aree, per cui il dato esposto potrebbe non essere aggiornato. I corsi svolti nelle Aree verranno a breve registrati e i crediti formativi aggiornati di conseguenza.</li>
                        </ul>
                    </td>
                </tr>
            </table>
            <br>
            <br>
            <strong>NON ha ancora completato l’aggiornamento professionale per il 2024?</strong>
            <br><br>
            Ottenere 30 crediti formativi per l’anno in corso è un requisito indispensabile per esercitare l’attività di intermediazione assicurativa. Utilizzi quest’ultima parte dell’anno per completare il suo aggiornamento professionale obbligatorio.
            <br>
            ";

        if (!empty($collabs)) {
            $body .= "<br>
                <strong>La formazione dei collaboratori</strong>
                <br><br>
                Con le stesse modalità, le riepiloghiamo quanto risulta dal sistema riguardo alla formazione attribuita ai suoi collaboratori:
                <br><br>
                $collabs
                ";
        }

        $body .= "<br>
                <strong>La formazione di prodotto</strong>
                <br><br>
                Le ricordiamo che la formazione di prodotto è necessaria per distribuire i prodotti della Compagnia a clienti o potenziali clienti. Tale formazione concorre comunque al raggiungimento del monte ore (30 ore) relativo all’aggiornamento professionale annuale.
                <br><br>
                <strong>La Compagnia, al fine di garantire un adeguato presidio della rete distributiva, in ottemperanza a quanto richiesto dalla normativa e dalla Vigilanza, monitora gli adempimenti formativi da parte degli intermediari Agenti ed effettua delle verifiche sia in materia di aggiornamento professionale che di formazione tecnica di prodotto.</strong>
                <br><br>
                <strong>Corsi Antiriciclaggio e GDPR</strong>
                <br><br>
                <p>Le ricordiamo che la formazione di prodotto è necessaria per distribuire i prodotti della Compagnia a clienti o potenziali clienti. Tale formazione concorre comunque al raggiungimento del monte ore (30 ore) relativo all’aggiornamento professionale annuale.</p>
                <p>
                    Desideriamo infine richiamare la sua attenzione, sulla fondamentale importanza della fruizione di due corsi che la normativa segnala come obbligatori per l’esercizio della professione:
                    <ul>
                        <li><u>Corso relativo alla normativa Antiriciclaggio</u></li>
                        <li><u>Corso relativo alla normativa GDPR</u></li>
                    </ul>
                    Le ore di formazione dedicate a questi due corsi sono considerate valide ai fini del conseguimento del numero minimo di ore di formazione e di aggiornamento professionale. Vi invitiamo pertanto, qualora non l’aveste ancora fatto, a seguire i corsi dedicati che trovate nella piattaforma e-learning. Qualora aveste svolto tali corsi con società/aziende esterne, vi preghiamo di inserire le informazioni sulla fruizione dei corsi negli appositi Form messi a vostra disposizione su Portale Agendo, in questo caso dovranno essere inserite unicamente le informazioni relative agli Agenti e non quelle concernenti i vostri collaboratori sia interni sia esterni all’agenzia.
                </p>
                <br>
        ";

        $body .= "<strong>
            Per qualsiasi chiarimento in merito a dati discordanti dalla situazione effettiva,
            la inviatiamo a rivolgersi al suo District Manager per le verifiche del caso.
            </strong>

            <p>Cordiali saluti,</p>
            <p>Direzione Distribuzione – Formazione Rete</p>
        ";

        return $body;
    }
}
