<?php

namespace batch;

use api\apps\incentive\rinnoviamoci\exceptions\ColumnsMismatchException;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\CoreTrait;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchTrait;
use PHPExcel_IOFactory;
use service\GroupamaUtils;

class LifeClients
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{

    const CSV_DATA_COLS = 21;
    const CSV_MU30_COLS = 21;

    use PdoTrait, BatchTrait, CoreTrait;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    protected $errors = [];

    /**
     * @batch(description="Import file clienti Vita")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importFileAction(Request $Req, Response $Res)
    {
        if (!$file = $this->getFile($this->getClientsPath())) {
            $this->l("File not found for path: clienti");
            return;
        }

        $this->pdoStExecute("START TRANSACTION");
        try {
            $this->pdoStExecute($this->getClientsInitStatement());
            $this->pdoStExecute("ALTER TABLE life_clients AUTO_INCREMENT = 1");
            $this->pdoStExecute("COMMIT");
        } catch (\Exception $e) {
            $this->pdoStExecute("ROLLBACK");
            throw $e;
        }

        // Insert statement.
        $statement = $this->pdoPrepare($this->getClientsStatement());

        $counter = 0;
        $limit = 500;

        $totalRows = 0;
        $successfulImports = 0;
        $failedImports = 0;
        $errors = [];

        foreach (file($file) as $lineNumber => $row) {
            $totalRows++;
            $lineNumber++; // Add 1 to get actual line number (as array is 0-based)

            /*if ($counter >= $limit) {
                $this->l("Reached limit of $limit rows. Stopping import.");
                break;
            }*/

            $row = explode("\t", $row);

            if ($columnCount = count($row) != 13) {
                $this->pushError($lineNumber, $row, null);
                $this->log("Skip row: bad column count: $columnCount");
                continue;
            }

            if ($row[0]) {
                $cfpiva = $row[0];
                //$this->log("Using cf[0]");
            } elseif ($row[1]) {
                $cfpiva = $row[1];
                //$this->log("Using cf[1]");
            } else {
                $this->pushError($lineNumber, $row, "cfpiva not found");
                //$this->log("Skip row: no cf/piva");
                continue;
            }

            if (! $this->validateCF($cfpiva)) {
                $this->pushError($lineNumber, $row, "invalid cfpiva");
                //$this->log("Skip row: invalid cfpiva");
                continue;
            }

            try {
                $statement->execute([
                    'CF_PIVA' => hash('sha256', $cfpiva),
                    'flag_tcm_ltc' => $row[2],
                    'flag_risparmio' => $row[3],
                    'flag_cliente_vita' => $row[4],
                    'flag_cliente_fpa' => $row[5],
                    'flag_auto' => $row[6],
                    'flag_persona' => $row[7],
                    'flag_casa_fam' => $row[8],
                    'flag_attivita' => $row[9],
                    'flag_altro' => $row[10],
                    'flag_risparmio_202306' => $row[11],
                    'flag_cli_ner_202307' => $row[12],
                ]);
                $successfulImports++;
            } catch (\Exception $ex) {
                $this->pushError($lineNumber, $row, $ex->getMessage());
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($row, true));
            }

            $counter++;

            if (\metadigit\core\ENVIRONMENT == 'PROD' && $counter % 100 == 0) {
                sleep(0.2);
            }

        }

        $failedImports = count($this->errors);

        $this->log("\nProcessing Summary:");
        $this->log("=====================================");
        $this->log("Total Rows in File:\t$totalRows");
        $this->log("Successful Imports:\t$successfulImports\t(" . round(($successfulImports / $totalRows) * 100, 2) . "%)");
        $this->log("Failed Imports:\t\t$failedImports\t(" . round(($failedImports / $totalRows) * 100, 2) . "%)");
        $this->log("=====================================");

        if (!empty($this->errors)) {
            $this->log("\nDetailed Error Report:");
            $this->log("=====================================");
            foreach ($this->errors as $index => $error) {
                $this->log("\nError #" . ($index + 1));
                $this->log("Line Number: " . $error['line']);
                $this->log("Error Type: " . $error['message']);
                $this->log("Row: " . $error['row']);
                $this->log("-------------------");
            }
        }
    }

    protected function validateCF($string)
    {
        if (! preg_match('/^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$/i', $string) &&
            ! preg_match('/^[0-9]{11}$/', $string)) {
            return false;
        }

        return true;
    }

    protected function pushError($lineNumber, $row, $message = null)
    {
        $this->errors[] = [
            'line' => $lineNumber,
            'message' => $message,
            'row' => implode("\t", $row),
        ];
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        if (! ($count = count($files))) {
            $this->l("File $filepath not found.");
            return null;
        }

        if ($count != 1) {
            $this->l("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    public function l($data)
    {
        if (is_array($data) || is_object($data)) {
            $data = print_r($data, true);
        }

        $this->log($data);
    }


    /**
     * @return string
     */
    protected function getClientsPath()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS PATH");
        return \metadigit\core\UPLOAD_DIR . "clienti_attivi_risparmio_CF_PIVA_202501_202505.txt";
    }

    /**
     * @return string
     */
    protected function getClientsInitStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS INIT STATEMENT");
        return "DELETE FROM life_clients";
    }

    /**
     * @return string
     */
    protected function getClientsStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS STATEMENT");
        return "
            INSERT INTO life_clients (
            CF_PIVA,
            flag_tcm_ltc,
            flag_risparmio,
            flag_cliente_vita,
            flag_cliente_fpa,
            flag_auto,
            flag_persona,
            flag_casa_fam,
            flag_attivita,
            flag_altro,
            flag_risparmio_202306,
            flag_cli_ner_202307
        ) values (
            :CF_PIVA,
            :flag_tcm_ltc,
            :flag_risparmio,
            :flag_cliente_vita,
            :flag_cliente_fpa,
            :flag_auto,
            :flag_persona,
            :flag_casa_fam,
            :flag_attivita,
            :flag_altro,
            :flag_risparmio_202306,
            :flag_cli_ner_202307
        )";
    }

}