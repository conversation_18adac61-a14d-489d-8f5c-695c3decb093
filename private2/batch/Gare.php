<?php
namespace batch;

use metadigit\core\cli\Request,
metadigit\core\cli\Response,
metadigit\lib\util\csv\CsvProcessor;

class Gare extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait;
	use \metadigit\lib\batch\BatchTrait;

	const PATTERN_STATICO = '/^INIZIATIVA_${ID}_STATICO_[0-9]{8}.(txt|TXT|csv)$/';
	const PATTERN_STATUS  = '/^INIZIATIVA_${ID}_STATUS_[0-9]{8}.(txt|TXT|csv)$/';

	const OLD_PATTERN_STATICO = '/^STATICO_GS_([0-9]{1,3})_([0-9]{8}).(txt|TXT|csv)$/';
	const OLD_PATTERN_STATUS  = '/^GS_([0-9]{1,3})_([0-9]{8}).(txt|TXT|csv)$/';

	protected $processedFiles = [];

	protected function preHandle(Request $Req, Response $Res) {
		$this->log('===== INIZIO BATCH =====================================================');
		return true;
	}

	protected function postHandle(Request $Req, Response $Res, $View = null) {
		$this->log('===== FINE BATCH =======================================================');
	}

	/**
	 * @batch(description="FIX: rinomina files & elimina doppioni")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function fixfilesAction(Request $Req, Response $Res) {
		$cache = [];
		foreach (scandir(UPLOAD_DIR) as $file) {
			if (preg_match(self::OLD_PATTERN_STATICO, $file, $matches)) {
				list($oldFile, $id, $data, $ext) = $matches;
				$newFile                         = 'INIZIATIVA_' . $id . '_STATICO_' . $data . '.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile . ' => ' . $newFile);
				rename(UPLOAD_DIR . $oldFile, UPLOAD_DIR . $newFile);
				$cache[$id]['STATICO'][] = $newFile;
			}
			if (preg_match(self::OLD_PATTERN_STATUS, $file, $matches)) {
				list($oldFile, $id, $data, $ext) = $matches;
				$newFile                         = 'INIZIATIVA_' . $id . '_STATUS_' . $data . '.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile . ' => ' . $newFile);
				rename(UPLOAD_DIR . $oldFile, UPLOAD_DIR . $newFile);
				$cache[$id]['STATUS'][] = $newFile;
			}
		}
		foreach ($cache as $id => $array) {
			foreach ($array as $type => $files) {
				array_pop($files);
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'DELETE doppioni ' . $id . ' ' . $type, print_r($files, true));
				foreach ($files as $file) {
					unlink(UPLOAD_DIR . $file);
				}
			}
		}
	}

	/**
	 * @batch(description="Gara 78 Speciale Vita 2014")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i078Action(Request $Req, Response $Res) {
		$this->processStaticCsv(78);
		$this->processStatusUpdateCsv(78);
		$this->backupFiles(78);
		$this->execStoredProcedure(78);
	} */

	/**
	 * @batch(description="Gara 79 Speciale Privati 2014")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i079Action(Request $Req, Response $Res) {
		$this->processStaticCsv(79);
		$this->processStatusUpdateCsv(79);
		$this->backupFiles(79);
		$this->execStoredProcedure(79);
	}*/

	/**
	 * @batch(description="Gara 80 Speciale AziendeAgricoli 2014")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i080Action(Request $Req, Response $Res) {
		$this->processStaticCsv(80);
		$this->processStatusUpdateCsv(80);
		$this->backupFiles(80);
		$this->execStoredProcedure(80);
	} */

	/**
	 * @batch(description="Gara 82 Globale 2014")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i082Action(Request $Req, Response $Res) {
		$this->processStaticCsv(82);
		$this->processStatusUpdateCsv(82);
		$this->backupFiles(82);
		$this->execStoredProcedure(82);
	} */

	/**
	 * @batch(description="Gara 98 Globale 2015")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i098Action(Request $Req, Response $Res) {
		$this->processStaticCsv(98);
		$this->processStatusUpdateCsv(98);
		$this->backupFiles(98);
		$this->execStoredProcedure(98);
	} */

	/**
	 * @batch(description="Gara 99 Speciale AziendeAgricoli 2015")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i099Action(Request $Req, Response $Res) {
		$this->processStaticCsv(99);
		$this->processStatusUpdateCsv(99);
		$this->backupFiles(99);
		$this->execStoredProcedure(99);
	} */

	/**
	 * @batch(description="Gara 100 Speciale Privati 2015")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i100Action(Request $Req, Response $Res) {
		$this->processStaticCsv(100);
		$this->processStatusUpdateCsv(100);
		$this->backupFiles(100);
		$this->execStoredProcedure(100);
	} */

	/**
	 * @batch(description="Gara 101 Speciale Vita 2015")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i101Action(Request $Req, Response $Res) {
		$this->processStaticCsv(101);
		$this->processStatusUpdateCsv(101);
		$this->backupFiles(101);
		$this->execStoredProcedure(101);
	} */

	/**
	 * @batch(description="Gara 129 Globale 2016")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i129Action(Request $Req, Response $Res) {
		$this->processStaticCsv(129);
		$this->processStatusUpdateCsv(129);
		$this->backupFiles(129);
		$this->execStoredProcedure(129);
	} */

	/**
	 * @batch(description="Gara 130 Speciale AziendeAgricoli 2016")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i130Action(Request $Req, Response $Res) {
		$this->processStaticCsv(130);
		$this->processStatusUpdateCsv(130);
		$this->backupFiles(130);
		$this->execStoredProcedure(130);
	} */

	/**
	 * @batch(description="Gara 131 Speciale Privati 2016")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i131Action(Request $Req, Response $Res) {
		$this->processStaticCsv(131);
		$this->processStatusUpdateCsv(131);
		$this->backupFiles(131);
		$this->execStoredProcedure(131);
	} */

	/**
	 * @batch(description="Gara 132 Speciale Vita 2016")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i132Action(Request $Req, Response $Res) {
		$this->processStaticCsv(132);
		$this->processStatusUpdateCsv(132);
		$this->backupFiles(132);
		$this->execStoredProcedure(132);
	} */

	/**
	 * @batch(description="Gara 160 - Risparmio e Protezione persona 2017")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i160Action(Request $Req, Response $Res) {
		$this->processStaticCsv(160);
		$this->processStatusUpdateCsv(160);
		$this->backupFiles(160);
		$this->execStoredProcedure(160);
	} */

	/**
	 * @batch(description="Gara 161 - Protezione beni 2017")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i161Action(Request $Req, Response $Res) {
		$this->processStaticCsv(161);
		$this->processStatusUpdateCsv(161);
		$this->backupFiles(161);
		$this->execStoredProcedure(161);
	} */

	/**
	 * @batch(description="Gara 162 - Globale 2017")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i162Action(Request $Req, Response $Res) {
		$this->processStaticCsv(162);
		$this->processStatusUpdateCsv(162);
		$this->backupFiles(162);
		$this->execStoredProcedure(162);
	} */

	/**
	 * @batch(description="Gara 193 - Globale 2018")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i193Action(Request $Req, Response $Res) {
		$this->processStaticCsv(193);
		$this->processStatusUpdateCsv(193);
		$this->backupFiles(193);
		$this->execStoredProcedure(193);
	} */

	/**
	 * @batch(description="Gara 194 - Protezione beni 2018")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i194Action(Request $Req, Response $Res) {
		$this->processStaticCsv(194);
		$this->processStatusUpdateCsv(194);
		$this->backupFiles(194);
		$this->execStoredProcedure(194);
	} */

	/**
	 * @batch(description="Gara 195 - Risparmio e Protezione persona 2018")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i195Action(Request $Req, Response $Res) {
		$this->processStaticCsv(195);
		$this->processStatusUpdateCsv(195);
		$this->backupFiles(195);
		$this->execStoredProcedure(195);
	} */

	/**
	 * @batch(description="Gara 216 - Protezione beni 2019")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i216Action(Request $Req, Response $Res) {
		$this->processStaticCsv(216);
		$this->processStatusUpdateCsv(216);
		$this->backupFiles(216);
		$this->execStoredProcedure(216);
	} */

	/**
	 * @batch(description="Gara 217 - Risparmio e Protezione persona 2019")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i217Action(Request $Req, Response $Res) {
		$this->processStaticCsv(217);
		$this->processStatusUpdateCsv(217);
		$this->backupFiles(217);
		$this->execStoredProcedure(217);
	} */

	/**
	 * @batch(description="Gara 218 - Globale 2019")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i218Action(Request $Req, Response $Res) {
		$this->processStaticCsv(218);
		$this->processStatusUpdateCsv(218);
		$this->backupFiles(218);
		$this->execStoredProcedure(218);
	} */

	/**
	 * @batch(description="Gara 228 - Protezione beni 2020")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i228Action(Request $Req, Response $Res) {
		$this->processStaticCsv(228);
		$this->processStatusUpdateCsv(228);
		$this->backupFiles(228);
		$this->execStoredProcedure(228);
	}*/

	/**
	 * @batch(description="Gara 229 - Risparmio e Protezione persona 2020")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i229Action(Request $Req, Response $Res) {
		$this->processStaticCsv(229);
		$this->processStatusUpdateCsv(229);
		$this->backupFiles(229);
		$this->execStoredProcedure(229);
	}*/

	/**
	 * @batch(description="Gara 230 - Globale 2020")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i230Action(Request $Req, Response $Res) {
		$this->processStaticCsv(230);
		$this->processStatusUpdateCsv(230);
		$this->backupFiles(230);
		$this->execStoredProcedure(230);
	}*/

	/**
	 * @batch(description="Gara 251 - Protezione beni 2021")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i251Action(Request $Req, Response $Res) {
		$this->processStaticCsv(251);
		$this->processStatusUpdateCsv(251);
		$this->backupFiles(251);
		$this->execStoredProcedure(251);
	}*/

	/**
	 * @batch(description="Gara 252 - Risparmio e Protezione persona 2021")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i252Action(Request $Req, Response $Res) {
		$this->processStaticCsv(252);
		$this->processStatusUpdateCsv(252);
		$this->backupFiles(252);
		$this->execStoredProcedure(252);
	}*/

	/**
	 * @batch(description="Gara 253 - Globale 2021")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i253Action(Request $Req, Response $Res) {
		$this->processStaticCsv(253);
		$this->processStatusUpdateCsv(253);
		$this->backupFiles(253);
		$this->execStoredProcedure(253);
	}*/

	/**
	 * @batch(description="Gara 278 - Protezione beni 2022")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/* function i278Action(Request $Req, Response $Res) {
		$this->processStaticCsv(278);
		$this->processStatusUpdateCsv(278);
		$this->backupFiles(278);
		$this->execStoredProcedure(278);
	}*/

	/**
	 * @batch(description="Gara 279 - Risparmio e Protezione persona 2022")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i279Action(Request $Req, Response $Res) {
		$this->processStaticCsv(279);
		$this->processStatusUpdateCsv(279);
		$this->backupFiles(279);
		$this->execStoredProcedure(279);
	}*/

	/**
	 * @batch(description="Gara 280 - Globale 2022")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i280Action(Request $Req, Response $Res) {
		$this->processStaticCsv(280);
		$this->processStatusUpdateCsv(280);
		$this->backupFiles(280);
		$this->execStoredProcedure(280);
	}*/

	/**
	 * @batch(description="Gara 301 - Protezione beni 2023")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i301Action(Request $Req, Response $Res) {
		$this->processStaticCsv(301);
		$this->processStatusUpdateCsv(301);
		$this->backupFiles(301);
		$this->execStoredProcedure(301);
	}

	/**
	 * @batch(description="Gara 302 - Risparmio e Protezione persona 2023")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i302Action(Request $Req, Response $Res) {
		$this->processStaticCsv(302);
		$this->processStatusUpdateCsv(302);
		$this->backupFiles(302);
		$this->execStoredProcedure(302);
	}

	/**
	 * @batch(description="Gara 303 - Globale 2023")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i303Action(Request $Req, Response $Res) {
		$this->processStaticCsv(303);
		$this->processStatusUpdateCsv(303);
		$this->backupFiles(303);
		$this->execStoredProcedure(303);
	}

	/**
	 * @batch(description="Gara 307 - Protezione beni 2024")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i307Action(Request $Req, Response $Res) {
		$this->processStaticCsv(307);
		$this->processStatusUpdateCsv(307);
		$this->backupFiles(307);
		$this->execStoredProcedure(307);
	}

	/**
	 * @batch(description="Gara 308 - Risparmio e Protezione persona 2024")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i308Action(Request $Req, Response $Res) {
		$this->processStaticCsv(308);
		$this->processStatusUpdateCsv(308);
		$this->backupFiles(308);
		$this->execStoredProcedure(308);
	}

	/**
	 * @batch(description="Gara 309 - Globale 2024")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i309Action(Request $Req, Response $Res) {
		$this->processStaticCsv(309);
		$this->processStatusUpdateCsv(309);
		$this->backupFiles(309);
		$this->execStoredProcedure(309);
	}

	/**
	 * @batch(description="Gara 334 - Protezione beni 2025")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i334Action(Request $Req, Response $Res) {
		$this->processStaticCsv(334);
		$this->processStatusUpdateCsv(334);
		$this->backupFiles(334);
		$this->execStoredProcedure(334);
	}

	/**
	 * @batch(description="Gara 335 - Risparmio e Protezione persona 2025")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i335Action(Request $Req, Response $Res) {
		$this->processStaticCsv(335);
		$this->processStatusUpdateCsv(335);
		$this->backupFiles(335);
		$this->execStoredProcedure(335);
	}

	/**
	 * @batch(description="Gara 336 - Globale 2025")
	 * @param Request $Req
	 * @param Response $Res
	 */
	public function i336Action(Request $Req, Response $Res) {
		$this->processStaticCsv(336);
		$this->processStatusUpdateCsv(336);
		$this->backupFiles(336);
		$this->execStoredProcedure(336);
	}

	private function processStaticCsv($id) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Setup tabella di status ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ',', 'csvEnclosure' => '']);
		$pattern      = str_replace('${ID}', str_pad($id, 3, '0', STR_PAD_LEFT), self::PATTERN_STATICO);
		if ($id >= 160) {
			$csvFields = 6;
			$pdoSt     = $this->pdoPrepare('CALL gare_' . $id . '_insert_obiettivi (?,?,?,?,?,?)');
		} else {
			$csvFields = 5;
			$pdoSt     = $this->pdoPrepare('CALL gare_' . $id . '_insert_obiettivi (?,?,?,?,?)');
		}
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute($values);
			return [true, null];
		};

		// process CSV STATICO_INIZIATIVA
		foreach (scandir(UPLOAD_DIR) as $file) {
			if (in_array($file, ['.', '..']) || !preg_match($pattern, $file)) {
				continue;
			}
			$this->processedFiles[] = $file;
			$this->log(chr(9) . 'CSV STATICO: ' . $file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . $file, $csvFields, $processFn);
			$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach ($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
			}
		}
	}

	private function processStatusUpdateCsv($id) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Import dei CSV ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ',', 'csvEnclosure' => '']);
		if ($id >= 160) {
			$csvFields = 7;
			$pdoSt     = $this->pdoPrepare('CALL gare_' . $id . '_insert_status (?,?,?,?,?,?,?)');
		} else {
			$csvFields = 5;
			$pdoSt     = $this->pdoPrepare('CALL gare_' . $id . '_insert_status (?,?,?,?,?)');
		}
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute($values);
			return [true, null];
		};

		// process CSV STATUS
		$pattern = str_replace('${ID}', str_pad($id, 3, '0', STR_PAD_LEFT), self::PATTERN_STATUS);
		foreach (scandir(UPLOAD_DIR) as $file) {
			if (in_array($file, ['.', '..']) || !preg_match($pattern, $file)) {
				continue;
			}
			$this->processedFiles[] = $file;
			$this->log(chr(9) . 'CSV: ' . $file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . $file, $csvFields, $processFn);
			$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach ($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
			}
		}
	}

	private function backupFiles($id) {
		if (empty($this->processedFiles)) {
			return;
		}
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$backupDirectory = \metadigit\core\BACKUP_DIR . 'CSV/Iniz' . str_pad($id, 3, '0', STR_PAD_LEFT) . '/' . date('Y-m-d');
		$this->log('Backup dei CSV in: BACKUP/CSV/Iniz' . str_pad($id, 3, '0', STR_PAD_LEFT) . '/' . date('Y-m-d'));
		if (!file_exists($backupDirectory)) {
			mkdir($backupDirectory, 0700, true);
		}
		foreach ($this->processedFiles as $file) {
			copy(UPLOAD_DIR . '/' . $file, $backupDirectory . '/' . $file);
		}
	}

	private function execStoredProcedure($id) {
		$this->log('Aggiornamento dati ...');
		$groupArray = $this->pdoQuery('SELECT DISTINCT gruppo FROM gare_' . $id . ' WHERE gruppo != "" ORDER BY gruppo')->fetchAll(\PDO::FETCH_COLUMN);
		$pdoSt      = $this->pdoPrepare('CALL gare_' . $id . '_set_classifica(:gruppo)');
		foreach ($groupArray as $group) {
			$pdoSt->execute(['gruppo' => $group]);
		}
		if (in_array('GA', $groupArray)) { // ONLY Gara Protezione e Gara Risparmio
			$this->pdoExec('UPDATE gare_' . $id . ' SET posizione = 0 WHERE gruppo = "GA"');
			$areeArray = $this->pdoQuery('SELECT id FROM geo_aree ORDER BY id')->fetchAll(\PDO::FETCH_COLUMN);
			$pdoSt     = $this->pdoPrepare('CALL gare_' . $id . '_set_classifica_area(:area)');
			foreach ($areeArray as $area) {
				$pdoSt->execute(['area' => $area]);
			}
		}
		$this->pdoStExecute('UPDATE gare SET dataUpdate = (SELECT @gara_' . $id . '_dataUpdate) WHERE id = ' . $id);
	}
}
