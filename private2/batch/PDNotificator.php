<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response;

class PDNotificator extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	/** GroupamaPDNotificator
	 * @var \service\GroupamaPDNotificator
	 */
	protected $GroupamaPDNotificator;

	/**
	 * @batch(description="GroupamaPDNotificator: AVANZAMENTI UPDATE")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function avanzamentiUpdateAction(Request $Req, Response $Res) {
		$this->GroupamaPDNotificator->publish('avanzamenti', 'update');
	}

	/**
	 * @batch(description="GroupamaPDNotificator: FORMAZ NUOVO CORSO")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function formazNuovoCorsoAction(Request $Req, Response $Res) {
		$this->GroupamaPDNotificator->publish('formaz', 'nuovo_corso');
	}

	/**
	 * @batch(description="GroupamaPDNotificator: FORMAZ INIZIO CORSO")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function formazInizioCorsoAction(Request $Req, Response $Res) {
		$this->GroupamaPDNotificator->publish('formaz', 'inizio_corso');
	}

	/**
	 * @batch(description="GroupamaPDNotificator: FORMAZ REPORT ORE")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function formazReportOreAction(Request $Req, Response $Res) {
		$this->GroupamaPDNotificator->publish('formaz', 'report_ore');
	}

	/**
	 * @batch(description="GroupamaPDNotificator: INCENTIV UPDATE")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function incentivazioniUpdateAction(Request $Req, Response $Res) {
		$this->GroupamaPDNotificator->publish('incentiv', 'update');
	}
}
