<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\core\db\Query,
	metadigit\core\db\orm\Metadata,
	metadigit\lib\util\csv\CsvProcessor;

class TassoZero extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const PATTERN_NEXUS = '/^TASSOZERO_NEXUS_[0-9]{8}.(txt|TXT|csv)$/';
	const PATTERN_COMPASS = '/^TASSOZERO_COMPASS_[0-9]{8}.(txt|TXT|csv)$/';
	const PATTERN_COMPASS_OLD = '/^TASSOZERO_COMPASS_OLD_[0-9]{8}.(txt|TXT|csv)$/';
	const PATTERN_FIDITALIA = '/^TASSOZERO_FIDITALIA_[0-9]{8}.(txt|TXT|csv)$/';

	/**
	 * @batch(description="TassoZero: import CSV")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function updateAction(Request $Req, Response $Res) {
		$this->importCsvCompassOLD();
		$this->importCsvCompass();
		$this->importCsvFiditalia();
		$this->importCsvNexus();
		$this->pdoExec('CALL tassozero_import()');
	}

	private function importCsvCompassOLD() {
		$this->log('== COMPASS ==============: ');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		// build Query
		$Metadata = Metadata::get('data\apps\tassozero\PolizzaCompass');
		$fields = implode(',',array_keys($Metadata->properties()));
		$Query = (new Query())->on('tassozero_compass', $fields);

		// CSV process function
		$csvFields = count($Metadata->properties()) + 3; // 5 UNUSED fields - 2 (POLIZZA & POLIZZA RAW) !!!
		$processFn = function($i, $line, $values) use ($Query) {
//			try {
			$Query->execInsertUpdate(
				[
					'polizza' => preg_replace('[\D]','',$values[6]),
					'polizza_raw' => preg_replace('[\W]','',$values[6]),
					'agenzia_id' => strtoupper($values[9]),
					'codiceFisc' => $values[19],
					'partitaIva' => '',
					'nome' => $values[18],
					'cognome' => $values[17],
					'dataFatturazione' => '',
					'dataRichiestaFinanz' => str_replace('/', '-', $values[5]),
					'dataConcessFinanz' => str_replace('/', '-', $values[16]),
					'codicePratica' => '',
					'codiceConvenzione' => $values[10],
					'durataMesi' => '',
					'capitale' => (float)$values[3],
					'commissioni' => '',
					'spese' => '',
					'tasso' => '',
					'costoTotFinanz' => (float)$values[4],
					// OLD 'causaleAcquisto' => $values[11],
					// OLD 'descrizAcquisto' => $values[12],
					// OLD 'filiale' => $values[22],
					// OLD 'ragioneSociale' => $values[13],
					// OLD 'agzPartitaIva' => $values[14],
					// OLD 'agzCodiceFisc' => $values[15],
					'dataStart' => str_replace('/', '-', $values[1]),    // data inizio estrazione Compass
					'dataEnd' => str_replace('/', '-', $values[2])        // data fine estrazione Compass
				],
				['id','polizza','polizza_raw']);
//			} catch(\Exception $Ex) {
//				$this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
//			}
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_compass')->fetchColumn();
		$this->log('Numero di Polizze COMPASS iniziali: '.$oldRecords);

		// process CSV NEXUS
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match(self::PATTERN_COMPASS_OLD, $file)) continue;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_compass')->fetchColumn();
		$this->log('Numero di Polizze COMPASS finali: '.$newRecords);
	}

	private function importCsvCompass() {
		$this->log('== COMPASS ==============: ');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		// build Query
		$Metadata = Metadata::get('data\apps\tassozero\PolizzaCompass');
		$fields = implode(',',array_keys($Metadata->properties()));
		$Query = (new Query())->on('tassozero_compass', $fields);

		// CSV process function
		$csvFields = count($Metadata->properties()) + 3; // 5 UNUSED fields - 2 (POLIZZA & POLIZZA RAW) !!!
		$processFn = function($i, $line, $values) use ($Query) {
//			try {
			$Query->execInsertUpdate(
				[
					'polizza' => preg_replace('[\D]','',$values[6]),
					'polizza_raw' => preg_replace('[\W]','',$values[6]),
					'agenzia_id' => strtoupper($values[7]),
					'codiceFisc' => $values[11],
					'partitaIva' => $values[12],
					'nome' => $values[13],
					'cognome' => $values[14],
					'dataFatturazione' => str_replace('/', '-', $values[3]),
					'dataRichiestaFinanz' => str_replace('/', '-', $values[4]),
					'dataConcessFinanz' => str_replace('/', '-', $values[5]),
					'codicePratica' => $values[8],
					'codiceConvenzione' => $values[9],
					'durataMesi' => $values[10],
					'capitale' => (float)$values[15],
					'commissioni' => (float)$values[16],
					'spese' => (float)$values[17],
					'tasso' => (float)$values[18],
					'costoTotFinanz' => (float)$values[19],
					'dataStart' => str_replace('/', '-', $values[1]),    // data inizio estrazione Compass
					'dataEnd' => str_replace('/', '-', $values[2])        // data fine estrazione Compass
				],
				['id','polizza','polizza_raw']);
//			} catch(\Exception $Ex) {
//				$this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
//			}
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_compass')->fetchColumn();
		$this->log('Numero di Polizze COMPASS iniziali: '.$oldRecords);

		// process CSV NEXUS
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match(self::PATTERN_COMPASS, $file)) continue;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_compass')->fetchColumn();
		$this->log('Numero di Polizze COMPASS finali: '.$newRecords);
	}

	private function importCsvFiditalia() {
		$this->log('== FIDITALIA ==============: ');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		// build Query
		$Metadata = Metadata::get('data\apps\tassozero\PolizzaFiditalia');
		$fields = implode(',',array_keys($Metadata->properties()));
		$Query = (new Query())->on('tassozero_fiditalia', $fields);

		// CSV process function
		$csvFields = count($Metadata->properties()) + 3; // 5 UNUSED fields - 2 (POLIZZA & POLIZZA RAW) !!!
		$processFn = function($i, $line, $values) use ($Query) {
//			try {
			$Query->execInsertUpdate(
				[
					'polizza' => preg_replace('[\D]','',$values[6]),
					'polizza_raw' => preg_replace('[\W]','',$values[6]),
					'agenzia_id' => strtoupper($values[7]),
					'codiceFisc' => $values[11],
					'partitaIva' => $values[12],
					'nome' => $values[13],
					'cognome' => $values[14],
					'dataFatturazione' => str_replace('/', '-', $values[3]),
					'dataRichiestaFinanz' => str_replace('/', '-', $values[4]),
					'dataConcessFinanz' => str_replace('/', '-', $values[5]),
					'codicePratica' => $values[8],
					'codiceConvenzione' => $values[9],
					'durataMesi' => $values[10],
					'capitale' => (float)$values[15],
					'commissioni' => (float)$values[16],
					'spese' => (float)$values[17],
					'tasso' => (float)$values[18],
					'costoTotFinanz' => (float)$values[19],
					'dataStart' => str_replace('/', '-', $values[1]),    // data inizio estrazione Fiditalia
					'dataEnd' => str_replace('/', '-', $values[2])        // data fine estrazione Fiditalia
				],
				['id','polizza','polizza_raw']);
//			} catch(\Exception $Ex) {
//				$this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
//			}
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_fiditalia')->fetchColumn();
		$this->log('Numero di Polizze FIDITALIA iniziali: '.$oldRecords);

		// process CSV NEXUS
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match(self::PATTERN_FIDITALIA, $file)) continue;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_fiditalia')->fetchColumn();
		$this->log('Numero di Polizze FIDITALIA finali: '.$newRecords);
	}

	private function importCsvNexus() {
		$this->log('== NEXUS ==============: ');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		// build Query
		$Metadata = Metadata::get('data\apps\tassozero\PolizzaNexus');
		$fields = implode(',',array_keys($Metadata->properties()));
		$Query = (new Query())->on('tassozero_nexus', $fields);

		// CSV process function
		$csvFields = count($Metadata->properties());
		$processFn = function($i, $line, $values) use ($Query) {
//			try {
				// SKIP codiceStatoOperaz
				if(in_array((int)$values[28], [7,8,9,10,163,329,347,375])) return [true, null];
				$Query->execInsertUpdate(
					[
						'polizza' => $values[3],
						'agenzia_id' => $values[4],
						'codiceFisc' => $values[5],
						'partitaIva' => $values[6],
						'tipo' => $values[7],
						'nome' => $values[8],
						'cognome' => $values[9],
						'premioRCA' => $values[10],
						'premioRCAprev' => $values[11],
						'premioTot' => $values[12],
						'flexSconto1' => (float)$values[13],
						'flexSconto8' => (float)$values[14],
						'flexSconto1prev' => (float)$values[15],
						'flexSconto8prev' => (float)$values[16],
						'codiceTitolo' => $values[17],
						'descrizTitolo' => $values[18],
						'codiceProdotto' => $values[19],
						'descrizprodotto' => $values[20],
						'dataEffettoContr' => str_replace('/', '-', $values[21]),
						'dataEffettoRinnovo' => str_replace('/', '-', $values[22]),
						'dataScadenzaRinnovo' => str_replace('/', '-', $values[23]),
						'dataScadenzaCopert' => str_replace('/', '-', $values[24]),
						'dataContabEmissione' => str_replace('/', '-', $values[25]),
						'dataContabIncasso' => str_replace('/', '-', $values[26]),
						'settoreANIA' => $values[27],
						'codiceStatoOperaz' => $values[28],
						'descrizStatoOperaz' => $values[29],
						'codicePolizzaSostit' => $values[30],
						'fattore1TATT0' => ($values[31] == 'SI') ? true : false,
						'fattore1RICT0' => ($values[32] == 'SI') ? true : false,
						'codiceProposta' => $values[33],
						'targa' => $values[34],
						'data' => str_replace('/', '-', $values[2]),        // data elaborazione Nexus
						'dataStart' => str_replace('/', '-', $values[0]),    // data inizio rilevazione Nexus
						'dataEnd' => str_replace('/', '-', $values[1])        // data fine rilevazione Nexus
					],
					['polizza']);
//			} catch(\Exception $Ex) {
//				$this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
//			}
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_nexus')->fetchColumn();
		$this->log('Numero di Polizze NEXUS iniziali: '.$oldRecords);

		// process CSV NEXUS
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match(self::PATTERN_NEXUS, $file)) continue;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM tassozero_nexus')->fetchColumn();
		$this->log('Numero di Polizze NEXUS finali: '.$newRecords);
	}
}
