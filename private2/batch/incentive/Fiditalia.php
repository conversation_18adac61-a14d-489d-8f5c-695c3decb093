<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
  metadigit\core\cli\Response;

class Fiditalia extends Incentive
{

  /**
   * @batch(description="Incentive: fiditalia import")
   * @param \metadigit\core\cli\Request $Req
   * @param \metadigit\core\cli\Response $Res
   * @throws \Exception
   */
  public function dataAction(Request $Req, Response $Res)
  {
    parent::dataAction($Req, $Res);
  }
}
