<?php namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Focus2022 extends Incentive {

    /**
     * @batch(description="Incentive: focus2022 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        parent::dataAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focus2022 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focus2022 clients")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function clientsAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile(\metadigit\core\UPLOAD_DIR . "FOCUS_281_CLIENTI_*.csv")) {
            $this->l("File not found for path: clienti");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute("delete from inc_focus_clients");

        // Insert statement.
        $statement = $this->pdoPrepare(
            "INSERT INTO inc_focus_clients (iniziativa_id, agenzia_id, codiceCliente, nominativo, partenza, arrivo)
            values(:iniziativa_id, :agenzia_id, :codiceCliente, :nominativo, :partenza, :arrivo)"
        );

        // Insert loop.
        $this->insert(
            $statement,
            file($file),
            281,
            false,
            ";",
            [$this->incentive, "parseClientsRow"]
        );

        $this->pdoCommit();
    }
}
