<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class FocusFase22023 extends Incentive
{

    protected $focus2023Fase2;

    /**
     * @batch(description="Incentive: focus2023 fase2 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        parent::dataAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focus2023 fase2 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focus2023 fase2 clients")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function clientsAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile(
            $this->getClientsPath()
        )
    ) {
            $this->l("File not found for path: clienti");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute($this->getClientsInitStatement());

        // Insert statement.
        $statement = $this->pdoPrepare($this->getClientsStatement());

        // Insert loop.
        $this->insert(
            $statement,
            file($file),
            306,
            false,
            ";",
            [$this->incentive, "parseClientsRow"]
        );

        $this->pdoCommit();
    }


    /**
     * @return string
     */
    protected function getClientsPath(){
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS PATH");
        return \metadigit\core\UPLOAD_DIR . "FOCUS_306_CLIENTI*.csv";
    }

    /**
     * @return string
     */
    protected function getClientsInitStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS INIT STATEMENT");
        return "DELETE FROM inc_focus2023_fase2_clients";
    }

    /**
     * @return string
     */
    protected function getClientsStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS STATEMENT");
        return "
            INSERT INTO inc_focus2023_fase2_clients (
            iniziativa_id,
            agenzia_id,
            codiceCliente,
            nominativo,
            partenza,
            arrivo,
            acquisito,
            conRelazioni
        ) values (
            :iniziativa_id,
            :agenzia_id,
            :codiceCliente,
            :nominativo,
            :partenza,
            :arrivo,
            :acquisito,
            :conRelazioni
        )";
    }
}
