<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class RaccoltaVincente2025 extends Incentive
{

    protected $raccoltaVincente2025;

    const PRODUCTS = [
        "MU18", "MU19", "MU20", "MU21", "MU22", "MU23", // Groupama Progetto Attivo
        "UL10", // Dimensione Quota
        "PU08", // Dimensione Silver
        "VC15", // Mente Serena Tutela
        "MU30", // Groupama Investimento Protetto 2030
        "PI03", // Programma Per Te
        "FPA" // Programma Open
    ];

    /**
     * @batch(description="Incentive: 4 - Raccolta Vincente 2025 import polizze")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {

        $this->pdoStExecute("delete from inc_raccolta_2025_data");
        $selectedRows = 0;
        $rowsRegistered = 0;
        $rowsDoubled = 0;
        $rowsError = 0;
        $errors = [];

        $statement = $this->pdoPrepare("INSERT INTO inc_raccolta_2025_data (
            agenzia_id,
            polizza,
            codiceProdotto,
            CF_PIVA,
            tipoPremioQuietanza,
            dataContabile,
            dataIncasso_check,
            premio,
            parziale
        ) VALUES (
            :agenzia_id,
            :polizza,
            :codiceProdotto,
            :CF_PIVA,
            :tipoPremioQuietanza,
            :dataContabile,
            :dataIncasso_check,
            :premio,
            :parziale
        )");

        $selectedRows = $this->pdoQuery("
            SELECT 
                agenzia_id,
                polizza,
                codiceProdotto,
                CF_PIVA,
                premio
            FROM life_data 
            WHERE dataContabile BETWEEN '2025-02-01' AND '2025-05-31' 
            AND codiceProdotto IN('" . implode("','", self::PRODUCTS) . "')
        ")->rowCount();

        $data = $this->pdoQuery("
            SELECT 
                agenzia_id,
                polizza,
                codiceProdotto,
                CF_PIVA,
                tipoPremioQuietanza,
                premio,
                dataContabile,
                dataIncasso_check
            FROM life_data 
            WHERE dataContabile BETWEEN '2025-02-01' AND '2025-05-31' 
            AND codiceProdotto IN('" . implode("','", self::PRODUCTS) . "')
        ")->fetchAll(\PDO::FETCH_ASSOC);



        foreach ($data as $row) {
            try {
                // Check if policy needs to be split
                $splitData = $this->checkSplitPolicy($row);

                if ($splitData) {
                    // Split policy into two records: one with premio04 and parziale '04', one with premio08 and parziale '08'

                    // First record with premio04 and parziale '04'
                    $statement->execute([
                        'agenzia_id' => $row['agenzia_id'],
                        'polizza' => $row['polizza'],
                        'codiceProdotto' => $row['codiceProdotto'],
                        'CF_PIVA' => $row['CF_PIVA'],
                        'premio' => $splitData['premio04'],
                        'tipoPremioQuietanza' => $row['tipoPremioQuietanza'],
                        'dataContabile' => $row['dataContabile'],
                        'dataIncasso_check' => $row['dataIncasso_check'],
                        'parziale' => '04'
                    ]);

                    // Second record with premio08 and parziale '08'
                    $statement->execute([
                        'agenzia_id' => $row['agenzia_id'],
                        'polizza' => $row['polizza'],
                        'codiceProdotto' => $row['codiceProdotto'],
                        'CF_PIVA' => $row['CF_PIVA'],
                        'premio' => $splitData['premio08'],
                        'tipoPremioQuietanza' => $row['tipoPremioQuietanza'],
                        'dataContabile' => $row['dataContabile'],
                        'dataIncasso_check' => $row['dataIncasso_check'],
                        'parziale' => '08'
                    ]);
                    $rowsDoubled++;

                } else {
                    // Regular policy registration without split
                    $statement->execute([
                        'agenzia_id' => $row['agenzia_id'],
                        'polizza' => $row['polizza'],
                        'codiceProdotto' => $row['codiceProdotto'],
                        'CF_PIVA' => $row['CF_PIVA'],
                        'premio' => $row['premio'],
                        'tipoPremioQuietanza' => $row['tipoPremioQuietanza'],
                        'dataContabile' => $row['dataContabile'],
                        'dataIncasso_check' => $row['dataIncasso_check'],
                        'parziale' => '-'
                    ]);
                    $rowsRegistered++;
                }
            } catch (\Exception $ex) {
                $rowsError++;
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($row, true));
                $errors[] = [
                    'error' => $ex->getMessage(),
                    'row' => $row
                ];
            }
        }

        $this->log("Righe totali: $selectedRows");
        $this->log("Righe registrate: $rowsRegistered");
        $this->log("Righe sdoppiate: $rowsDoubled");
        $this->log("Righe con errori: $rowsError");
        if ($errors) {
            $this->log("Errori:");
            foreach ($errors as $index => $error) {
                $this->log("Error " . ($index + 1) . ": " . $error['error']);
                $this->log("Row data: " . print_r($error['row'], true));
                $this->log("-------------------");
            }
        }

        $parsed = new \stdClass();
        $parsed->id = 331;
        $parsed->date = date('Y-m-d');
        $this->incentive->postInsert($parsed);

        // Timestamps.
        /*$this->pdoStExecute(
            "update incentive set lastUpdate = :lastUpdate, lastUpdateLabel = :lastUpdateLabel where id = :id",
            [
                ':lastUpdate' => date("Y-m-d H:i:s"),
                ':lastUpdateLabel' => $this->parsed->date,
                ':id' => $this->parsed->id,
            ]
        );*/

    }

    /**
     * @batch(description="Incentive: 0 - Raccolta Vincente 2025 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: 1 - Raccolta Vincente 2025 FPA")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function fpaAction(Request $Req, Response $Res)
    {

        if (! $file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "FPA_DEFINITIVO*.csv")) {
            $this->l("File not found for path: $path");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute("delete from inc_raccolta_2025_data_fpa");

        // Insert statement.
        $statement = $this->pdoPrepare("INSERT INTO inc_raccolta_2025_data_fpa (
            agenzia_id,
            numModuloAdesione,
            pezzi,
            premio
        ) VALUES (
            :agenzia_id,
            :numModuloAdesione,
            :pezzi,
            :premio
        )");

        $data = file($file);
        array_shift($data);

        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
        ];

        // Scan CSV file.
        foreach ($data as $row) {
            $line++;
            $row = trim($row);
            if ($row === '') continue; // skip empty lines

            // Parse CSV columns (assuming semicolon separator)
            $fields = str_getcsv($row, ";");

            // Skip policies with premio < 1200
            if ( $fields[1] < 1200 ) continue;

            // Check if all required fields are present
            if (!isset($fields[0], $fields[1], $fields[2], $fields[4])) {
                $result['KO']++;
                $this->l("Malformed row at line $line: $row");
                continue;
            }

            $agenzia_id = $fields[0];
            $numModuloAdesione = $fields[2];
            $pezzi = $fields[4];
            $premio = $fields[1];

            try {
                $statement->execute([
                    'agenzia_id' => $agenzia_id,
                    'numModuloAdesione' => $numModuloAdesione,
                    'pezzi' => $pezzi,
                    'premio' => $premio,
                ]);
                $result['OK']++;
            } catch (\Exception $ex) {
                $result['KO']++;
                $this->l("Error at line $line: " . $ex->getMessage());
                $this->l("Row data: " . print_r([
                        'agenzia_id' => $agenzia_id,
                        'numModuloAdesione' => $numModuloAdesione,
                        'pezzi' => $pezzi,
                        'premio' => $premio,
                    ], true));
            }
        }

        $this->log("TOT\t$line");

        foreach ($result as $key => $value) {
            $this->log("$key\t$value\t" . (($value/$line) * 100) . "%");
        }

        $this->pdoCommit();

    }

    /**
     * @batch(description="Incentive: 2 - Raccolta Vincente 2025 OTP")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function otpAction(Request $Req, Response $Res)
    {

        if (! $file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "DOC_IN_OTP*.csv")) {
            $this->l("File not found for path: $path");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute("delete from inc_raccolta_2025_data_otp");

        // Insert statement.
        $statement = $this->pdoPrepare("INSERT INTO inc_raccolta_2025_data_otp (
            polizza,
            codiceProdotto,
            dataDecorrenza
        ) VALUES (
            :polizza,
            :codiceProdotto,
            :dataDecorrenza
        )");

        $data = file($file);
        array_shift($data);

        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
        ];

        // Scan CSV file.
        foreach ($data as $row) {
            $line++;
            $row = trim($row);
            if ($row === '') continue; // skip empty lines

            // Parse CSV columns (assuming semicolon separator)
            $fields = str_getcsv($row, ";");

            // Check if all required fields are present
            if (!isset($fields[0], $fields[1], $fields[2])) {
                $result['KO']++;
                $this->l("Malformed row at line $line: $row");
                continue;
            }

            $polizza = $fields[0];
            // If polizza starts with 000, remove it
            if (strpos($polizza, '000') === 0) {
                $polizza = substr($polizza, 3);
            }

            $dataDecorrenza = $fields[1];
            // Convert dataDecorrenza to YYYY-MM-DD format
            $dateObj = \DateTime::createFromFormat('Ymd', $dataDecorrenza);
            if ($dateObj !== false) {
                $dataDecorrenza = $dateObj->format('Y-m-d');
            } else {
                // Handle invalid date format if needed
                $dataDecorrenza = null;
            }

            $codiceProdotto = $fields[2];

            try {
                $statement->execute([
                    'polizza' => $polizza,
                    'dataDecorrenza' => $dataDecorrenza,
                    'codiceProdotto' => $codiceProdotto,
                ]);
                $result['OK']++;
            } catch (\Exception $ex) {
                $result['KO']++;
                $this->l("Error at line $line: " . $ex->getMessage());
                $this->l("Row data: " . print_r([
                        'polizza' => $polizza,
                        'dataDecorrenza' => $dataDecorrenza,
                        'codiceProdotto' => $codiceProdotto,
                    ], true));
            }
        }

        $this->log("TOT\t$line");

        foreach ($result as $key => $value) {
            $this->log("$key\t$value\t" . (($value/$line) * 100) . "%");
        }

        $this->pdoCommit();

    }

    /**
     * @batch(description="Incentive: 3 - Raccolta Vincente 2025 esclusione e riduzioni")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function exclusionReductionAction(Request $Req, Response $Res)
    {

        if (! $file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "ESCLUSIONI_RIDUZIONI*.csv")) {
            $this->l("File not found for path: $path");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute("delete from inc_raccolta_2025_data_exclusion_reduction");

        // Insert statement.
        $statement = $this->pdoPrepare("INSERT INTO inc_raccolta_2025_data_exclusion_reduction (
            agenzia_id,
            tpDoc,
            polizza,
            codiceProdotto,
            premio04,
            premio08
        ) VALUES (
            :agenzia_id,
            :tpDoc,
            :polizza,
            :codiceProdotto,
            :premio04,
            :premio08
        )");

        $data = file($file);
        array_shift($data);

        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
        ];

        // Scan CSV file.
        foreach ($data as $row) {
            $line++;
            $row = trim($row);
            if ($row === '') continue; // skip empty lines

            // Parse CSV columns (assuming semicolon separator)
            $fields = str_getcsv($row, ";");

            // Check if all required fields are present
            if (!isset($fields[0], $fields[1], $fields[2], $fields[4], $fields[6], $fields[7])) {
                $result['KO']++;
                $this->l("Malformed row at line $line: $row");
                continue;
            }

            $tpDoc = $fields[0];
            $agenzia_id = $fields[1];
            $polizza = $fields[2];
            $codiceProdotto = $fields[4];
            $premio04 = $fields[6];
            $premio08 = $fields[7];

            try {
                $statement->execute([
                    'tpDoc' => $tpDoc,
                    'agenzia_id' => $agenzia_id,
                    'polizza' => $polizza,
                    'codiceProdotto' => $codiceProdotto,
                    'premio04' => $premio04,
                    'premio08' => $premio08,
                ]);
                $result['OK']++;
            } catch (\Exception $ex) {
                $result['KO']++;
                $this->l("Error at line $line: " . $ex->getMessage());
                $this->l("Row data: " . print_r([
                        'tpDoc' => $tpDoc,
                        'agenzia_id' => $agenzia_id,
                        'polizza' => $polizza,
                        'codiceProdotto' => $codiceProdotto,
                        'premio04' => $premio04,
                        'premio08' => $premio08,
                    ], true));
            }
        }

        $this->log("TOT\t$line");

        foreach ($result as $key => $value) {
            $this->log("$key\t$value\t" . (($value/$line) * 100) . "%");
        }

        $this->pdoCommit();

    }

    protected function checkSplitPolicy($policy)
    {
        $tpDoc = $this->getTPDoc($policy);

        $stmt = $this->pdoQuery("
            SELECT * FROM inc_raccolta_2025_data_exclusion_reduction
            WHERE polizza = '" . $policy["polizza"] . "'
            AND agenzia_id = '" . $policy["agenzia_id"] . "'
            AND codiceProdotto = '" . $policy["codiceProdotto"] . "'
              AND tpDoc = '" . $tpDoc . "'
              AND premio04 > 0 AND premio08 > 0
            LIMIT 1
        ");

        $row = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($row && isset($row['premio04']) && isset($row['premio08'])) {
            return [
                'premio04' => $row['premio04'],
                'premio08' => $row['premio08']
            ];
        }
        return false;
    }

    protected function getTPDoc($policy)
    {
        // Determine tpDoc value based on tipoPremioQuietanza
        if (isset($policy["tipoPremioQuietanza"])) {
            if ($policy["tipoPremioQuietanza"] === "Perfezionamento") {
                return 'POL';
            } elseif ($policy["tipoPremioQuietanza"] === "Versamenti aggiuntivo PUR/Q") {
                return 'VS';
            }
        }
        return null;
    }

}
