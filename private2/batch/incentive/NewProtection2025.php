<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class NewProtection2025 extends Incentive
{

    const PRODUCTS = ["000176", "000177"];

    /**
     * @batch(description="Incentive: New Protection 2025 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {

        $this->pdoStExecute("delete from inc_newpro_2025_data");
        $selectedRows = 0;
        $rowsRegistered = 0;
        $rowsError = 0;
        $errors = [];

        $statement = $this->pdoPrepare("INSERT INTO inc_newpro_2025_data (
            agenzia_id,
            polizza,
            codiceProdotto,
            premio
        ) VALUES (
            :agenzia_id,
            :polizza,
            :codiceProdotto,
            :premio
        )");

        // Query con i requisiti dell'incentivazione
        $query = "
            SELECT
                agenzia_id,
                polizza,
                codiceProdotto,
                premioNetto,
                premioAccessori
            FROM pc_data
            WHERE canale = 'AGENZIE GENERALI'
            AND codiceProdotto IN('" . implode("','", self::PRODUCTS) . "')
            AND tipoEsitoOperazione = 'INCASSATO'
            AND dataContabileEmissione BETWEEN '2025-06-01' AND '2025-12-31'
            AND frazionamento NOT IN('Temporaneo', 'Unico anticipato')
            AND flagCollettiva != 'S'
            AND numeroCampagna IN('N.D.','000059','000060','000061','000062')
            AND NOT (
                (codiceProdotto = '000176' AND codiceConvenzione IN ('001124', '002094'))
                OR
                (codiceProdotto = '000177' AND codiceConvenzione IN ('001124', '001125', '002090', '002116'))
            )
        ";

        $selectedRows = $this->pdoQuery($query)->rowCount();

        $data = $this->pdoQuery($query)->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($data as $row) {
            try {
                $statement->execute(
                    [
                        'agenzia_id' => $row['agenzia_id'],
                        'polizza' => $row['polizza'],
                        'codiceProdotto' => $row['codiceProdotto'],
                        'premio' => $row['premioNetto'] + $row['premioAccessori'],
                    ]
                );
                $rowsRegistered++;
            } catch (\Exception $ex) {
                $rowsError++;
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($row, true));
                $errors[] = [
                    'error' => $ex->getMessage(),
                    'row' => $row
                ];
            }
        }

        $this->log("Righe totali: $selectedRows");
        $this->log("Righe registrate: $rowsRegistered");
        $this->log("Righe con errori: $rowsError");
        if ($errors) {
            $this->log("Errori:");
            foreach ($errors as $index => $error) {
                $this->log("Error " . ($index + 1) . ": " . $error['error']);
                $this->log("Row data: " . print_r($error['row'], true));
                $this->log("-------------------");
            }
        }

        $parsed = new \stdClass();
        $parsed->id = 337;
        $parsed->date = date('Y-m-d');
        $this->incentive->postInsert($parsed);

        // Timestamps.
        /*$this->pdoStExecute(
            "update incentive set lastUpdate = :lastUpdate, lastUpdateLabel = :lastUpdateLabel where id = :id",
            [
                ':lastUpdate' => date("Y-m-d H:i:s"),
                ':lastUpdateLabel' => $this->parsed->date,
                ':id' => $this->parsed->id,
            ]
        );*/

    }

    /**
     * @batch(description="Incentive: New Protection 2025 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

}
