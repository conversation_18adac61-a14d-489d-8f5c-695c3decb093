<?php namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Compass2025 extends Incentive {

    /**
     * @batch(description="Incentive: compass 2025 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        parent::dataAction($Req, $Res);
    }
}
