<?php namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Supernova2024 extends Incentive {

    /**
     * @batch(description="Incentive: supernova import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        parent::dataAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: supernova static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

}