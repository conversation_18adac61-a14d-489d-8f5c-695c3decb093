<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class FocusFase22024 extends Incentive
{

    protected $focusFase22024;

    /**
     * @batch(description="Incentive: focusFase22024 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {
        parent::dataAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focusFase22024 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

    /**
     * @batch(description="Incentive: focusFase22024 clients")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function clientsAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile(
            $this->getClientsPath()
        )
    ) {
            $this->l("File not found for path: clienti");
            return;
        }

        $this->pdoBeginTransaction();

        // Init stage.
        $this->pdoStExecute($this->getClientsInitStatement());

        // Insert statement.
        $statement = $this->pdoPrepare($this->getClientsStatement());
        //$this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "STATEMENT", print_r(file($file), 1));

        // Insert loop.
        $this->insert(
            $statement,
            file($file),
            330,
            false,
            ";",
            [$this->incentive, "parseClientsRow"]
        );

        $this->pdoCommit();
    }


    /**
     * @return string
     */
    protected function getClientsPath(){
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS PATH");
        return \metadigit\core\UPLOAD_DIR . "FOCUS_330_CLIENTI*.csv";
    }

    /**
     * @return string
     */
    protected function getClientsInitStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS INIT STATEMENT");
        return "DELETE FROM inc_focus2024_fase2_clients";
    }

    /**
     * @return string
     */
    protected function getClientsStatement()
    {
        $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "GET CLIENTS STATEMENT");
        return "
            INSERT INTO inc_focus2024_fase2_clients (
            iniziativa_id,
            agenzia_id,
            codiceCliente,
            nominativo,
            partenza,
            arrivo,
            acquisito,
            conRelazioni,
            boostInfortuni,
            boostMalattia,
            boostMaxi
        ) values (
            :iniziativa_id,
            :agenzia_id,
            :codiceCliente,
            :nominativo,
            :partenza,
            :arrivo,
            :acquisito,
            :conRelazioni,
            :boostInfortuni,
            :boostMalattia,
            :boostMaxi
        )";
    }
}
