<?php

namespace batch;


use data\apps\formazione\Managers\MailerManager;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;
use metadigit\core\db\orm\Repository;
use metadigit\core\util\DateTime;

class Formazione
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{
    use \metadigit\core\db\PdoTrait;
    use \metadigit\lib\batch\BatchTrait;

    /**
     * @var MailerManager
     */
    protected $mailerManager;


    /**
     * Send Notices to Publication a Course
     *
     * @batch(description="Formazione: invio email di invito")
     * @throws \Exception
     */
    public function sendPublicationNoticesAction(Request $Req, Response $Res)
    {
        $this->sendNotices('publication');
    }

    /**
     * Send Notices to Registration a Course
     *
     * @batch(description="Formazione: invio email di registrazione")
     * @throws \Exception
     */
    public function sendRegistrationNoticesAction(Request $Req, Response $Res)
    {
        $this->sendRegistrationNotices();
    }

    /**
     * IMPORT DATA FROM OLD TABLES
     *
     * importLocationsTable
     * importCourses
     * importDirezClasses
     * importAreaClasses
     * importElearningClasses
     * importAttachmentsTable
     * importRecipients
     */

    /**
     * Import courses from old tables
     *
     * @batch(description="Formazione: import dei corsi dal vecchio sistema")
     */
    public function importCoursesAction(Request $Req, Response $Res)
    {
        $this->importFormazCorsoTable();
    }

    /**
     * Import classes from old tables
     *
     * @batch(description="Formazione: import delle classi direz dal vecchio sistema")
     */
    public function importDirezClassesAction(Request $Req, Response $Res)
    {
        // Direz classes
        $this->importFormazClassesDirezTable();
    }

    /**
     * Import classes from old tables
     *
     * @batch(description="Formazione: import delle classi area dal vecchio sistema")
     */
    public function importAreaClassesAction(Request $Req, Response $Res)
    {
        try {
            // Area classes
            $this->importFormazClassesAreaTable();

            $this->log("importFormazClassesAreaTable: success, import ok!");
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            $this->log($ex->getTraceAsString());
        }
    }

    /**
     * Import classes from old tables
     *
     * @batch(description="Formazione: import delle classi elearning dal vecchio sistema")
     */
    public function importElearningClassesAction(Request $Req, Response $Res)
    {
        try {
            // E-learning classes
            $this->importFormazClassesElearningTable();

            $this->log("importFormazClassesElearningTable: success, import ok!");
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            $this->log($ex->getTraceAsString());
        }
    }

    /**
     * Import FORStrutture table
     *
     * @batch(description="Formazione: import delle strutture dal vecchio sistema")
     */
    public function importLocationsTableAction(Request $Req, Response $Res)
    {
        $query     = "SELECT * FROM FORStrutture";
        $locations = $this->pdoQuery($query)->fetchAll();

        foreach ($locations as $location) {
            $id   = $location['id'];
            $name = addslashes($location['struttura']);
            $data = [
                'facilityName' => addslashes($location['struttura']),
                'address' => addslashes($location['indirizzo'] .", ". $location['civico'])
            ];
            $city = $location['localita'];

            if ($location['telefono1']) {
                $data['phone'] = $location['telefono1'];
            } elseif($location['telefono2']) {
                $data['phone'] = $location['telefono2'];
            }

            $data = json_encode($data);

            $query = "INSERT INTO tra_location(`id`, `name`, `data`, `city`)
                           VALUES ($id, '$name', '$data', '$city')";
            $this->pdoExec($query);
        }
    }

    /**
     * Import FORcorso2webfiles table
     *
     * @batch(description="Formazione: import dei files dal vecchio sistema")
     */
    public function importAttachmentsTableAction(Request $Req, Response $Res)
    {
        $filesDir    = "/storage/portaleagendo.it/data/downloads/apps/formazione/files/old/";

        $query       = "SELECT * FROM FORcorso2webfiles";
        $attachments = $this->pdoQuery($query)->fetchAll();

        if (!is_dir($filesDir)) mkdir($filesDir, 0755, true);

        foreach ($attachments as $attachment) {
            $query  = "SELECT * FROM FORCorsi WHERE id = ".$attachment['idcorso'];
            $course = $this->pdoQuery($query)->fetch();
            if ($course > 0) {
                $id       = $attachment['id'];
                $courseId = $course['corso_id'];
                $filename = addslashes($attachment['file']);
                $path     = addslashes($filesDir . $id . "." . $attachment['ext']);
                $private  = ($attachment['pubblico']) ? '0' : '1';

                $query = "INSERT INTO tra_file(`id`, `path`, `filename`, `course_id`, `private`)
                           VALUES ($id, '$path', '$filename', $courseId, $private)";
                $this->pdoExec($query);
            }
        }
    }

    /**
     * Import FORiscrizioni table
     *
     * @batch(description="Formazione: import delle iscrizioni dal vecchio sistema")
     */
    public function importRecipientsAction(Request $Req, Response $Res)
    {
        $query      = "SELECT fc.id AS courseId, i.iduser AS userId
                         FROM FORiscrizioni i
                              JOIN FORCorsi c ON c.id = i.idcorso
                              JOIN formaz_corso fc ON c.corso_id = fc.id
                        WHERE fc.`status` != 'ANN'";
        $recipients = $this->pdoQuery($query)->fetchAll();

        foreach ($recipients as $recipient) {
            $courseId = $recipient['courseId'];
            $userId   = $recipient['userId'];

            try {
                $query = "INSERT INTO tra_recipient(`user_id`, `course_id`)
                           VALUES ($userId, $courseId)";
                $this->pdoExec($query);
            } catch (\Exception $ex) {
                $this->log($query);
                $this->log($ex->getMessage());
                $this->log($ex->getTraceAsString());
            }
        }
    }

    /**
     * Import formaz_corso table
     */
    private function importFormazCorsoTable()
    {
        $query = "INSERT INTO tra_course(`id`, `title`, `type`, `credits`, `groupamaType`, `status`, `year`, `code`, `data`, `cover`, `filters`, `remoteId`, `tipo`, `erogato`, `modalita` )
                       SELECT c.`id`,
                              c.`titolo`,
                              CASE WHEN (c.`impl` = 'direz') THEN 'event' ELSE 'register' END,
                              c.`oreFormative`,
                              CASE WHEN (c.`impl` = 'direz') THEN 'direz'
                                   WHEN (c.`impl` = 'aree') THEN 'area'
                                   WHEN (c.`impl` = 'elearning') THEN 'e-learning'
                                   WHEN (c.`impl` = 'banche') THEN 'bank'
                              END,
                              LOWER(c.`status`),
                              c.`anno`,
                              c.`protocollo`,
                              null, null, null, null,
                              c.`tipo`,
                              c.`erogato`,
                              c.`modalita`
                         FROM formaz_corso AS c
                        WHERE c.`status` != 'ANN'";
        $this->pdoExec($query);
    }

    /**
     * Import FORCorsi table
     */
    private function importFormazClassesDirezTable()
    {
        $query   = "SELECT c.*, fc.`id` AS fcid, s.`id` AS struttura, s.`sede` AS sede, s.`giorno`, s.`scadenza`, s.`partecipanti_uff`
                      FROM FORCorsi c
                           JOIN formaz_corso fc ON c.corso_id = fc.id
                           JOIN FORSedi s ON s.corso = c.id
                     WHERE fc.`status` != 'ANN'
                     ORDER BY s.`giorno`";
        $classes = $this->pdoQuery($query)->fetchAll();

        foreach ($classes as $class) {
            $classId  = $class['id'];
            $courseId = $class["corso_id"];

            if (! $courseId) {
                $this->log("Manca course id a cui collegarlo. CLASS ID in FORCorsi = ".$classId);
                continue;
            }

            // Check if exist course with $courseId
            $query       = "SELECT count(1) FROM tra_course WHERE id = ".$courseId;
            $courseExist = (int) $this->pdoQuery($query)->fetchColumn(0);
            if ($courseExist <= 0) {
                $this->log("Manca course id a cui collegarlo. CLASS ID in FORCorsi = ".$classId);
                continue;
            }

            $locationId = $class['sede'];

            $this->log($classId.", ".$locationId);

            $days       = $this->getDatesClass($classId, $locationId);

            $this->log(json_encode($days));

            $status      = ($class['stato'] == 'on') ? 'active' : 'inactive';
            $seats       = $class['partecipanti_uff'];
            $data        = json_encode([
                'days' => $days
            ]);
            $code        = $class['protocolloFormazione'];
            $firstDay    = (new \DateTime($class['giorno']))->format("Y-m-d");
            $signupStart = (new \DateTime($class['scadenza']))->sub(new \DateInterval('P10D'))->format("Y-m-d");
            $signupEnd   = (new \DateTime($class['scadenza']))->format("Y-m-d");

            $query    = "INSERT INTO tra_class(`old_class_id`, `course_id`, `location_id`, `status`, `seats`, `trainer`, `data`, `code`, `firstDay`, `signupStart`, `signupEnd`, area, agency_id, sendMemo)
                              VALUES ('$classId', '$courseId', '$locationId', '$status', '$seats', null, '$data', '$code', '$firstDay', '$signupStart', '$signupEnd', null, null, 1)";
            $this->pdoExec($query);

            $newClassId = $this->pdoLastInsertId();

            // UPDATE COURSE TABLE

            $days            = $this->getDaysCourse($classId);
            $targets         = $this->getTargetsCourse($classId);
            $description     = $this->getDescriptionCourse($classId);
            $certificateArgs = $this->getCertificateArgsCourse($classId);

            $dataCourse = json_encode([
                'subtitle'              => "",
                'duration'              => $class['duratagiorni'],
                'description'           => $this->fixStrings($description),
                'certificateArgs'       => $this->fixStrings($certificateArgs),
                'targets'               => $this->fixStrings($targets),
                'schedule'              => $days,
                'agencyLimit' => $class['limiteAgenzia']
            ]);
            $credits = $class['oreformative'];
            $filters = [
                "isCsv" => ($class['csv'])
            ];

            if (! $class['csv'] && $class['intermediari']) : $filters["intermediariesRegistered"] = 1; endif;
            if (! $class['csv'] && $class['dipendenti']) : $filters["employeesRegistered"] = 1; endif;
            if (! $class['csv'] && $class['dipendenti_no_rui']) : $filters["employeesUnregistered"] = 1; endif;
            if (! $class['csv'] && $class['agenti']) : $filters["agents"] = 1; endif;

            $filters = json_encode($filters);

            $query = "UPDATE tra_course AS c SET c.`data` = '$dataCourse', c.`filters` = '$filters', c.`credits` = '$credits' WHERE c.`id` = " . $class['fcid'];
            $this->pdoExec($query);

            // IMPORT ATTENDEES
            $this->importFormazPartecipazioneDirezTable($classId, $class['struttura'], $newClassId, $class['fcid']);
        }
    }

    private function fixStrings($string)
    {
        $this->log("BEFORE FIX STRING: " . $string);

        $string = preg_replace("/[\n\r]/","", $string);
        $string = str_replace('"', '\"', $string);
        $string = str_replace("'", "\'", $string);

        $this->log("AFTER FIX STRING: " . $string);

        return $string;
    }

    private function getDatesClass($classId, $locationId = null)
    {
        $dates = null;
        $days  = [];

        if ($classId) {
            $query = "SELECT *
                    FROM FORSedi2date s JOIN FORSedi st ON s.sede = st.id
                         JOIN FORCorsi c ON c.id = s.corso
                   WHERE c.id = $classId";

            if ($locationId) {
                $query .= " AND st.sede = $locationId ORDER BY s.id";
            }

            $dates = $this->pdoQuery($query)->fetchAll();
        }

        if ($dates) {
            foreach ($dates as $date) {
                $days[] = [
                    'date' => (new \DateTime($date['giorno']))->format('Y-m-d'),
                    'startTime' => (new \DateTime($date['giorno']))->format('H:i'),
                    'endTime' => ""
                ];
            }
        }

        return $days;
    }

    private function getTargetsCourse($courseId)
    {
        $query = "SELECT obiettivi FROM FORObiettivi WHERE idcorso = $courseId";
        return $this->pdoQuery($query)->fetchColumn(0);
    }

    private function getDescriptionCourse($courseId)
    {
        $query = "SELECT descrizione FROM FORDescrizione WHERE idcorso = $courseId";
        return $this->pdoQuery($query)->fetchColumn(0);
    }

    private function getCertificateArgsCourse($courseId)
    {
        $query = "SELECT attestato FROM FORAttestato WHERE idcorso = $courseId";
        return $this->pdoQuery($query)->fetchColumn(0);
    }

    private function getDaysCourse($courseId)
    {
        $result = [];

        $query  = "SELECT `data` FROM FORSedi2date WHERE corso = $courseId";
        $days   = $this->pdoQuery($query)->fetchAll();

        foreach ($days as $day) {
            $date     = new \DateTime($day['data']);
            $result[] = [
                "startTime" => $date->format("H:i"),
                "endTime" => "",
                "description" => ""
            ];
        }

        return $result;
    }

    /**
     * Import formaz_corso_area table
     */
    private function importFormazClassesAreaTable()
    {
        $query   = "SELECT * FROM formaz_corso_area WHERE status != 'ANN'";
        $classes = $this->pdoQuery($query)->fetchAll();

        $this->log("Selected all formaz_corso_area rows except those with status = ANN. ROWS ".count($classes));

        $i = 1;

        foreach ($classes as $class) {
            $classId  = $class['id'];
            $courseId = $class['corso_id'];
            $area     = $class['area'];
            $credits  = $class['oreFormative'];

            $days = [
                [ 'date' => (new \DateTime($class['data']))->format('Y-m-d'), 'startTime' => '', 'endTime' => '']
            ];

            if (! is_null($class['dataSecondoGiorno'])) {
                $days[] = [
                    'date' => (new \DateTime($class['dataSecondoGiorno']))->format('Y-m-d'),
                    'startTime' => '',
                    'endTime' => ''
                ];
            }

            $data     = json_encode([
                'days' => $days,
                'location' => str_replace("'", "", $class['sede']),
            ]);
            $status   = ($class['status'] == 'ON') ? 'active' : 'inactive';
            $trainer  = addslashes($class['formatore']);
            $code     = substr($class['protocollo'], -2);
            $firstDay = (new \DateTime($class['data']))->format('Y-m-d');

            $query    = "INSERT INTO tra_class(`course_id`, `area`, `status`, `trainer`, `code`, `firstDay`, `data`, sendMemo, credits)
                              VALUES ($courseId, '$area', '$status', '$trainer', '$code', '$firstDay', '$data', 1, $credits)";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $this->log("Success, query executed");

            $newClassId = $this->pdoLastInsertId();

            try {
                $this->log("Import attendees for course $courseId, OLD class $classId, NEW class $newClassId");

                $this->importFormazPartecipazioneAreaTable($courseId, $classId, $newClassId);

                $this->log("Success, import executed");
            } catch (\Exception $ex) {
                $this->log($ex->getTraceAsString());
                $this->log("Attendee for course $courseId, OLD class $classId, NEW class $newClassId");
            }

            /*$this->log("Update course");

            $query = "UPDATE tra_course AS c SET c.`credits` = '$credits' WHERE c.`id` = " . $courseId;

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $this->log("Success, course updated");*/

            $this->log("FINISHED, COURSE $i");
            $i++;
        }
    }

    /**
     * Import formaz_corso_elearning table
     */
    private function importFormazClassesElearningTable()
    {
        $query   = "SELECT * FROM formaz_corso_elearning";
        $classes = $this->pdoQuery($query)->fetchAll();

        foreach ($classes as $class) {
            $remoteId = $class['idRemoto'];
            $courseId = $class['corso_id'];
            $status   = 'active';

            $query    = "INSERT INTO tra_class(`course_id`, `status`, sendMemo)
                              VALUES ($courseId, '$status', 1)";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $this->log("Success, query executed");

            $newClassId = $this->pdoLastInsertId();

            try {
                $this->log("Import attendees for course $courseId, NEW class $newClassId");

                $this->importFormazPartecipazioneElearningTable($courseId, $newClassId);

                $this->log("Success, import executed");
            } catch (\Exception $ex) {
                $this->log($ex->getTraceAsString());
                $this->log("Attendee for course $courseId, NEW class $newClassId");
            }

            $this->log("Update course remoteId");

            $query = "UPDATE tra_course AS c SET c.`remoteId` = '$remoteId' WHERE c.`id` = $courseId";
            $this->pdoExec($query);

            $this->log("Success, course updated");
        }
    }

    /**
     * Import FORiscrizioni table
     *
     * @param $classId
     * @param $locationId
     * @param $newClassId
     * @param $courseId
     * @throws \Exception
     */
    private function importFormazPartecipazioneDirezTable($classId, $locationId, $newClassId, $courseId)
    {
        $query     = "SELECT i.*, p.`corso_id`, p.`user_id`, p.`esito` AS pesito, p.`oreFormative` AS credits, u.`type`, c.oreformative, p.role
                        FROM FORiscrizioni i
                             LEFT JOIN formaz_partecipazione p ON p.`id` = i.`partecipazione_id`
                             JOIN users u ON i.iduser = u.id
                             JOIN FORCorsi c ON i.idcorso = c.id 
                        WHERE i.sede IS NOT NULL
                          AND ( (i.`iscritto` = '1' OR (i.iscritto = '0' AND queued = 1) ) OR i.iscritto = 'deleted')
                          AND i.idcorso = $classId
                          AND i.sede = $locationId";
        $attendees = $this->pdoQuery($query)->fetchAll();

        foreach ($attendees as $attendee) {
            if (! is_null($attendee['user_id'])) {
                $userId = $attendee['user_id'];
            } elseif (! is_null($attendee['iduser'])) {
                $userId = $attendee['iduser'];
            } else {
                $this->log("Manca lo userID");
                continue;
            }

            $state       = ($attendee['queued']) ? 'queued' : 'signedup';
            $result      = $attendee['esito'];
            $role        = $attendee['role'];
            $signedUpAt  = ($attendee['iscrittowhen']) ? $attendee['iscrittowhen'] : null;
            $bookedAt    = ($attendee['booked']) ? $attendee['booked'] : null;
            $completedAt = null;
            $queuedAt    = ($attendee['queuedTime']) ? $attendee['queuedTime'] : null;
            $createdAt   = ($attendee['created']) ? $attendee['created'] : (new \DateTime('now'))->format('Y-m-d H:i:s');
            $updatedAt   = ($attendee['updateat']) ? $attendee['updateat'] : (new \DateTime('now'))->format('Y-m-d H:i:s');

            if ($result == 'ok') {
                $credits = ((double) $attendee['credits']) ? $attendee['credits'] : $attendee['oreformative'];
            } else {
                $credits = 0;
            }

            $explode = explode(";", $attendee['presenze']);

            $presence = 0;
            foreach($explode as $ex) {
                $presence += (int) substr($ex, -1);
            }

            $data = json_encode([
                'presence' => $presence,
                'questionnaireDelivered' => (boolean) $attendee['questionario']
            ]);

            $this->log("$userId$classId, $credits, $state, $result, $role, $data");

            $query    = "INSERT INTO tra_attendance(`user_id`, `course_id`, `class_id`, `credits`, `createdAt`, `updatedAt`, `state`, `author_id`, `result`, `role`, `data`, `signedupAt`, `bookedAt`, `queuedAt`, `completedAt`)
                              VALUES('$userId', '$courseId', '$newClassId', '$credits', '$createdAt', '$updatedAt', '$state', null, '$result', '$role', '$data', '$signedUpAt', '$bookedAt', '$queuedAt', '$completedAt')";
            $this->pdoExec($query);
        }
    }

    /**
     * Import formaz_partecipazione_area table
     *
     * @param $courseId
     * @param $classId
     * @param $newClassId
     * @throws \Exception
     */
    private function importFormazPartecipazioneAreaTable($courseId, $classId, $newClassId)
    {
        $query     = "SELECT *
                        FROM formaz_partecipazione p
                             JOIN formaz_partecipazione_area pe ON p.`id` = pe.`partecipazione_id`
                             JOIN formaz_corso_area c ON c.id = pe.corso_area_id
                       WHERE c.id = $classId";

        $this->log("PART AREA: $query");

        $attendees = $this->pdoQuery($query)->fetchAll();

        foreach ($attendees as $attendee) {
            $useId       = $attendee['user_id'];
            $credits     = $attendee['oreFormative'];
            $state       = 'signedup';
            $result      = $attendee['esito'];
            $role        = $attendee['role'];
            $createdAt   = ($attendee['created']) ? $attendee['created'] : (new \DateTime('now'))->format('Y-m-d H:i:s');
            $updatedAt   = ($attendee['created']) ? $attendee['created'] : (new \DateTime('now'))->format('Y-m-d H:i:s');

            $query     = "INSERT INTO tra_attendance(`user_id`, `course_id`, `class_id`, `credits`, `createdAt`, `updatedAt`, `state`, `author_id`, `result`, `role`)
                               VALUES ('$useId', '$courseId', '$newClassId', '$credits', '$createdAt', '$updatedAt', '$state', null, '$result', '$role')";

            $this->log("Import attendee: " . $query);

            $this->pdoExec($query);

            $this->log("Success, attendee created");
        }
    }

    /**
     * Import formaz_partecipazione_elearning table
     *
     * @param $courseId
     * @param $classId
     * @throws \Exception
     */
    private function importFormazPartecipazioneElearningTable($courseId, $classId)
    {
        $query     = "SELECT *
                        FROM formaz_partecipazione p
                             JOIN formaz_partecipazione_elearning pe ON p.`id` = pe.`partecipazione_id`
                       WHERE corso_id = $courseId";
        $attendees = $this->pdoQuery($query)->fetchAll();

        $this->log("Selected all attendees rows from old database. ROWS ".count($attendees));

        $i = 1;

        foreach ($attendees as $attendee) {
            $useId         = $attendee['user_id'];
            $credits       = $attendee['oreFormative'];
            $state         = $attendee['esito'];
            $score         = $attendee['punteggio'];
            $completedDate = $attendee['dataCompletamento'];
            $role          = $attendee['role'];

            $query    = "INSERT INTO tra_attendance(`user_id`, `course_id`, `class_id`, `credits`, `createdAt`, `updatedAt`, `result`, `author_id`, `score`, `completedAt`, `state`, `role`)
                              VALUES('$useId', '$courseId', '$classId', '$credits', '$completedDate', '$completedDate', '$state', null, '$score', '$completedDate', 'signedup', '$role')";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $this->log("Success, attendance inserted");

            $this->log("FINISHED, COURSE $i");
            $i++;
        }
    }

    /**
     * Create notices for all classrooms
     *
     * @batch(description="Formazione: import notices")
     *
     * @param Request $Req
     * @param Response $Res
     * @throws \Exception
     */
    public function importNoticesAction(Request $Req, Response $Res)
    {
        $query   = "SELECT * FROM tra_course";
        $courses = $this->pdoQuery($query)->fetchAll();

        $this->log("Selected all courses. ROWS ".count($courses));

        $today = new DateTime('now');

        foreach ($courses as $course) {
            $query = "INSERT INTO tra_mail_notice(`course_id`, `subject`, `body`, `type`, `sendDate`, `step`, `sent`)
                           VALUES (".$course['id'].", '', '', 'publication', '".$today->format('y-m-d H:i:s')."', 1, 1)";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $query = "INSERT INTO tra_mail_notice(`course_id`, `subject`, `body`, `type`, `sendDate`, `step`, `sent`)
                           VALUES (".$course['id'].", '', '', 'registration', '".$today->format('y-m-d H:i:s')."', 1, 1)";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $query = "INSERT INTO tra_mail_notice(`course_id`, `subject`, `body`, `type`, `sendDate`, `step`, `sent`)
                           VALUES (".$course['id'].", '', '', 'memo', '".$today->format('y-m-d H:i:s')."', 1, 1)";

            $this->log("Execute query: $query");

            $this->pdoExec($query);

            $this->log("Success, notices inserted");

            $this->log("FINISHED, COURSE {$course['id']}");
        }
    }

    /**
     * Send Notices to Memo a Course
     *
     * @batch(description="Formazione: invio email di memo")
     * @throws \Exception
     */
    public function sendMemoNoticesAction(Request $Req, Response $Res)
    {
        $today = (new \DateTime('now'))->format('Y-m-d');

        // Notice required for the body message
        $query = "SELECT * FROM tra_mail_notice n WHERE n.type = 'memo' AND n.sent = 0";
        $notices = $this->pdoQuery($query)->fetchAll();

        foreach ($notices as $notice) {
            try {
                // Retrieve course's info and replace tags in body
                $query = "SELECT * FROM tra_course c WHERE c.id = ".$notice['course_id'];

                $this->log("Execute query: $query");

                $course = $this->pdoQuery($query)->fetch();

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);

                // Retrieve all ClassRooms to associate to a Course
                $query = "SELECT * FROM vw_tra_class c WHERE c.course_id = ".$notice['course_id']." AND c.sendMemo = 0";

                $this->log("Execute query: $query");

                $classRooms = $this->pdoQuery($query)->fetchAll();

                $countStarted = 0;  // Count started class

                foreach ($classRooms as $classRoom) {
                    if (!$classRoom['firstDay']) {
                        continue;
                    }

                    $this->log("Class ".$classRoom['id']);

                    $firstDay = \DateTime::createFromFormat('Y-m-d', $classRoom['firstDay']);
                    $firstDay->sub(new \DateInterval("P".$notice['step']."D"));
                    $firstDay = $firstDay->format('Y-m-d');

                    if ($today > $firstDay) {
                        // Already sent because class is began
                        $countStarted++;
                        continue;
                    }

                    if ($today !== $firstDay) {
                        continue;
                    }

                    // Replace classroom tags in body
                    $classroomBody = $this->mailerManager->replaceClassroomTags($body, $classRoom);

                    // Retrieve Recipients
                    $query = "SELECT r.*
                                FROM vw_tra_recipient_attendances r
                               WHERE r.class_id = ".$classRoom['id']."
                                 AND r.course_id = ".$notice['course_id']."
                                 AND r.state = 'signedup'";

                    $this->log("Execute query: $query");

                    $recipients = $this->pdoQuery($query)->fetchAll();

                    $this->log("BEGIN::SEND MAIL");
                    $this->mailerManager->sendToRecipients($recipients, $notice['subject'], $classroomBody);
                    $this->log("END::SEND MAIL");

                    // Update class
                    $query = "UPDATE tra_class c SET c.sendMemo = 1 WHERE c.id = ".$classRoom['id'];

                    $this->log("Execute query: $query");

                    $this->pdoExec($query);
                }

                // Sign notice sent Recipients for all classes
                $this->trace(LOG_DEBUG, 1, "COUNT", __FUNCTION__, "Count: " . count($classRooms));
                $this->trace(LOG_DEBUG, 1, "STARTED", __FUNCTION__, "$countStarted: " . $countStarted);

                // Set notice sent boolean
                if (count($classRooms) === 0) {
                    $query = "UPDATE tra_mail_notice n SET n.sent = 1 WHERE n.id = ".$notice['id'];

                    $this->log("Execute query: $query");

                    $this->pdoExec($query);
                }
            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
            }
        }
    }

    /**
     * Send Notices
     *
     * @param $type
     * @throws \Exception
     */
    private function sendNotices($type)
    {
        $today = (new \DateTime('now'))->format('Y-m-d');
        $query = "SELECT * FROM tra_mail_notice n WHERE n.type = '$type' AND n.sent = 0 AND sendDate = '$today'";
        $notices = $this->pdoQuery($query)->fetchAll();

        foreach ($notices as $notice) {
            try {
                // Retrieve course's info and replace tags in body.
                // Check if the course is active.
                $query = "SELECT c.* FROM tra_course c WHERE c.id = ".$notice['course_id']." AND c.status = 'on'";
                $course = $this->pdoQuery($query)->fetch();

                if (! $course) {
                    throw new \Exception("The course {$notice['course_id']} not exists or it is not active");
                }

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);

                // Retrieve Recipients
                $query = "SELECT * FROM vw_tra_recipient r WHERE r.course_id = ".$notice['course_id'];
                $recipients = $this->pdoQuery($query)->fetchAll();

                $this->mailerManager->sendToRecipients($recipients, $notice['subject'], $body);

                // Sign notice sent Recipients
                $query = "UPDATE tra_mail_notice n SET n.sent = 1 WHERE n.id = ".$notice['id'];
                $this->pdoExec($query);
            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
            }
        }
    }

    /**
     * Send Registration mail
     *
     * @throws \Exception
     */
    private function sendRegistrationNotices()
    {
        try {
            $query = "SELECT a.id, a.firstname, a.lastname, a.email, a.course_id, a.class_id, a.user_id
                        FROM vw_tra_attendance a
                   WHERE a.sentMailRegistration = 0
                     AND a.state = 'signedup'
                     AND a.firstDay > CURDATE()
                     AND a.courseStatus = 'on'";
            $attendances = $this->pdoQuery($query)->fetchAll();
        } catch (\Exception $ex) {
            $this->log($ex->getMessage());
            exit;
        }

        foreach ($attendances as $attendance) {
            try {
                $query = "SELECT * FROM tra_mail_notice n WHERE n.type = 'registration' AND n.course_id = ".$attendance['course_id'];
                $notice = $this->pdoQuery($query)->fetch();

                // Retrieve course's info and replace tags in body
                $query = "SELECT c.* FROM tra_course c WHERE c.id = ".$attendance['course_id'];
                $course = $this->pdoQuery($query)->fetch();

                // Retrieve classroom's info and replace tags in body
                $query = "SELECT c.* FROM vw_tra_class c WHERE c.id = ".$attendance['class_id'];
                $classRoom = $this->pdoQuery($query)->fetch();

                // Retrieve user's info and replace tags in body
                $query = "SELECT u.* FROM vw_tra_users u WHERE u.id = ".$attendance['user_id'];
                $user = $this->pdoQuery($query)->fetch();

                // Retrieve the user's agent
                $query = "SELECT u.* FROM vw_tra_users u WHERE u.agencyId = ".$user['agencyId']." AND u.type = 'AGENTE' AND u.email IS NOT NULL ORDER BY u.id LIMIT 1";
                $agent = $this->pdoQuery($query)->fetch();

                $body = $this->mailerManager->replaceCourseTags($notice['body'], $course);
                $body = $this->mailerManager->replaceClassroomTags($body, $classRoom);
                $body = $this->mailerManager->replaceUserTags($body, $user);

                // Send Mail to Agent of Attendance
                if ($this->mailerManager->send(
                    ($agent) ? $agent['email'] : null,
                    $attendance['firstname'] . ' ' . $attendance['lastname'],
                    $notice['subject'],
                    $body)) {
                    // Sign notice sent Recipients
                    $query = "UPDATE tra_attendance a SET a.sentMailRegistration = 1 WHERE a.id = ".$attendance['id'];
                    $this->pdoExec($query);
                }
            } catch (\Exception $ex) {
                $this->log($ex->getMessage());
                continue;
            }
        }
    }
}
