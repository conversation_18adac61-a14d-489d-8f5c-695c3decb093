<?php
namespace batch;
use const metadigit\core\BASE_DIR,
	metadigit\core\ENVIRONMENT,
	metadigit\core\TMP_DIR;
use	metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\core\context\Context,
	metadigit\core\db\orm\Repository,
	metadigit\core\util\html\HtmlWriter,
	util\csv\CsvProcessor,
	data\User,
	data\apps\reportistica\Batch;

class Reportistica extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const DEBUG_EMAIL = '<EMAIL>';
	const TMP_DIR = TMP_DIR.'app-reportistica/';

	/** AreeRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $AreeRepository;
	/** BatchesRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $BatchesRepository;
	/** DatiCommercialiRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $DatiCommercialiRepository;
	/** DatiIniziativeRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $DatiIniziativeRepository;
	/** DistrictsRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $DistrictsRepository;
	/** GareAreaRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $GareAreaRepository;
	/** Mailer
	 * @var \metadigit\core\mail\Mailer */
	protected $Mailer;
	/** UsersRepository
	 * @var \data\UsersRepository */
	protected $UsersRepository;

	/**
	 * @batch(description="Reportistica: dati commerciali")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @param integer $id DatoCommerciale ID
	 * @param string $users Users types
	 * @param string $aree
	 * @param string $options
	 * @param string $email
	 * @throws \metadigit\core\db\orm\Exception
	 * @throws \metadigit\lib\util\csv\Exception
	 */
	function daticommercialiAction(Request $Req, Response $Res, $id, $users, $aree, $options, $email) {
		$params['users'] = explode(',', $users);
		$params['aree'] = explode(',', $aree);
		$params['options'] = explode(',', $options);
		$params['email'] = explode(',', $email);
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'BATCH PARAMS', print_r($params, true));

		$Report = $this->DatiCommercialiRepository->update($id, ['lastSendDate'=>date('Y-m-d H:i:s')]);
		$Batch = $this->BatchesRepository->insert(NULL, ['type'=>'COMMERCIALI', 'id_dati'=>$id, 'nome'=>$Report->nome, 'status'=>'STARTING', 'mail'=>0]);


		// ====================================================================
		// load CSV -----------------------------------------------------------
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'CSV ANALYSIS');
		$files = scandir(Batch::DATI_COMMERCIALI_DIR.$id);
		$file = array_pop($files);
		$file = str_replace('.log','.csv',$file);
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		$csvData = $CsvProcessor->analyzeFile(Batch::DATI_COMMERCIALI_DIR.$id.'/'.$file);


		// ====================================================================
		// create SQL table ---------------------------------------------------
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'CREATE SQL TABLE');
		$this->pdoExec('DROP TABLE IF EXISTS app_reportistica_batch_'.$id);
		$sql = 'CREATE TABLE app_reportistica_batch_'.$id.' ( ';
		$sql .= 'id INTEGER UNSIGNED NOT NULL AUTO_INCREMENT, ';
		$sql .= 'agenzia_id CHAR(4) NOT NULL, ';
		foreach ($csvData['data'] as $k => $d) {
			if($k==0) continue; // SKIP agenzia_id
			switch ($d['type']) {
				case 'integer': $sql .= 'col'.$k.' INT UNSIGNED NOT NULL DEFAULT 0, '; break;
				case 'float': $sql .= 'col'.$k.' DECIMAL(11,2) UNSIGNED NOT NULL DEFAULT 0, '; break;
				case 'boolean': $sql .= 'col'.$k.' TINYINT(1) UNSIGNED NOT NULL DEFAULT 0, '; break;
				case 'date': $sql .= 'col'.$k.' DATE NULL DEFAULT NULL, '; break;
				case 'datetime': $sql .= 'col'.$k.' DATETIME NULL DEFAULT NULL, '; break;
				case 'string': $sql .= 'col'.$k.' VARCHAR(50) NULL DEFAULT NULL, '; break;
			}
		}
		$sql .= 'PRIMARY KEY (id), ';
		//$sql .= 'PRIMARY KEY (agenzia_id), '; // 2017-02-20 remove to allow multiple rows per agency
		$sql .= 'CONSTRAINT fk_app_reportistica_batch_'.$id.' FOREIGN KEY (agenzia_id) REFERENCES agenzie (id) ';
		$sql .= ') ENGINE=InnoDB DEFAULT CHARSET=utf8;';
		$this->pdoExec($sql);


		// ====================================================================
		// create SQL view ----------------------------------------------------
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'CREATE SQL VIEW');
		$this->pdoExec('DROP VIEW IF EXISTS vw_app_reportistica_batch_'.$id);
		$this->pdoExec('CREATE VIEW vw_app_reportistica_batch_'.$id.' AS SELECT
							b.*,
							ag.area		AS area_id,
							ag.district	AS district_id,
							geoa.nome	AS areaNome,
							geod.nome	AS districtNome
						FROM app_reportistica_batch_'.$id.' b
							LEFT JOIN agenzie ag ON b.agenzia_id = ag.id
							LEFT JOIN geo_aree geoa ON ag.area = geoa.id
							LEFT JOIN geo_districts geod ON ag.district = geod.id
						');
		$this->BatchesRepository->update($Batch->id, ['status'=>'RUNNING']);

		// ====================================================================
		// process CSV into SQL -----------------------------------------------
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'INSERT CSV DATA INTO DB');
		$this->log('CSV: '.$file);

		$sql = 'INSERT INTO app_reportistica_batch_'.$id.' ( agenzia_id, ';
		foreach ($csvData['data'] as $k => $d) {
			if($k==0) continue; // SKIP agenzia_id
			$sql .= 'col'.$k.', ';
		}
		$sql = substr($sql, 0, -2);
		$sql .= ' ) VALUES ( :agenzia_id, ';
		foreach ($csvData['data'] as $k => $d) {
			if($k==0) continue; // SKIP agenzia_id
			$sql .= ':col'.$k.', ';
		}
		$sql = substr($sql, 0, -2);
		$sql .= ' )';
		$pdoSt = $this->pdoPrepare($sql);
		unset($sql);
		$processFn = function($i, $line, $values) use ($pdoSt, $csvData) {
			$params = ['agenzia_id'=>$values[0]];
			foreach ($csvData['data'] as $k => $d) {
				if($k==0) continue; // SKIP agenzia_id
				$params['col'.$k] = $values[$k];
			}
			$pdoSt->execute($params);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(Batch::DATI_COMMERCIALI_DIR.$id.'/'.$file, $csvData['columns'], $processFn);
		$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
		}


		// ====================================================================
		// HTML Writer function
		if(!is_dir(self::TMP_DIR)) mkdir(self::TMP_DIR, 0755, true);
		$xlsWriteFn = function($data) use ($id, $csvData) {
			$limit = ini_set('memory_limit', '512M');
			$HtmlWriter = new HtmlWriter();
			$columns = [
				['Area',		'areaNome'],
				['District',	'districtNome'],
				['Agenzia',		'agenzia_id']
			];
			foreach ($csvData['data'] as $k => $d) {
				if($k==0) continue; // SKIP agenzia_id
				// ABORTED on 2016-11-03: not working on Windows // if($d['type'] == 'float') $ExcelWriter->addColumn($d['label'], 'col'.$k, function($v) { return number_format($v,2,',','.'); });
				$columns[] = [$d['label'], 'col'.$k];
			}
			$HtmlWriter->setData(['DATA'=>$data, 'COLUMNS'=>$columns])->setTemplate(BASE_DIR.'api/apps/reportistica/tpl/dati-commerciali.phtml');
			$HtmlWriter->write(self::TMP_DIR.'REPORT-COMMERC-'.$id.'.html');
			ini_set('memory_limit', $limit);
		};


		// ====================================================================
		// Mail function
		$mailCounter = 0;
		$mailFn = function($email, $name='') use ($id, $Report, &$mailCounter, $params) {
			if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
				$this->log('- '.$email.' EMAIL NON VALIDA');
				return;
			}
			$Message = $this->Mailer->newMessage()
				->setSubject($Report->mailSubject)
				->setBody($Report->mailBody)
				->setFrom(['<EMAIL>'=>'Nucleo di supporto'])
				->attach(\Swift_Attachment::fromPath(self::TMP_DIR.'REPORT-COMMERC-'.$id.'.html', 'text/html')->setFilename('REPORT.html'));
			if(ENVIRONMENT != 'PROD' && !in_array('PREVIEW', $params['options']))
				$Message->setTo([self::DEBUG_EMAIL=>$name]);
			else
				$Message->setTo([$email=>$name]);
			if($this->Mailer->send($Message)) $mailCounter++;
		};


		// ====================================================================
		// FETCH SQL ----------------------------------------------------------
		$fetchSQL = 'SELECT * FROM vw_app_reportistica_batch_'.$id;


		// ====================================================================
		// DIREZIONE ----------------------------------------------------------
		if(in_array('AMMINISTRATORE', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO DIREZIONE');
			$this->log('MAILING UTENTI DIREZIONE');
			$data = $this->pdoQuery($fetchSQL)->fetchAll(\PDO::FETCH_ASSOC);
			$xlsWriteFn($data);
			if(in_array('PREVIEW', $params['options'])) {
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AMMINISTRATORE');
				foreach ($users as $User) {
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}

		// ====================================================================
		// AREA MANAGERS ------------------------------------------------------
		if(in_array('AREAMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO AREA MANAGERS');
			$this->log('MAILING UTENTI AREA MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				$sql = !in_array('CROSSAREE', $params['options']) ? $fetchSQL.' WHERE area_id = '.$User->area : $fetchSQL;
				$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
				$xlsWriteFn($data);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$sql = !in_array('CROSSAREE', $params['options']) ? $fetchSQL.' WHERE area_id = '.$User->area : $fetchSQL;
					$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
					$xlsWriteFn($data);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}

		// ====================================================================
		// DISTRICT MANAGERS --------------------------------------------------
		if(in_array('DISTRICTMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO DISTRICT MANAGERS');
			$this->log('MAILING UTENTI DISTRICT MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				$sql = !in_array('CROSSDISTRICTS', $params['options']) ? $fetchSQL.' WHERE area_id = '.$User->area.' AND district_id = '.$User->district : $fetchSQL;
				$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
				$xlsWriteFn($data);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$sql = !in_array('CROSSDISTRICTS', $params['options']) ? $fetchSQL.' WHERE area_id = '.$User->area.' AND district_id = '.$User->district : $fetchSQL;
					$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
					$xlsWriteFn($data);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}


		// ====================================================================
		// LOG ----------------------------------------------------------------

		if(!is_dir(Batch::LOG_DIR)) mkdir(Batch::LOG_DIR, 0755, true);
		file_put_contents(Batch::LOG_DIR.$Batch->id.'.log', $this->getLog());

		preg_match('/^([0-9]{4})-([0-9]{2})-([0-9]{2})_([0-9]{2})([0-9]{2}).csv$/', $file, $matches);
		list($_, $Y, $m, $d, $H, $i) = $matches;
		$this->BatchesRepository->update($Batch->id, [
			'status'=>'COMPLETED',
			'fileDate'=>date('Y-m-d H:i:s', mktime($H, $i, 0, $m, $d, $Y)),
			'mail'=>$mailCounter
		]);

		// ====================================================================
		// CLEAN --------------------------------------------------------------

		$this->pdoExec('DROP VIEW IF EXISTS vw_app_reportistica_batch_'.$id);
		$this->pdoExec('DROP TABLE IF EXISTS app_reportistica_batch_'.$id);
		unlink(self::TMP_DIR.'REPORT-COMMERC-'.$id.'.html');
	}

	/**
	 * @batch(description="Reportistica: dati incentivazioni")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @param integer $id DatoIniziativa ID
	 * @param string $users Users types
	 * @param string $aree
	 * @param string $options
	 * @param string $email
	 * @throws \metadigit\core\context\ContextException
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function datiincentivazioniAction(Request $Req, Response $Res, $id, $users, $aree, $options, $email) {
		$params['users'] = explode(',', $users);
		$params['aree'] = explode(',', $aree);
		$params['options'] = explode(',', $options);
		$params['email'] = explode(',', $email);
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, '### BATCH PARAMS', print_r($params, true));

		$Report = $this->DatiIniziativeRepository->update($id, ['lastSendDate' => date('Y-m-d H:i:s')]);
		$Batch = $this->BatchesRepository->insert(NULL, ['type' => 'INIZIATIVE', 'id_iniz' => $id, 'nome' => $Report->nome, 'status' => 'STARTING', 'mail' => 0]);


		// ====================================================================
		// HTML Writer function
		if(!is_dir(self::TMP_DIR)) mkdir(self::TMP_DIR, 0755, true);
		$xlsWriteFn = function($data) use ($id) {
			$limit = ini_set('memory_limit', '512M');
			$HtmlWriter = new HtmlWriter();
			$columns = [];
			include BASE_DIR.'api/iniziative/tpl/'.$id.'/all.xls.php';
			$HtmlWriter->setData(['DATA'=>$data, 'COLUMNS'=>$columns])->setTemplate(BASE_DIR.'api/apps/reportistica/tpl/dati-incentivazioni.phtml');
			$HtmlWriter->write(self::TMP_DIR.'REPORT-INIZ-'.$id.'.html');
			ini_set('memory_limit', $limit);
		};


		// ====================================================================
		// Mail function
		$mailCounter = 0;
		$mailFn = function($email, $name='', $stats) use ($id, $Report, &$mailCounter, $params) {
			if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
				$this->log('- '.$email.' EMAIL NON VALIDA');
				return;
			}
			$bodyData = <<<BODY

Pezzi: %d
Agenzie produttive: %d/%d (%d%%)
Agenzie OBJ: %d/%d (%d%%)
Totale premi computabili: € %s
Totale importo erogabile: € %s
BODY;
			$bodyData = sprintf($bodyData, $stats['pezzi'], $stats['agzProd'], $stats['agzTot'], $stats['agzProdPerc'], $stats['agzObj'], $stats['agzProd'], $stats['agzObjPerc'], $stats['premi'], $stats['importo']);
			$Message = $this->Mailer->newMessage()
				->setSubject($Report->mailSubject)
				->setBody($Report->mailBody.EOL.$bodyData)
				->setFrom(['<EMAIL>'=>'Nucleo di supporto'])
				->attach(\Swift_Attachment::fromPath(self::TMP_DIR.'REPORT-INIZ-'.$id.'.html', 'text/html')->setFilename('REPORT.html'));
			if(ENVIRONMENT != 'PROD' && !in_array('PREVIEW', $params['options']))
				$Message->setTo([self::DEBUG_EMAIL=>$name]);
			else
				$Message->setTo([$email=>$name]);
			if($this->Mailer->send($Message)) $mailCounter++;
		};

		// ====================================================================
		// FETCH DATA ---------------------------------------------------------

		$repositoryID = 'data.iniz.I'.$id.'StatusRepository';
		$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'ORM Repository: '.$repositoryID);
		$Repository = Context::factory('data.iniz')->get($repositoryID);

		$this->trace(LOG_DEBUG, 1, __FUNCTION__, '### FETCH GEO DATA');
		$_SESSION['AUTH']['UTYPE'] = 'AMMINISTRATORE';
		$_SESSION['AUTH']['AREA'] = null;
		$_SESSION['AUTH']['DISTRICT'] = null;
		$data = $this->AreeRepository->fetchAll(1, null, null, null, Repository::FETCH_ARRAY);
		$geoAree = [];
		foreach($data as $d) {
			$geoAree[$d['id']] = $d['nome'];
		}
		$data = $this->UsersRepository->fetchAll(1, null, null, 'active,EQ,1|type,EQ,AREAMGR', Repository::FETCH_ARRAY);
		$geoAreaMgrs = [];
		foreach($data as $d) {
			$geoAreaMgrs[$d['area']] = $d['cognome'].' '.$d['nome'];
		}
		$data = $this->DistrictsRepository->fetchAll(1, null, null, null,  Repository::FETCH_ARRAY);
		$geoDistricts = [];
		foreach($data as $d) {
			$geoDistricts[$d['id']] = $d['nome'];
		}

		$injectFn = function($data) use ($geoAree, $geoAreaMgrs, $geoDistricts) {
			foreach($data as $k=>$item) {
					$data[$k]['areaName'] = (isset($geoAree[$data[$k]['area']])) ? $geoAree[$data[$k]['area']] : null;
					$data[$k]['areaManager'] = (isset($geoAreaMgrs[$data[$k]['area']])) ? $geoAreaMgrs[$data[$k]['area']] : null;
					$data[$k]['districtManager'] = (isset($geoDistricts[$data[$k]['district']])) ? $geoDistricts[$data[$k]['district']] : null;
			}
			return $data;
		};

		$fetchStatsFn = function(User $User) use ($id) {
			switch ($User->type) {
				case 'AMMINISTRATORE':	$criteriaSql = '1 = 1'; break;
				case 'AREAMGR':			$criteriaSql = 'area = '.$User->area; break;
				case 'DISTRICTMGR':		$criteriaSql = 'area = '.$User->area.' AND district = '.$User->district; break;
			}
			$stats = [];
			list($stats['pezzi'], $stats['premi'], $stats['importo']) = $this->pdoQuery('SELECT SUM(pezziTOTALI), SUM(premiCompTOTALI), SUM(importoErogTOTALE) FROM vw_iniz_'.$id.' WHERE '.$criteriaSql)->fetch(\PDO::FETCH_NUM);
			$stats['premi'] = number_format($stats['premi'],2,',','.');
			$stats['importo'] = number_format($stats['importo'],2,',','.');
			list($stats['agzTot']) = $this->pdoQuery('SELECT COUNT(*) FROM vw_iniz_'.$id.' WHERE '.$criteriaSql)->fetch(\PDO::FETCH_NUM);
			list($stats['agzProd']) = $this->pdoQuery('SELECT COUNT(*) FROM vw_iniz_'.$id.' WHERE '.$criteriaSql.' AND pezziTOTALI > 0')->fetch(\PDO::FETCH_NUM);
			list($stats['agzObj']) = $this->pdoQuery('SELECT COUNT(*) FROM vw_iniz_'.$id.' WHERE '.$criteriaSql.' AND obiettivoOK = 1')->fetch(\PDO::FETCH_NUM);
			$stats['agzProdPerc'] = ($stats['agzTot']) ? (int)($stats['agzProd'] / $stats['agzTot'] * 100) : 0;
			$stats['agzObjPerc'] = ($stats['agzProd']) ? (int)($stats['agzObj'] / $stats['agzProd'] * 100) : 0;
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, '### STATS DATA', print_r($stats,true));
			return $stats;
		};


		// ====================================================================
		// DIREZIONE ----------------------------------------------------------
		if(in_array('AMMINISTRATORE', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, '### MAILING TO DIREZIONE');
			$this->log('MAILING UTENTI DIREZIONE');
			$data = $Repository->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);
			$data = $injectFn($data);
			$xlsWriteFn($data);
			$stats = $fetchStatsFn(new User(['type'=>'AMMINISTRATORE']));
			if(in_array('PREVIEW', $params['options'])) {
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email, '', $stats);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AMMINISTRATORE');
				foreach ($users as $User) {
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$mailFn($User->email, $User->nome.' '.$User->cognome, $stats);
				}
			}
		}

		// ====================================================================
		// AREA MANAGERS ------------------------------------------------------
		if(in_array('AREAMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, '### MAILING TO AREA MANAGERS');
			$this->log('MAILING UTENTI AREA MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				$_SESSION['AUTH']['UTYPE'] = 'AREAMGR';
				$_SESSION['AUTH']['AREA'] = $User->area;
				$_SESSION['AUTH']['DISTRICT'] = $User->district;
				$data = $Repository->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);
				$data = $injectFn($data);
				$xlsWriteFn($data);
				$stats = $fetchStatsFn($User);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email, '', $stats);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$_SESSION['AUTH']['UTYPE'] = 'AREAMGR';
					$_SESSION['AUTH']['AREA'] = $User->area;
					$_SESSION['AUTH']['DISTRICT'] = $User->district;
					$data = $Repository->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);
					$data = $injectFn($data);
					$xlsWriteFn($data);
					$stats = $fetchStatsFn($User);
					$mailFn($User->email, $User->nome.' '.$User->cognome, $stats);
				}
			}
		}

		// ====================================================================
		// DISTRICT MANAGERS --------------------------------------------------
		if(in_array('DISTRICTMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, '### MAILING TO DISTRICT MANAGERS');
			$this->log('MAILING UTENTI DISTRICT MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				$_SESSION['AUTH']['UTYPE'] = 'DISTRICTMGR';
				$_SESSION['AUTH']['AREA'] = $User->area;
				$_SESSION['AUTH']['DISTRICT'] = $User->district;
				$data = $Repository->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);
				$data = $injectFn($data);
				$xlsWriteFn($data);
				$stats = $fetchStatsFn($User);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email, '', $stats);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$_SESSION['AUTH']['UTYPE'] = 'DISTRICTMGR';
					$_SESSION['AUTH']['AREA'] = $User->area;
					$_SESSION['AUTH']['DISTRICT'] = $User->district;
					$data = $Repository->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);
					$data = $injectFn($data);
					$xlsWriteFn($data);
					$stats = $fetchStatsFn($User);
					$mailFn($User->email, $User->nome.' '.$User->cognome, $stats);
				}
			}
		}

		// ====================================================================
		// LOG ----------------------------------------------------------------

		if(!is_dir(Batch::LOG_DIR)) mkdir(Batch::LOG_DIR, 0755, true);
		file_put_contents(Batch::LOG_DIR.$Batch->id.'.log', $this->getLog());

		$data = @scandir(UPLOAD_DIR);
		foreach ($data as $k =>$file) if(!preg_match('/^INIZIATIVA_'.$id.'_POLIZZE_([0-9]{8})/',$file)) unset($data[$k]);
		$file = @array_pop($data);
		$stat = stat(UPLOAD_DIR.$file);
		$this->BatchesRepository->update($Batch->id, [
			'status'=>'COMPLETED',
			'fileDate'=>date('Y-m-d H:i:s', $stat['mtime']),
			'mail'=>$mailCounter
		]);

		// ====================================================================
		// CLEAN --------------------------------------------------------------

		unlink(self::TMP_DIR.'REPORT-INIZ-'.$id.'.html');
	}

	/**
	 * @batch(description="Reportistica: gare di area")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 * @param integer $id DatoCommerciale ID
	 * @param string $users Users types
	 * @param string $aree
	 * @param string $options
	 * @param string $email
	 * @throws \metadigit\core\db\orm\Exception
	 * @throws \metadigit\lib\util\csv\Exception
	 */
	function gareareaAction(Request $Req, Response $Res, $id, $users, $aree, $options, $email) {
		$params['users'] = explode(',', $users);
		$params['aree'] = explode(',', $aree);
		$params['options'] = explode(',', $options);
		$params['email'] = explode(',', $email);
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'BATCH PARAMS', print_r($params, true));

		$Report = $this->GareAreaRepository->update($id, ['lastSendDate'=>date('Y-m-d H:i:s')]);
		$Batch = $this->BatchesRepository->insert(NULL, ['type'=>'GARE AREA', 'id_gara'=>$id, 'nome'=>$Report->nome, 'status'=>'RUNNING', 'mail'=>0]);

		// ====================================================================
		// process CSV into SQL -----------------------------------------------

		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'INSERT CSV DATA INTO DB');
		$files = scandir(Batch::GARE_AREA_DIR.$id);
		$file = array_pop($files);
		$file = str_replace('.log','.csv',$file);
		$this->log('CSV: '.$file);
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		$pdoSt = $this->pdoPrepare('CALL app_reportistica_gare_area_polizze (?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?)');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute($values);
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_reportistica_gare_area_polizze WHERE gara_id = '.$id)->fetchColumn();
		$this->log('Numero di Polizze iniziali: '.$oldRecords);
		$this->pdoQuery('UPDATE app_reportistica_gare_area_polizze SET dataEstrazione = 0 WHERE gara_id = '.$id);

		// process CSV POLIZZE
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(Batch::GARE_AREA_DIR.$id.'/'.$file, 31, $processFn);
		$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
		}

		// count AFTER
		$this->pdoExec('DELETE FROM app_reportistica_gare_area_polizze WHERE dataEstrazione = 0 AND gara_id = '.$id);
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_reportistica_gare_area_polizze WHERE gara_id = '.$id)->fetchColumn();
		$this->log('Numero di Polizze finali: '.$newRecords);

		// ====================================================================
		// HTML Writer function
		if(!is_dir(self::TMP_DIR)) mkdir(self::TMP_DIR, 0755, true);
		$columns = [];
		include BASE_DIR.'api/incentivazioni/tpl/polizze.xls.php';
		array_unshift($columns,
			['Area',		'areaNome'],
			['District',	'districtNome'],
			['Agenzia',		'agenzia_id']
		);
		$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'COLUMNS', print_r($columns,true));
		$xlsWriteFn = function($data) use ($id, $columns) {
			$limit = ini_set('memory_limit', '512M');
			$HtmlWriter = new HtmlWriter();
			$HtmlWriter->setData(['DATA'=>$data, 'COLUMNS'=>$columns])->setTemplate(BASE_DIR.'api/apps/reportistica/tpl/gare-area.phtml');
			$HtmlWriter->write(self::TMP_DIR.'GARA-DI-AREA-'.$id.'.html');
			ini_set('memory_limit', $limit);
		};


		// ====================================================================
		// Mail function
		$mailCounter = 0;
		$mailFn = function($email, $name='') use ($id, $Report, &$mailCounter, $params) {
			if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
				$this->log('- '.$email.' EMAIL NON VALIDA');
				return;
			}
			$Message = $this->Mailer->newMessage()
				->setSubject($Report->mailSubject)
				->setBody($Report->mailBody)
				->setFrom(['<EMAIL>'=>'Nucleo di supporto'])
				->attach(\Swift_Attachment::fromPath(self::TMP_DIR.'GARA-DI-AREA-'.$id.'.html', 'text/html')->setFilename('REPORT.html'));
			if(ENVIRONMENT != 'PROD' && !in_array('PREVIEW', $params['options']))
				$Message->setTo([self::DEBUG_EMAIL=>$name]);
			else
				$Message->setTo([$email=>$name]);
			if($this->Mailer->send($Message)) $mailCounter++;
		};


		// ====================================================================
		// FETCH SQL ----------------------------------------------------------
		$fetchSQL = 'SELECT * FROM vw_app_reportistica_gare_area_polizze WHERE gara_id = '.$id;


		// ====================================================================
		// DIREZIONE ----------------------------------------------------------
		if(in_array('AMMINISTRATORE', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO DIREZIONE');
			$this->log('MAILING UTENTI DIREZIONE');
			$data = $this->pdoQuery($fetchSQL)->fetchAll(\PDO::FETCH_ASSOC);
			$xlsWriteFn($data);
			if(in_array('PREVIEW', $params['options'])) {
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AMMINISTRATORE');
				foreach ($users as $User) {
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}

		// ====================================================================
		// AREA MANAGERS ------------------------------------------------------
		if(in_array('AREAMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO AREA MANAGERS');
			$this->log('MAILING UTENTI AREA MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				$sql = !in_array('CROSSAREE', $params['options']) ? $fetchSQL.' AND area_id = '.$User->area : $fetchSQL;
				$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
				$xlsWriteFn($data);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,AREAMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$sql = !in_array('CROSSAREE', $params['options']) ? $fetchSQL.' AND area_id = '.$User->area : $fetchSQL;
					$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
					$xlsWriteFn($data);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}

		// ====================================================================
		// DISTRICT MANAGERS --------------------------------------------------
		if(in_array('DISTRICTMGR', $params['users'])) {
			$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO DISTRICT MANAGERS');
			$this->log('MAILING UTENTI DISTRICT MANAGERS');
			if(in_array('PREVIEW', $params['options'])) {
				$User =  $this->UsersRepository->fetchOne( null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				$sql = !in_array('CROSSDISTRICTS', $params['options']) ? $fetchSQL.' AND area_id = '.$User->area.' AND district_id = '.$User->district : $fetchSQL;
				$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
				$xlsWriteFn($data);
				foreach ($params['email'] as $email) {
					$this->log('- '.$email);
					$mailFn($email);
				}
			} else {
				$users =  $this->UsersRepository->fetchAll(null, null, 'cognome.Asc', 'active,EQ,1|type,EQ,DISTRICTMGR');
				foreach ($users as $User) {
					if(!in_array($User->area, $params['aree'])) continue;
					$this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'MAILING TO: '.$User->email);
					$this->log('- '.$User->email.' '.$User->nome.' '.$User->cognome);
					$sql = !in_array('CROSSDISTRICTS', $params['options']) ? $fetchSQL.' AND area_id = '.$User->area.' AND district_id = '.$User->district : $fetchSQL;
					$data = $this->pdoQuery($sql)->fetchAll(\PDO::FETCH_ASSOC);
					$xlsWriteFn($data);
					$mailFn($User->email, $User->nome.' '.$User->cognome);
				}
			}
		}


		// ====================================================================
		// LOG ----------------------------------------------------------------

		if(!is_dir(Batch::LOG_DIR)) mkdir(Batch::LOG_DIR, 0755, true);
		file_put_contents(Batch::LOG_DIR.$Batch->id.'.log', $this->getLog());

		preg_match('/^([0-9]{4})-([0-9]{2})-([0-9]{2})_([0-9]{2})([0-9]{2}).csv$/', $file, $matches);
		list($_, $Y, $m, $d, $H, $i) = $matches;
		$this->BatchesRepository->update($Batch->id, [
			'status'=>'COMPLETED',
			'fileDate'=>date('Y-m-d H:i:s', mktime($H, $i, 0, $m, $d, $Y)),
			'mail'=>$mailCounter
		]);

		// ====================================================================
		// CLEAN --------------------------------------------------------------
//		$this->pdoExec('DELETE FROM TABLE app_reportistica_gare_area_polizze');
		unlink(self::TMP_DIR.'GARA-DI-AREA-'.$id.'.html');
	}
}
