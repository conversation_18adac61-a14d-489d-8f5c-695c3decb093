<?php namespace batch;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\console\controller\ActionController;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchInterface;
use metadigit\lib\batch\BatchTrait;

class Abi extends ActionController implements BatchInterface {
    use PdoTrait, BatchTrait;

	/** Abi Service
	 * @var \service\AbiService */
	protected $AbiService;
	/** UsersRepository
	 * @var \data\UsersRepository */
	protected $UsersRepository;


	/**
	 * @batch(description="Abi: initialize users")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function initAction(Request $Req, Response $Res) {
		$this->log('===== INIZIO BATCH =====================================================');

		$ok = $err = 0;
		$total = $this->UsersRepository->count('active,EQ,1|acl_elearning,EQ,1|type,IN,AGENTE,INTERMEDIARIO');
		TRACE and $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'active users: ' . $total);
		$this->log(chr(9) . 'total users: ' . $total);
		$pageSize = 100;
		$pages = ceil($total/$pageSize);

		for($page = 1; $page <= $pages; $page++) {
			TRACE and $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'loop '. $page);

			$users = $this->UsersRepository->fetchAll($page, $pageSize, 'id.ASC', 'active,EQ,1|acl_elearning,EQ,1|type,IN,AGENTE,INTERMEDIARIO');
			foreach($users as $User) {
				if ($this->AbiService->registerUser($User->id)) {
					$ok++;
				} else {
					$err++;
					TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, 'ABI API failed');
					$this->log(chr(9).'ERR '.str_pad($User->id,8).str_pad($User->type,14).' '.$User->nome.' '.$User->cognome);
					$debug = $this->AbiService->debug();
					$this->log(chr(9).chr(9).'- HTTP CODE   '. $debug['code']);
					$this->log(chr(9).chr(9).'- PARAMS      '.json_encode($debug['params']));
					$this->log(chr(9).chr(9).'- RESPONSE    '.$debug['response']);
				}
			}
		}
		$this->log(chr(9) . sprintf('OK/ERR/TOT: %s/%s/%s', $ok, $err, $total));
		$this->log('===== FINE BATCH =======================================================');
	}

    /**
     * @string
     */
    protected $output;

    /**
     * @batch(description="Abi: compila email in csv")
     *
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     *
     * @return null
     */
    public function emailAction(Request $Req, Response $Res) {
        if (! $this->initFile()) {
            $this->log("Cannot init file.");
            return null;
        }

        $filepath = \metadigit\core\UPLOAD_DIR . "ABI_EMAIL.csv";

        if (! $lines = file($filepath)) {
            $this->log("No input file: $filepath");
            return null;
        }

        $statement = $this->pdoPrepare("SELECT email FROM vw_users_auth WHERE login=:id or login1=:id or login2=:id");

        foreach ($lines as $line) {
            $line = str_getcsv($line, ",");
            $id = $line[0];

            $statement->execute([':id' => $id]);

            if (! $user = $statement->fetch()) {
                $this->log("User $id not found");
                continue;
            }

            if (! isset($user['email']) || ! $user['email']) {
                $this->log("User $id email empty");
                continue;
            }

            if (! $this->append($id, $user['email'])) {
                $this->log("Error writing user $id, {$user['email']}");
            }
        }
    }

    protected function initFile() {
        $outputDir = \metadigit\core\LOG_DIR . "abi";

        if (! is_dir($outputDir) && ! mkdir($outputDir)) {
            $this->log("Cannot create output directory: $outputDir");
            return false;
        }

        $this->output = $outputDir . "/emails.csv";

        file_put_contents($this->output, "");
        return true;
    }

    protected function append($id, $email) {
        return file_put_contents($this->output, "$id,$email\n", FILE_APPEND);
    }

}
