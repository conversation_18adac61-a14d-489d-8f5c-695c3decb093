<?php namespace batch;

use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class Restart extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
    use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

    const COLS = 35;

    /**
     * @batch(description="Restart: import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile(\metadigit\core\UPLOAD_DIR . "RESTART_26[234567]_POLIZZE*.csv")) {
            $this->log("File not found");
            return;
        }

        if (! $id = $this->getId($file)) {
            $this->log("ID not found");
            return;
        }

        $this->log("Processing $file");

        $this->pdoBeginTransaction();

        $this->pdoQuery("delete from restart_polizze where iniziativa_id=$id");

        $statement = $this->pdoPrepare($this->query());

        $this->insert('parseRow', $statement, file($file), $id);

        $this->insertDifference($id);

        $dateLabel = $this->getDateLabel($file);

        $this->pdoQuery("update restart set lastUpdate='" . date('Y-m-d H:i:s') . "',lastUpdateLabel=$dateLabel where id = $id");

        $this->pdoCommit();

        $this->backupFile($file);
    }

    /**
     * @batch(description="Restart: static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        if (! $file = $this->getFile(\metadigit\core\UPLOAD_DIR . "RESTART_26[234567]_STATICO*.csv")) {
            $this->log("File not found");
            return;
        }

        if (! $id = $this->getId($file)) {
            $this->log("ID not found");
            return;
        }

        $this->log("Processing $file");

        $this->pdoBeginTransaction();

        $statement = $this->pdoPrepare("insert into restart_agenzie (iniziativa_id,agenzia_id,active,liquidated) values(:iniziativa_id,:agenzia_id,:active,:liquidated) on duplicate key update active=:active,liquidated=:liquidated");

        $this->insert('parseStaticRow', $statement, file($file), $id, ",");

        $this->pdoCommit();

        $this->backupFile($file);
    }

    protected function backupFile($file)
    {
        $destination = \metadigit\core\BACKUP_DIR . "CSV/Restart/" . basename($file);

        if (! rename($file, $destination)) {
            $this->log("WARNING: cannot backup $file");

            return false;
        }

        $this->log("File has been backed up.");

        return true;
    }

    protected function insert($parser, $statement, $data, $id, $separator = ";")
    {
        foreach ($data as $row) {
            $rowInfo = call_user_func_array([$this, $parser], [str_getcsv($row, $separator), $id]);

            try {
                $statement->execute($rowInfo);
            }

            catch (\PDOException $ex) {
                if ($ex->getCode() == 23000) {
                    $this->log("Not found {$rowInfo['agenzia_id']}");
                } else {
                    $this->log(print_r($rowInfo, true));
                    $this->log($ex->getMessage());
                }
            }

            catch (\Exception $ex) {
                $this->log("Skip agenzia {$rowInfo['agenzia_id']}");
                $this->log(print_r($rowInfo, true));
                $this->log($ex->getMessage());
            }
        }
    }

    protected function getId($filename)
    {
        preg_match("/RESTART_([0-9]{3})/", $filename, $matches);

        if (! $id = $matches[1]) {
            return null;
        }

        if (! in_array($id, [262, 263, 264 ,265, 266 ,267])) {
            return null;
        }

        return $id;
    }

    protected function getDateLabel($filename)
    {
        preg_match("/RESTART_[0-9]{3}_POLIZZE_([0-9]{8})\.csv/", $filename, $matches);

        if (! $label = $matches[1]) {
            return null;
        }

        return $label;
    }

    protected function insertDifference($inizId)
    {
        $this->pdoQuery("
            insert into restart_polizze
            (agenzia_id, iniziativa_id, convenzione, bonus, polizza, codiceProdotto, premioComputabile, codiceConvenzione)

            select
            distinct(a.agenzia_id), $inizId, 0, 0, 0, 0, 0, 0
            from restart_agenzie a
            left join restart_polizze p on p.agenzia_id = a.agenzia_id and p.iniziativa_id = a.iniziativa_id

            where p.agenzia_id is null
            and a.iniziativa_id = $inizId

            on duplicate key update agenzia_id=p.agenzia_id
        ");
    }

    protected function parseStaticRow($row, $id)
    {
        // Assert columns.
        if ($count = count($row) < 4) {
            throw new \Exception("Found $count columns");
        }

        if ($id != $row[0]) {
            throw new \Exception("Bad ID, expected $id - found $row[0]");
        }

        // Trim columns.
        $row = array_map(function($item){
            return trim($item);
        }, $row);

        return [
            'iniziativa_id' => $row[0],
            'agenzia_id' => $row[1],
            'active' => $row[2],
            'liquidated' => $row[8],
        ];
    }

    protected function parseRow($row, $id)
    {
        // Assert columns.
        if ($count = count($row) != self::COLS) {
            throw new \Exception("Found $count columns");
        }

        if ($id != $row[1]) {
            throw new \Exception("Bad ID, expected $id - found $row[1]");
        }

        // Trim columns.
        $row = array_map(function($item){
            return trim($item);
        }, $row);

        $bonus = $this->bonus($row);

        return [
            // Functional fields
            'agenzia_id' => $row[6] . str_pad($row[8], 3, "0", STR_PAD_LEFT),
            'iniziativa_id' => $row[1],
            "convenzione" => $bonus ? 1 : 0,
            "bonus" => $bonus,
            'polizza' => $row[12],
            'categoriaProdotto' => $this->getCategory($row),
            'codiceProdotto' => $row[17],
            'premioComputabile' => $this->float($row[24]),
            'codiceConvenzione' => $row[26],

            // Export only fields
            'tipoGara' => $row[0],
            'dataEstrazione' => $row[2],
            'dataEmissione' => $row[3],
            'dataIncasso' => $row[4],
            'dataEffetto' => $row[5],
            'compagnia' => $row[6],
            'danniVita' => $row[7],
            'idIntermediario' => $row[9],
            'delega' => $row[10],
            'idSezione' => $row[11],
            'codiceBene' => $row[13],
            'codiceCliente' => $row[15],
            'motivoEntrata' => $row[18],
            'famigliaProdotto' => $row[19],
            'premioAnnuoImp' => $row[20],
            'premioUnicoImp' => $row[21],
            'versamentiAggiuntivi' => $row[22],
            'deltaPremio' => $row[23],
            'codiceCampagna' => $row[25],
            'codicePartnership' => $row[27],
            'tipoCollettiva' => $row[28],
            'numeroMadre' => $row[29],
            'modulareVita' => $row[30],
            'otp' => $row[31],
            'fea' => $row[32],
            'trasmElettronicaDoc' => $row[33],
            'polizzaDigitale' => $row[34],
        ];
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        $this->log($count = count($files) . " files found.");

        if ($count != 1) {
            $this->log("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    protected function float($string)
    {
        return floatval(preg_replace("/,/", ".", $string));
    }

    protected function bonus($row)
    {
        // @WARNING precision loss at 3rd decimal position.
        $bonus = $this->float($row[24]) * 0.1;

        if (in_array($row[26], ["001863", "001867"])) {
            return $bonus;
        }

        if (in_array($row[17], ['CA18', 'DA18', '001021', '169'])) {
            return $bonus;
        }

        return 0;
    }

    protected function getCategory($row)
    {
        if (in_array($row[17], ['001021', '169'])) {
            return "MYPROTECTION";
        }

        if (in_array($row[17], ['CA18', 'DA18'])) {
            return "TCM";
        }

        return "DANNI";
    }

    protected function query()
    {
        return "INSERT INTO restart_polizze (
            agenzia_id,
            iniziativa_id,
            convenzione,
            bonus,
            polizza,
            categoriaProdotto,
            codiceProdotto,
            premioComputabile,
            codiceConvenzione,

            tipoGara,
            dataEstrazione,
            dataEmissione,
            dataIncasso,
            dataEffetto,
            compagnia,
            danniVita,
            idIntermediario,
            delega,
            idSezione,
            codiceBene,
            codiceCliente,
            motivoEntrata,
            famigliaProdotto,
            premioAnnuoImp,
            premioUnicoImp,
            versamentiAggiuntivi,
            deltaPremio,
            codiceCampagna,
            codicePartnership,
            tipoCollettiva,
            numeroMadre,
            modulareVita,
            otp,
            fea,
            trasmElettronicaDoc,
            polizzaDigitale
        ) values (
            :agenzia_id,
            :iniziativa_id,
            :convenzione,
            :bonus,
            :polizza,
            :categoriaProdotto,
            :codiceProdotto,
            :premioComputabile,
            :codiceConvenzione,

            :tipoGara,
            :dataEstrazione,
            :dataEmissione,
            :dataIncasso,
            :dataEffetto,
            :compagnia,
            :danniVita,
            :idIntermediario,
            :delega,
            :idSezione,
            :codiceBene,
            :codiceCliente,
            :motivoEntrata,
            :famigliaProdotto,
            :premioAnnuoImp,
            :premioUnicoImp,
            :versamentiAggiuntivi,
            :deltaPremio,
            :codiceCampagna,
            :codicePartnership,
            :tipoCollettiva,
            :numeroMadre,
            :modulareVita,
            :otp,
            :fea,
            :trasmElettronicaDoc,
            :polizzaDigitale
        ) ON DUPLICATE KEY UPDATE
            agenzia_id = :agenzia_id,
            iniziativa_id = :iniziativa_id,
            convenzione = :convenzione,
            bonus = :bonus,
            polizza = :polizza,
            codiceProdotto = :codiceProdotto,
            premioComputabile = :premioComputabile,
            codiceConvenzione = :codiceConvenzione
        ";
    }
}
