<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class <PERSON><PERSON> extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const FILE_LEAD = 'SANTINO_LEAD.csv';

	/**
	 * @batch(description="Santino: import CSV files")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res
	 */
	function importcsvAction(Request $Req, Response $Res) {
		$this->importCsvLead();
	}


	private function importCsvLead() {
		$this->log('== LEAD ==============: ');
		if(!file_exists(UPLOAD_DIR.self::FILE_LEAD)) {
			$this->log(chr(9).'CSV: '.self::FILE_LEAD.' NON PRESENTE');
			return;
		}

		$CsvProcessor = new CsvProcessor(['csvDelimiter' => ';', 'csvEnclosure' => '']);
		$sql = 'INSERT INTO app_santino_lead ( agenzia_id, data, nome, cognome, email, telefono, indirizzo, cap, localita, eta, dataScadenza, targa, marca, modello, cavalli )' .
			'VALUES ( :agenzia_id, :data, :nome, :cognome, :email, :telefono, :indirizzo, :cap, :localita, :eta, :dataScadenza, :targa, :marca, :modello, :cavalli )' .
			'ON DUPLICATE KEY UPDATE agenzia_id = :agenzia_id2, nome = :nome2, cognome = :cognome2, email = :email2, telefono = :telefono2, indirizzo = :indirizzo2, cap = :cap2, localita = :localita2, eta = :eta2, dataScadenza = :dataScadenza2, marca = :marca2, modello = :modello2, cavalli = :cavalli2 ';
		$pdoSt = $this->pdoPrepare($sql);
		$processFn = function ($i, $line, $values) use ($pdoSt) {
			$pdoSt->execute([
				'agenzia_id' => $values[0], 'agenzia_id2' => $values[0],
				'data' => date('Y-m-d'),
				'nome' => $values[1], 'nome2' => $values[1],
				'cognome' => $values[2], 'cognome2' => $values[2],
				'email' => $values[3], 'email2' => $values[3],
				'telefono' => $values[4], 'telefono2' => $values[4],
				'indirizzo' => $values[5], 'indirizzo2' => $values[5],
				'cap' => $values[7], 'cap2' => $values[7],
				'localita'=>$values[8], 'localita2'=>$values[8],
				'eta' => $values[12], 'eta2' => $values[12],
				'dataScadenza' => $values[6], 'dataScadenza2' => $values[6],
				'targa' => $values[13],
				'marca' => $values[9], 'marca2' => $values[9],
				'modello' => $values[10], 'modello2' => $values[10],
				'cavalli' => $values[11], 'cavalli2' => $values[11],
			]);
			return [true, null];
		};

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_santino_lead')->fetchColumn();
		$this->log('Numero LEAD iniziali: ' . $oldRecords);
		$this->log(chr(9) . 'CSV: ' . self::FILE_LEAD);
		// process CSV
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR . self::FILE_LEAD, 14, $processFn);
		$this->log(chr(9) . chr(9) . sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
		foreach ($errors as $error) {
			$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
				'EXCEPTION [' . $error['errData']['code'] . '] ' . $error['errData']['msg']
				: $error['errMsg'];
			$this->log(chr(9) . chr(9) . 'ROW ' . $error['index'] . ' ' . $msg . ' DATA: ' . $error['data']);
		}
		// count AFTER
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM app_santino_lead')->fetchColumn();
		$this->log('Numero LEAD finali: ' . $newRecords);
	}
}
