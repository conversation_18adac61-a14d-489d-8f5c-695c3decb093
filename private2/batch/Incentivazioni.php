<?php
namespace batch;
use data\incentivazioni\Incentivazione,
	metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class Incentivazioni extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const PATTERN_POLIZZE = '/^INIZIATIVA_([0-9]{1,3})_POLIZZE_([0-9]{8}).(txt|TXT|csv)$/';
	const PATTERN_POLIZZE_ERRATA = '/^INIZIATIVA_([0-9]{1,3})_POLIZZE_ERRATA_([0-9]{8}).(txt|TXT|csv)$/';
	const PATTERN_CLIENTI = '/^INIZIATIVA_([0-9]{1,3})_CLIENTI_([0-9]{8}).(txt|TXT|csv)$/';
	const PATTERN_QUIETANZE = '/^INIZIATIVA_([0-9]{1,3})_QUIETANZE_([0-9]{8}).(txt|TXT|csv)$/';
	const PATTERN_REGISTRAZ_MYANGEL = '/^INIZIATIVA_([0-9]{1,3})_REGISTRAZ_MYANGEL_([0-9]{8}).(txt|TXT|csv)$/';
	const PATTERN_STATICO = '/^INIZIATIVA_([0-9]{1,3})_STATICO_([0-9]{8}).(txt|TXT|csv)$/';

	const OLD_PATTERN_POLIZZE = '/^(NT|GA)_(DANN|VITA|DANN_NEXUS|VITA_NEXUS)_([0-9]{1,3})_([0-9]{8}).(txt|TXT|csv)$/';
	const OLD_PATTERN_POLIZZE_ERRATA = '/^(NT|GA)_(DANN|VITA|DANN_NEXUS|VITA_NEXUS)_([0-9]{1,3})_([0-9]{8})_ERRATA.(txt|TXT|csv)$/';
	const OLD_PATTERN_CLIENTI = '/^CLIENTI_INIZIATIVA_([0-9]{1,3})_([0-9]{8}).(txt|TXT|csv)$/';
	const OLD_PATTERN_QUIETANZE = '/^(NT|GA)_(DANN|VITA)_([0-9]{1,3})_QUIETANZE_([0-9]{8}).(txt|TXT|csv)$/';
	const OLD_PATTERN_STATICO = '/^STATICO_INIZIATIVA_([0-9]{1,3})_([0-9]{8}).(txt|TXT|csv)$/';

	protected $processedFiles = [];
    /** GroupamaPDNotificator
     * @var \service\GroupamaPDNotificator */
    protected $GroupamaPDNotificator;
	/** IncentivazioniRepository
	 * @var \metadigit\core\db\orm\Repository */
	protected $IncentivazioniRepository;

	protected function preHandle(Request $Req, Response $Res) {
		$this->log('===== INIZIO BATCH =====================================================');
		return true;
	}

	protected function postHandle(Request $Req, Response $Res, $View=null) {
		$this->log('===== FINE BATCH =======================================================');
	}

	/**
	 * @batch(description="FIX: rinomina files & elimina doppioni")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function fixfilesAction(Request $Req, Response $Res) {
		$cache = [];
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(preg_match(self::OLD_PATTERN_POLIZZE, $file, $matches)) {
				list($oldFile, $compagnia, $ramo, $id, $data, $ext) = $matches;
				$newFile = 'INIZIATIVA_'.$id.'_POLIZZE_'.$data.'.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile.' => '.$newFile);
				rename(UPLOAD_DIR.$oldFile, UPLOAD_DIR.$newFile);
				$cache[$id]['POLIZZE'][] = $newFile;
			}
			if(preg_match(self::PATTERN_POLIZZE, $file, $matches)) {
				list($file, $id, $data, $ext) = $matches;
				$cache[$id]['POLIZZE'][] = $file;
			}
			if(preg_match(self::OLD_PATTERN_POLIZZE_ERRATA, $file, $matches)) {
				list($oldFile, $compagnia, $ramo, $id, $data, $ext) = $matches;
				$newFile = 'INIZIATIVA_'.$id.'_POLIZZE_ERRATA_'.$data.'.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile.' => '.$newFile);
				rename(UPLOAD_DIR.$oldFile, UPLOAD_DIR.$newFile);
				$cache[$id]['POLIZZE_ERRATA'][] = $newFile;
			}
			if(preg_match(self::PATTERN_POLIZZE_ERRATA, $file, $matches)) {
				list($file, $id, $data, $ext) = $matches;
				$cache[$id]['POLIZZE_ERRATA'][] = $file;
			}
			if(preg_match(self::OLD_PATTERN_CLIENTI, $file, $matches)) {
				list($oldFile, $id, $data, $ext) = $matches;
				$newFile = 'INIZIATIVA_'.$id.'_CLIENTI_'.$data.'.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile.' => '.$newFile);
				rename(UPLOAD_DIR.$oldFile, UPLOAD_DIR.$newFile);
				$cache[$id]['CLIENTI'][] = $newFile;
			}
			if(preg_match(self::PATTERN_CLIENTI, $file, $matches)) {
				list($file, $id, $data, $ext) = $matches;
				$cache[$id]['CLIENTI'][] = $file;
			}
			if(preg_match(self::OLD_PATTERN_QUIETANZE, $file, $matches)) {
				list($oldFile, $compagnia, $ramo, $id, $data, $ext) = $matches;
				$newFile = 'INIZIATIVA_'.$id.'_QUIETANZE_'.$data.'.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile.' => '.$newFile);
				rename(UPLOAD_DIR.$oldFile, UPLOAD_DIR.$newFile);
				$cache[$id]['QUIETANZE'][] = $newFile;
			}
			if(preg_match(self::PATTERN_QUIETANZE, $file, $matches)) {
				list($file, $id, $data, $ext) = $matches;
				$cache[$id]['QUIETANZE'][] = $file;
			}
			if(preg_match(self::OLD_PATTERN_STATICO, $file, $matches)) {
				list($oldFile, $id, $data, $ext) = $matches;
				$newFile = 'INIZIATIVA_'.$id.'_STATICO_'.$data.'.csv';
				$this->trace(LOG_DEBUG, 1, __FUNCTION__, $oldFile.' => '.$newFile);
				rename(UPLOAD_DIR.$oldFile, UPLOAD_DIR.$newFile);
				$cache[$id]['STATICO'][] = $newFile;
			}
			if(preg_match(self::PATTERN_STATICO, $file, $matches)) {
				list($file, $id, $data, $ext) = $matches;
				$cache[$id]['STATICO'][] = $file;
			}
		}
		foreach ($cache as $id => $array) {
			foreach ($array as $type => $files) {
				sort($files);
				$files = array_unique($files);
				array_pop($files);
				if(!empty($files)) {
					$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'DELETE doppioni '.$id.' '.$type, print_r($files,true));
					foreach ($files as $file) {
						unlink(UPLOAD_DIR.$file);
					}
				}
			}
		}
	}

	/**
	 * @batch(description="GroupamaPDNotificator: aggiornamento incentivazioni (ENQUEUE)")
	 * @param Request $Req
	 * @param Response $Res
	 * @throws \metadigit\core\context\ContextException
	 * @throws \metadigit\core\db\orm\Exception
	 */
	function notifyPDAction(Request $Req, Response $Res) {
		$_SESSION['AUTH']['UTYPE'] = 'KA';
		$incentivazioni = $this->IncentivazioniRepository->fetchAll(null, null, null, 'dataUpdate,EQ,'.date('Y-m-d', time()-86400));
		foreach ($incentivazioni as $Incentiv) {
			$this->trace(LOG_DEBUG, 1, __FUNCTION__, $Incentiv->id.' '.$Incentiv->nome);
			$Repository = Incentivazione::getStatusRepository($Incentiv->id);
			$data = $Repository->fetchAll(null, null, null);
			foreach ($data as $Status) {
				$this->GroupamaPDNotificator->incentivazioneUpdate($Incentiv->id, $Incentiv->nome, $Incentiv->url, $Status->agenzia_id, $Status->notifyMessage());
			}
		}
	}

	/**
	 * @batch(description="Incent. 47 Super puntata 2")
	 * @param Request $Req
	 * @param Response $Res

	function i047Action(Request $Req, Response $Res) {
		$this->processStaticCsv(47);
		$this->processPolizzeCsv(47);
		$this->backupFiles(47);
		$this->execStoredProcedure(47);
	} */

	/**
	 * @batch(description="Incent. 50 Super puntata 3")
	 * @param Request $Req
	 * @param Response $Res

	function i050Action(Request $Req, Response $Res) {
		$this->processStaticCsv(50);
		$this->processPolizzeCsv(50);
		$this->backupFiles(50);
		$this->execStoredProcedure(50);
	} */

	/**
	 * @batch(description="Incent. 52 Grande Slam")
	 * @param Request $Req
	 * @param Response $Res

	function i052Action(Request $Req, Response $Res) {
		$this->processStaticCsv(52);
		$this->processPolizzeCsv(52);
		$this->backupFiles(52);
		$this->execStoredProcedure(52);
	} */

	/**
	 * @batch(description="Incent. 53 Strike")
	 * @param Request $Req
	 * @param Response $Res

	function i053Action(Request $Req, Response $Res) {
		$this->processStaticCsv(53);
		$this->processPolizzeCsv(53);
		$this->backupFiles(53);
		$this->execStoredProcedure(53);
	} */

	/**
	 * @batch(description="Incent. 58 Alza la posta")
	 * @param Request $Req
	 * @param Response $Res

	function i058Action(Request $Req, Response $Res) {
		$this->processStaticCsv(58);
		$this->processPolizzeCsv(58);
		$this->backupFiles(58);
		$this->execStoredProcedure(58);
	} */

	/**
	 * @batch(description="Incent. 59 Doppio Relax")
	 * @param Request $Req
	 * @param Response $Res

	function i059Action(Request $Req, Response $Res) {
		$this->processStaticCsv(59);
		$this->processPolizzeCsv(59);
		$this->backupFiles(59);
		$this->execStoredProcedure(59);
	} */

	/**
	 * @batch(description="Incent. 60 Grande Slam 2")
	 * @param Request $Req
	 * @param Response $Res

	function i060Action(Request $Req, Response $Res) {
		$this->processStaticCsv(60);
		$this->processPolizzeCsv(60);
		$this->backupFiles(60);
		$this->execStoredProcedure(60);
	} */

	/**
	 * @batch(description="Incent. 61 Grande Slam 3")
	 * @param Request $Req
	 * @param Response $Res

	function i061Action(Request $Req, Response $Res) {
		$this->processStaticCsv(61);
		$this->processPolizzeCsv(61);
		$this->backupFiles(61);
		$this->execStoredProcedure(61);
	} */

	/**
	 * @batch(description="Incent. 62 Wonderful Life")
	 * @param Request $Req
	 * @param Response $Res

	function i062Action(Request $Req, Response $Res) {
		$this->processStaticCsv(62);
		$this->processPolizzeCsv(62);
		$this->backupFiles(62);
		$this->execStoredProcedure(62);
	} */

	/**
	 * @batch(description="Incent. 63 Doppio Vantaggio")
	 * @param Request $Req
	 * @param Response $Res

	function i063Action(Request $Req, Response $Res) {
		$this->processStaticCsv(63);
		$this->processPolizzeCsv(63);
		$this->backupFiles(63);
		$this->execStoredProcedure(63);
	} */

	/**
	 * @batch(description="Incent. 64 Triathlon")
	 * @param Request $Req
	 * @param Response $Res

	function i064Action(Request $Req, Response $Res) {
		$this->processStaticCsv(64);
		$this->processPolizzeCsv(64);
		$this->backupFiles(64);
		$this->execStoredProcedure(64);
	} */

	/**
	 * @batch(description="Incent. 69 Spring Up")
	 * @param Request $Req
	 * @param Response $Res

	function i069Action(Request $Req, Response $Res) {
		$this->processStaticCsv(69);
		$this->processPolizzeCsv(69);
		$this->backupFiles(69);
		$this->execStoredProcedure(69);
	} */

	/**
	 * @batch(description="Incent. 70 FocusCliente 2013")
	 * @param Request $Req
	 * @param Response $Res

	function i070Action(Request $Req, Response $Res) {
		$this->processStaticCsv(70);
		$this->processPolizzeCsv(70);
		$this->processClientiCsv(70);
		$this->backupFiles(70);
		$this->execStoredProcedure(70);
	} */

	/**
	 * @batch(description="Incent. 71 Triathlon Summer")
	 * @param Request $Req
	 * @param Response $Res

	function i071Action(Request $Req, Response $Res) {
		$this->processStaticCsv(71);
		$this->processPolizzeCsv(71);
		$this->backupFiles(71);
		$this->execStoredProcedure(71);
	} */

	/**
	 * @batch(description="Incent. 72 Ready 2 Go")
	 * @param Request $Req
	 * @param Response $Res

	function i072Action(Request $Req, Response $Res) {
		$this->processStaticCsv(72);
		$this->processPolizzeCsv(72);
		$this->backupFiles(72);
		$this->execStoredProcedure(72);
	} */

	/**
	 * @batch(description="Incent. 73 Casa Facile Start")
	 * @param Request $Req
	 * @param Response $Res

	function i073Action(Request $Req, Response $Res) {
		$this->processStaticCsv(73);
		$this->processPolizzeCsv(73);
		$this->backupFiles(73);
		$this->execStoredProcedure(73);
	} */

	/**
	 * @batch(description="Incent. 74 Dieci Più")
	 * @param Request $Req
	 * @param Response $Res

	function i074Action(Request $Req, Response $Res) {
		$this->processStaticCsv(74);
		$this->processPolizzeCsv(74);
		$this->backupFiles(74);
		$this->execStoredProcedure(74);
	} */

	/**
	 * @batch(description="Incent. 75 Triathlon Winter")
	 * @param Request $Req
	 * @param Response $Res

	function i075Action(Request $Req, Response $Res) {
		$this->processStaticCsv(75);
		$this->processPolizzeCsv(75);
		$this->backupFiles(75);
		$this->execStoredProcedure(75);
	} */

	/**
	 * @batch(description="Incent. 76 A tutta forza")
	 * @param Request $Req
	 * @param Response $Res

	function i076Action(Request $Req, Response $Res) {
		$this->processStaticCsv(76);
		$this->processPolizzeCsv(76);
		$this->backupFiles(76);
		$this->execStoredProcedure(76);
	} */

	/**
	 * @batch(description="Incent. 77 SalvaMalus")
	 * @param \metadigit\core\cli\Request $Req
	 * @param \metadigit\core\cli\Response $Res

	function i077Action(Request $Req, Response $Res) {
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_77 ( agenzia_id, soglia1, soglia2 ) VALUES ( :agenzia_id, :soglia1, :soglia2 ) ON DUPLICATE KEY UPDATE soglia1 = :soglia1, soglia2 = :soglia2 ');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			list($agenziaID, $soglia1, $soglia2) = $values;
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'soglia1'=>$soglia1, 'soglia2'=>$soglia2]);
			return [true, null];
		};
		$this->processStaticCsv(77, false, $processFn, 3);

		$dataUpdate = null;
		$pdoSt = $this->pdoPrepare('UPDATE iniz_77 SET pezzi1 = :pezzi1, pezzi2 = :pezzi2, pezziTOT = :pezziTOT WHERE agenzia_id = :agenzia_id');
		$processFn = function($i, $line, $values) use ($pdoSt, &$dataUpdate) {
			list($id, $date, $agenziaID, $pezzi1, $pezzi2) = $values;
			if($date > $dataUpdate) $dataUpdate = $date;
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'pezzi1'=>$pezzi1, 'pezzi2'=>$pezzi2, 'pezziTOT'=>$pezzi1+$pezzi2]);
			return [true, null];
		};
		$this->processPolizzeCsv(77, $processFn, 5);

		$this->backupFiles(77);
		$this->execStoredProcedure77(77, $dataUpdate);
	} */

	/**
	 * @batch(description="Incent. 83 Pesca Grossa")
	 * @param Request $Req
	 * @param Response $Res

	function i083Action(Request $Req, Response $Res) {
		$this->processStaticCsv(83);
		$this->processPolizzeCsv(83);
		$this->backupFiles(83);
		$this->execStoredProcedure(83);
	} */

	/**
	 * @batch(description="Incent. 84 Casa Facile 2014")
	 * @param Request $Req
	 * @param Response $Res

	function i084Action(Request $Req, Response $Res) {
		$this->processStaticCsv(84);
		$this->processPolizzeCsv(84);
		$this->backupFiles(84);
		$this->execStoredProcedure(84);
	} */

	/**
	 * @batch(description="Incent. 85 PrimaClasse Roma-Parigi")
	 * @param Request $Req
	 * @param Response $Res

	function i085Action(Request $Req, Response $Res) {
		$this->processStaticCsv(85);
		$this->processPolizzeCsv(85);
		$this->backupFiles(85);
		$this->execStoredProcedure(85);
	} */

	/**
	 * @batch(description="Incent. 86 FocusCliente 2014")
	 * @param Request $Req
	 * @param Response $Res

	function i086Action(Request $Req, Response $Res) {
		$this->processStaticCsv(86);
		$this->processPolizzeCsv(86);
		$this->processClientiCsv(86);
		$this->backupFiles(86);
		$this->execStoredProcedure(86);
	} */

	/**
	 * @batch(description="Incent. 87 Dolce Vita")
	 * @param Request $Req
	 * @param Response $Res

	function i087Action(Request $Req, Response $Res) {
		$this->processStaticCsv(87);
		$this->processPolizzeCsv(87);
		$this->backupFiles(87);
		$this->execStoredProcedure(87);
	} */

	/**
	 * @batch(description="Incent. 88 Auto Star")
	 * @param Request $Req
	 * @param Response $Res

	function i088Action(Request $Req, Response $Res) {
		$this->processStaticCsv(88);
		$this->processPolizzeCsv(88);
		$this->processPolizzeCsv(91); // NOTE: CUSTOMIZATION WITH AUXILIARY TABLE 91
		$this->backupFiles(88);
		$this->execStoredProcedure(88);
	} */

	/**
	 * @batch(description="Incent. 89 Elisir del successo")
	 * @param Request $Req
	 * @param Response $Res

	function i089Action(Request $Req, Response $Res) {
		$this->processStaticCsv(89);
		$this->processPolizzeCsv(89);
		$this->backupFiles(89);
		$this->execStoredProcedure(89);
	} */

	/**
	 * @batch(description="Incent. 90 PrimaClasse Roma-Mosca")
	 * @param Request $Req
	 * @param Response $Res

	function i090Action(Request $Req, Response $Res) {
		$this->processStaticCsv(90);
		$this->processPolizzeCsv(90);
		$this->backupFiles(90);
		$this->execStoredProcedure(90);
	} */

	/**
	 * @batch(description="Incent. 92 Fly High")
	 * @param Request $Req
	 * @param Response $Res

	function i092Action(Request $Req, Response $Res) {
		$this->processStaticCsv(92);
		$this->processPolizzeCsv(92);
		$this->backupFiles(92);
		$this->execStoredProcedure(92);
	} */

	/**
	 * @batch(description="Incent. 93 Mr. Vita")
	 * @param Request $Req
	 * @param Response $Res

	function i093Action(Request $Req, Response $Res) {
		$this->processStaticCsv(93);
		$this->processPolizzeCsv(93);
		$this->backupFiles(93);
		$this->execStoredProcedure(93);
	} */

	/**
	 * @batch(description="Incent. 94 PrimaClasse Roma-Londra")
	 * @param Request $Req
	 * @param Response $Res

	function i094Action(Request $Req, Response $Res) {
		$this->processStaticCsv(94);
		$this->processPolizzeCsv(94);
		$this->backupFiles(94);
		$this->execStoredProcedure(94);
	} */

	/**
	 * @batch(description="Incent. 95 Uno + uno 2014")
	 * @param Request $Req
	 * @param Response $Res

	function i095Action(Request $Req, Response $Res) {
		$this->processStaticCsv(95);
		$this->processPolizzeCsv(95);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_95_clienti (agenzia_id, codEsazione, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codEsazione, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE codEsazione = :codEsazione, nome = :nome2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codEsazione = $values[2];
			$nome = trim($values[3]);
			$codFisc = trim($values[4]);
			$segmento = trim($values[5]);
			$sottosegmento = trim($values[6]);
			$punteggio = trim($values[7]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codEsazione'=>$codEsazione, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(95, $processFn, 8);
		$this->backupFiles(95);
		$this->execStoredProcedure(95);
	} */

	/**
	 * @batch(description="Incent. 96 ContaPunti")
	 * @param Request $Req
	 * @param Response $Res

	function i096Action(Request $Req, Response $Res) {
		$this->processStaticCsv(96);
		$this->processPolizzeCsv(96);
		$this->backupFiles(96);
		$this->execStoredProcedure(96);
	} */

	/**
	 * @batch(description="Incent. 97 Successi stellari")
	 * @param Request $Req
	 * @param Response $Res

	function i097Action(Request $Req, Response $Res) {
		$this->processStaticCsv(97);
		$this->processPolizzeCsv(97);
		$this->backupFiles(97);
		$this->execStoredProcedure(97);
	} */

	/**
	 * @batch(description="Incent. 102 A vele spiegate")
	 * @param Request $Req
	 * @param Response $Res

	function i102Action(Request $Req, Response $Res) {
		$this->processStaticCsv(102);
		$this->processPolizzeCsv(102);
		$this->backupFiles(102);
		$this->execStoredProcedure(102);
	} */

	/**
	 * @batch(description="Incent. 103 Mission Sunrise")
	 * @param Request $Req
	 * @param Response $Res

	function i103Action(Request $Req, Response $Res) {
		$this->processStaticCsv(103);
		$this->processPolizzeCsv(103);
		$this->backupFiles(103);
		$this->execStoredProcedure(103);
	} */

	/**
	 * @batch(description="Incent. 104 FocusCliente 2015")
	 * @param Request $Req
	 * @param Response $Res

	function i104Action(Request $Req, Response $Res) {
		$this->processStaticCsv(104);
		$this->processPolizzeCsv(104);
		$this->processClientiCsv(104);
		$this->backupFiles(104);
		$this->execStoredProcedure(104);
	} */

	/**
	 * @batch(description="Incent. 105 Uno + uno 2015")
	 * @param Request $Req
	 * @param Response $Res

	function i105Action(Request $Req, Response $Res) {
		$this->processStaticCsv(105);
		$this->processPolizzeCsv(105);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_105_clienti (agenzia_id, codEsazione, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codEsazione, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE codEsazione = :codEsazione, nome = :nome2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codEsazione = $values[2];
			$nome = trim($values[3]);
			$codFisc = trim($values[4]);
			$segmento = trim($values[5]);
			$sottosegmento = trim($values[6]);
			$punteggio = trim($values[7]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codEsazione'=>$codEsazione, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(105, $processFn, 8);
		$this->backupFiles(105);
		$this->execStoredProcedure(105);
	} */

	/**
	 * @batch(description="Incent. 107 Auto Star")
	 * @param Request $Req
	 * @param Response $Res

	function i107Action(Request $Req, Response $Res) {
		$this->processStaticCsv(107);
		$this->processPolizzeCsv(106); // NOTE: CUSTOMIZATION WITH AUXILIARY TABLE 106
		$this->processPolizzeCsv(107);
		$this->backupFiles(107);
		$this->execStoredProcedure(107);
	} */

	/**
	 * @batch(description="Incent. 108 AgriStart")
	 * @param Request $Req
	 * @param Response $Res

	function i108Action(Request $Req, Response $Res) {
		$this->processStaticCsv(108);
		$this->processPolizzeCsv(108);
		$this->backupFiles(108);
		$this->execStoredProcedure(108);
	} */

	/**
	 * @batch(description="Incent. 113 Mission Starlight")
	 * @param Request $Req
	 * @param Response $Res

	function i113Action(Request $Req, Response $Res) {
		$this->processStaticCsv(113);
		$this->processPolizzeCsv(113);
		$this->backupFiles(113);
		$this->execStoredProcedure(113);
	} */

	/**
	 * @batch(description="Incent. 114 EmojiConNoi")
	 * @param Request $Req
	 * @param Response $Res

	function i114Action(Request $Req, Response $Res) {
		$this->processStaticCsv(114);
		$this->processPolizzeCsv(114);
		$this->processPolizzeCsv(116); // NOTE: CUSTOMIZATION WITH AUXILIARY TABLE 116
		$this->backupFiles(114);
		$this->execStoredProcedure(114);
	} */

	/**
	 * @batch(description="Incent. 115 I tesori dell'oceano")
	 * @param Request $Req
	 * @param Response $Res

	function i115Action(Request $Req, Response $Res) {
		$this->processStaticCsv(115);
		$this->processPolizzeCsv(115);
		$this->backupFiles(115);
		$this->execStoredProcedure(115);
	} */

	/**
	 * @batch(description="Incent. 117 Mission Recharge")
	 * @param Request $Req
	 * @param Response $Res

	function i117Action(Request $Req, Response $Res) {
		$this->processStaticCsv(117);
		$this->processPolizzeCsv(117);
		$this->processQuietanze117Csv(117);
		$this->backupFiles(117);
		$this->execStoredProcedure(117);
	} */

	/**
	 * @batch(description="Incent. 118 Formula Vincente")
	 * @param Request $Req
	 * @param Response $Res

	function i118Action(Request $Req, Response $Res) {
		$this->processStaticCsv(118);
		$this->processPolizzeCsv(118);
		$this->backupFiles(118);
		$this->execStoredProcedure(118);
	} */

	/**
	 * @batch(description="Incent. 119 PacDan")
	 * @param Request $Req
	 * @param Response $Res

	function i119Action(Request $Req, Response $Res) {
		$this->processStaticCsv(119);
		$this->processPolizzeCsv(119);
		$this->backupFiles(119);
		$this->execStoredProcedure(119);
	} */

	/**
	 * @batch(description="Incent. 124 Doppia Chance")
	 * @param Request $Req
	 * @param Response $Res

	function i124Action(Request $Req, Response $Res) {
		$this->processStaticCsv(124);
		$this->processPolizzeCsv(124);
		$this->backupFiles(124);
		$this->execStoredProcedure(124);
	} */

	/**
	 * @batch(description="Incent. 125 Uno + uno 2016")
	 * @param Request $Req
	 * @param Response $Res

	function i125Action(Request $Req, Response $Res) {
		$this->processStaticCsv(125);
		$this->processPolizzeCsv(125);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_125_clienti (agenzia_id, codEsazione, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codEsazione, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE codEsazione = :codEsazione, nome = :nome2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codEsazione = $values[2];
			$nome = trim($values[3]);
			$codFisc = trim($values[4]);
			$segmento = trim($values[5]);
			$sottosegmento = trim($values[6]);
			$punteggio = trim($values[7]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codEsazione'=>$codEsazione, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(125, $processFn, 8);
		$this->backupFiles(125);
		$this->execStoredProcedure(125);
	} */

	/**
	 * @batch(description="Incent. 126 Doppia Chance Dart")
	 * @param Request $Req
	 * @param Response $Res

	function i126Action(Request $Req, Response $Res) {
		$this->processStaticCsv(126);
		$this->processPolizzeCsv(126);
		$this->backupFiles(126);
		$this->execStoredProcedure(126);
	} */

	/**
	 * @batch(description="Incent. 127 Over the top")
	 * @param Request $Req
	 * @param Response $Res

	function i127Action(Request $Req, Response $Res) {
		$this->processStaticCsv(127);
		$this->processPolizzeCsv(127);
		$this->backupFiles(127);
		$this->execStoredProcedure(127);
	} */

	/**
	 * @batch(description="Incent. 133 House Time")
	 * @param Request $Req
	 * @param Response $Res

	function i133Action(Request $Req, Response $Res) {
		$this->processStaticCsv(133);
		$this->processPolizzeCsv(133);
		$this->backupFiles(133);
		$this->execStoredProcedure(133);
	} */

	/**
	 * @batch(description="Incent. 135 Push the button")
	 * @param Request $Req
	 * @param Response $Res

	function i135Action(Request $Req, Response $Res) {
		$this->processStaticCsv(135);
		$this->processPolizzeCsv(135);
		$this->backupFiles(135);
		$this->execStoredProcedure(135);
	} */

	/**
	 * @batch(description="Incent. 138 Casa connessa")
	 * @param Request $Req
	 * @param Response $Res

	function i138Action(Request $Req, Response $Res) {
		$this->processStaticCsv(138);
		$this->processPolizzeCsv(138);
		$this->backupFiles(138);
		$this->execStoredProcedure(138);
	} */

	/**
	 * @batch(description="Incent. 141 Transformer")
	 * @param Request $Req
	 * @param Response $Res

	function i141Action(Request $Req, Response $Res) {
		$this->processStaticCsv(141);
		$this->processPolizzeCsv(141);
		$this->backupFiles(141);
		$this->execStoredProcedure(141);
	} */

	/**
	 * @batch(description="Incent. 142 Doppia Chance Strike")
	 * @param Request $Req
	 * @param Response $Res

	function i142Action(Request $Req, Response $Res) {
		$this->processStaticCsv(142);
		$this->processPolizzeCsv(142);
		$this->backupFiles(142);
		$this->execStoredProcedure(142);
	} */

	/**
	 * @batch(description="Incent. 146 Autopiù")
	 * @param Request $Req
	 * @param Response $Res

	function i146Action(Request $Req, Response $Res) {
		$this->processStaticCsv(146);
		$this->processPolizzeCsv(146);
		$this->processRegistrazioniMyAngelCsv(146);
		$this->backupFiles(146);
		$this->execStoredProcedure(146);
	} */

	/**
	 * @batch(description="Incent. 147 FocusCliente 2016")
	 * @param Request $Req
	 * @param Response $Res

	function i147Action(Request $Req, Response $Res) {
		$this->processStaticCsv(147);
		$this->processPolizzeCsv(147);
		$this->processClientiCsv(147);
		$this->processRegistrazioniMyAngelCsv(147);
		$this->backupFiles(147);
		$this->execStoredProcedure(147);
	} */

	/**
	 * @batch(description="Incent. 148 Doppia Chance Golf")
	 * @param Request $Req
	 * @param Response $Res

	function i148Action(Request $Req, Response $Res) {
		$this->processStaticCsv(148);
		$this->processPolizzeCsv(148);
		$this->backupFiles(148);
		$this->execStoredProcedure(148);
	} */

	/**
	 * @batch(description="Incent. 149 Sulla cresta dell'onda")
	 * @param Request $Req
	 * @param Response $Res

	function i149Action(Request $Req, Response $Res) {
		$this->processStaticCsv(149);
		$this->processPolizzeCsv(149);
		$this->backupFiles(149);
		$this->execStoredProcedure(149);
	} */

	/**
	 * @batch(description="Incent. 151 La nave del tesoro")
	 * @param Request $Req
	 * @param Response $Res

	function i151Action(Request $Req, Response $Res) {
		$this->processStaticCsv(151);
		$this->processPolizzeCsv(151);
		$this->backupFiles(151);
		$this->execStoredProcedure(151);
	} */

	/**
	 * @batch(description="Incent. 152 Casa")
	 * @param Request $Req
	 * @param Response $Res

	function i152Action(Request $Req, Response $Res) {
		$this->processStaticCsv(152);
		$this->processPolizzeCsv(152);
		$this->backupFiles(152);
		$this->execStoredProcedure(152);
	} */

	/**
	 * @batch(description="Incent. 153 Big Jump - ottobre")
	 * @param Request $Req
	 * @param Response $Res

	function i153Action(Request $Req, Response $Res) {
		$this->processStaticCsv(153);
		$this->processPolizzeCsv(153);
		$this->backupFiles(153);
		$this->execStoredProcedure(153);
	} */

	/**
	 * @batch(description="Incent. 154 Big Jump - novembre")
	 * @param Request $Req
	 * @param Response $Res

	function i154Action(Request $Req, Response $Res) {
		$this->processStaticCsv(154);
		$this->processPolizzeCsv(154);
		$this->backupFiles(154);
		$this->execStoredProcedure(154);
	} */

	/**
	 * @batch(description="Incent. 155 Big Jump - dicembre")
	 * @param Request $Req
	 * @param Response $Res

	function i155Action(Request $Req, Response $Res) {
		$this->processStaticCsv(155);
		$this->processPolizzeCsv(155);
		$this->backupFiles(155);
		$this->execStoredProcedure(155);
	} */

	/**
	 * @batch(description="Incent. 156 Big Jump - gennaio")
	 * @param Request $Req
	 * @param Response $Res

	function i156Action(Request $Req, Response $Res) {
		$this->processStaticCsv(156);
		$this->processPolizzeCsv(156);
		$this->backupFiles(156);
		$this->execStoredProcedure(156);
	} */

	/**
	 * @batch(description="Incent. 159 City Glide")
	 * @param Request $Req
	 * @param Response $Res

	function i159Action(Request $Req, Response $Res) {
		$this->processStaticCsv(159);
		$this->processPolizzeCsv(159);
		$this->backupFiles(159);
		$this->execStoredProcedure(159);
	} */

	/**
	 * @batch(description="Incent. 163 Pronti al lancio")
	 * @param Request $Req
	 * @param Response $Res

	function i163Action(Request $Req, Response $Res) {
		$this->processStaticCsv(163);
		$this->processPolizzeCsv(163);
		$this->backupFiles(163);
		$this->execStoredProcedure(163);
	} */

	/**
	 * @batch(description="Incent. 164 Big Jump - febbraio")
	 * @param Request $Req
	 * @param Response $Res

	function i164Action(Request $Req, Response $Res) {
		$this->processStaticCsv(164);
		$this->processPolizzeCsv(164);
		$this->backupFiles(164);
		$this->execStoredProcedure(164);
	} */

	/**
	 * @batch(description="Incent. 167 City Glide London")
	 * @param Request $Req
	 * @param Response $Res

	function i167Action(Request $Req, Response $Res) {
		$this->processStaticCsv(167);
		$this->processPolizzeCsv(167);
		$this->backupFiles(167);
		$this->execStoredProcedure(167);
	} */

	/**
	 * @batch(description="Incent. 170 Meccanismo perfetto")
	 * @param Request $Req
	 * @param Response $Res

	function i170Action(Request $Req, Response $Res) {
		$this->processStaticCsv(170);
		$this->processPolizzeCsv(170);
		$this->backupFiles(170);
		$this->execStoredProcedure(170);
	} */

	/**
	 * @batch(description="Incent. 173 City Glide Sydney")
	 * @param Request $Req
	 * @param Response $Res

	function i173Action(Request $Req, Response $Res) {
		$this->processStaticCsv(173);
		$this->processPolizzeCsv(173);
		$this->backupFiles(173);
		$this->execStoredProcedure(173);
	} */

	/**
	 * @batch(description="Incent. 174 Big Jump - giugno")
	 * @param Request $Req
	 * @param Response $Res

	function i174Action(Request $Req, Response $Res) {
		$this->processStaticCsv(174);
		$this->processPolizzeCsv(174);
		$this->backupFiles(174);
		$this->execStoredProcedure(174);
	} */

	/**
	 * @batch(description="Incent. 175 Piani alti")
	 * @param Request $Req
	 * @param Response $Res

	function i175Action(Request $Req, Response $Res) {
		$this->processStaticCsv(175);
		$this->processPolizzeCsv(175);
		$this->backupFiles(175);
		$this->execStoredProcedure(175);
	} */

	/**
	 * @batch(description="Incent. 176 FocusCliente 2017")
	 * @param Request $Req
	 * @param Response $Res

	function i176Action(Request $Req, Response $Res) {
		$this->processStaticCsv(176);
		$this->processPolizzeCsv(176);
		$this->processClientiCsv(176);
		$this->processRegistrazioniMyAngelCsv(176);
		$this->backupFiles(176);
		$this->execStoredProcedure(176);
	} */

	/**
	 * @batch(description="Incent. 177 Big Jump - luglio")
	 * @param Request $Req
	 * @param Response $Res

	function i177Action(Request $Req, Response $Res) {
		$this->processStaticCsv(177);
		$this->processPolizzeCsv(177);
		$this->backupFiles(177);
		$this->execStoredProcedure(177);
	} */

	/** LIKE 179 Uno+Uno, but simpler
	 * @batch(description="Incent. 179 Gold Rush")
	 * @param Request $Req
	 * @param Response $Res

	function i179Action(Request $Req, Response $Res) {
		$this->processStaticCsv(179);
		$this->processPolizzeCsv(179);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_179_clienti (agenzia_id, codEsazione, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codEsazione, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE codEsazione = :codEsazione, nome = :nome2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codEsazione = $values[2];
			$nome = trim($values[3]);
			$codFisc = trim($values[4]);
			$segmento = trim($values[5]);
			$sottosegmento = trim($values[6]);
			$punteggio = trim($values[7]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codEsazione'=>$codEsazione, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(179, $processFn, 8);
		$this->backupFiles(179);
		$this->execStoredProcedure(179);
	} */

	/**
	 * @batch(description="Incent. 181 Pronti al lancio 2")
	 * @param Request $Req
	 * @param Response $Res

	function i181Action(Request $Req, Response $Res) {
		$this->processStaticCsv(181);
		$this->processPolizzeCsv(181);
		$this->backupFiles(181);
		$this->execStoredProcedure(181);
	} */

	/**
	 * @batch(description="Incent. 182 My Protection")
	 * @param Request $Req
	 * @param Response $Res

	function i182Action(Request $Req, Response $Res) {
		$this->processStaticCsv(182);
		$this->processPolizzeCsv(182);
		$this->backupFiles(182);
		$this->execStoredProcedure(182);
	} */

	/**
	 * @batch(description="Incent. 183 City Glide Rio de Janeiro")
	 * @param Request $Req
	 * @param Response $Res

	function i183Action(Request $Req, Response $Res) {
		$this->processStaticCsv(183);
		$this->processPolizzeCsv(183);
		$this->backupFiles(183);
		$this->execStoredProcedure(183);
	} */

	/**
	 * @batch(description="Incent. 186 Ring the Gong")
	 * @param Request $Req
	 * @param Response $Res

	function i186Action(Request $Req, Response $Res) {
		$this->processStaticCsv(186);
		$this->processPolizzeCsv(186);
		$this->backupFiles(186);
		$this->execStoredProcedure(186);
	} */

	/**
	 * @batch(description="Incent. 187 City Glide Bangkok")
	 * @param Request $Req
	 * @param Response $Res

	function i187Action(Request $Req, Response $Res) {
		$this->processStaticCsv(187);
		$this->processPolizzeCsv(187);
		$this->backupFiles(187);
		$this->execStoredProcedure(187);
	} */

	/**
	 * @batch(description="Incent. 188 Stop&Go gennaio")
	 * @param Request $Req
	 * @param Response $Res

	function i188Action(Request $Req, Response $Res) {
		$this->processStaticCsv(188);
		$this->processPolizzeCsv(188);
		$this->backupFiles(188);
		$this->execStoredProcedure(188);
	} */

	/**
	 * @batch(description="Incent. 189 Stop&Go febbraio")
	 * @param Request $Req
	 * @param Response $Res

	function i189Action(Request $Req, Response $Res) {
		$this->processStaticCsv(189);
		$this->processPolizzeCsv(189);
		$this->backupFiles(189);
		$this->execStoredProcedure(189);
	} */

	/**
	 * @batch(description="Incent. 190 My Protection 2")
	 * @param Request $Req
	 * @param Response $Res

	function i190Action(Request $Req, Response $Res) {
		$this->processStaticCsv(190);
		$this->processPolizzeCsv(190);
		$this->backupFiles(190);
		$this->execStoredProcedure(190);
	} */

	/**
	 * @batch(description="Incent. 191 Pronti al lancio 2018")
	 * @param Request $Req
	 * @param Response $Res

	function i191Action(Request $Req, Response $Res) {
		$this->processStaticCsv(191);
		$this->processPolizzeCsv(191);
		$this->backupFiles(191);
		$this->execStoredProcedure(191);
	} */

	/**
	 * @batch(description="Incent. 196 FocusCliente 2018")
	 * @param Request $Req
	 * @param Response $Res

	function i196Action(Request $Req, Response $Res) {
		$this->processStaticCsv(196);
		$this->processPolizzeCsv(196);
		$this->processClientiCsv(196);
		$this->processRegistrazioniMyAngelCsv(196);
		$this->backupFiles(196);
		$this->execStoredProcedure(196);
	} */

	/**
	 * @batch(description="Incent. 197 Stop&Go marzo")
	 * @param Request $Req
	 * @param Response $Res

	function i197Action(Request $Req, Response $Res) {
		$this->processStaticCsv(197);
		$this->processPolizzeCsv(197);
		$this->backupFiles(197);
		$this->execStoredProcedure(197);
	} */

	/**
	 * @batch(description="Incent. 200 Magic Land")
	 * @param Request $Req
	 * @param Response $Res

	function i200Action(Request $Req, Response $Res) {
		$this->processStaticCsv(200);
		$this->processPolizzeCsv(200);
		$this->backupFiles(200);
		$this->execStoredProcedure(200);
	} */

	/** LIKE 203
	 * @batch(description="Incent. 203 Jet Ski")
	 * @param Request $Req
	 * @param Response $Res

	function i203Action(Request $Req, Response $Res) {
		$this->processStaticCsv(203);
		$this->processPolizzeCsv(203);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_203_clienti (agenzia_id, codiceCliente, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codiceCliente, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE nome = :nome2, codFisc = :codFisc2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codiceCliente = $values[7];
			$nome = trim($values[2]);
			$codFisc = trim($values[3]);
			$segmento = trim($values[4]);
			$sottosegmento = trim($values[5]);
			$punteggio = trim($values[6]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codiceCliente'=>$codiceCliente, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'codFisc2'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(203, $processFn, 8);
		$this->backupFiles(203);
		$this->execStoredProcedure(203);
	} */

	/**
	 * @batch(description="Incent. 204 Super Building")
	 * @param Request $Req
	 * @param Response $Res

	function i204Action(Request $Req, Response $Res) {
		$this->processStaticCsv(204);
		$this->processPolizzeCsv(204);
		$this->backupFiles(204);
		$this->execStoredProcedure(204);
	} */

	/**
	 * @batch(description="Incent. 206 Stop&Go giugno")
	 * @param Request $Req
	 * @param Response $Res

	function i206Action(Request $Req, Response $Res) {
		$this->processStaticCsv(206);
		$this->processPolizzeCsv(206);
		$this->backupFiles(206);
		$this->execStoredProcedure(206);
	} */

	/**
	 * @batch(description="Incent. 207 Stop&Go luglio")
	 * @param Request $Req
	 * @param Response $Res

	function i207Action(Request $Req, Response $Res) {
		$this->processStaticCsv(207);
		$this->processPolizzeCsv(207);
		$this->backupFiles(207);
		$this->execStoredProcedure(207);
	} */

	/**
	 * @batch(description="Incent. 208 Stop&Go agosto")
	 * @param Request $Req
	 * @param Response $Res

	function i208Action(Request $Req, Response $Res) {
		$this->processStaticCsv(208);
		$this->processPolizzeCsv(208);
		$this->backupFiles(208);
		$this->execStoredProcedure(208);
	} */

	/**
	 * @batch(description="Incent. 209 Stop&Go settembre")
	 * @param Request $Req
	 * @param Response $Res

	function i209Action(Request $Req, Response $Res) {
		$this->processStaticCsv(209);
		$this->processPolizzeCsv(209);
		$this->backupFiles(209);
		$this->execStoredProcedure(209);
	} */

	/**
	 * @batch(description="Incent. 210 Epic Land")
	 * @param Request $Req
	 * @param Response $Res

	function i210Action(Request $Req, Response $Res) {
		$this->processStaticCsv(210);
		$this->processPolizzeCsv(210);
		$this->backupFiles(210);
		$this->execStoredProcedure(210);
	} */

	/**
	 * @batch(description="Incent. 212 Jungle Adventure")
	 * @param Request $Req
	 * @param Response $Res

	function i212Action(Request $Req, Response $Res) {
		$this->processStaticCsv(212);
		$this->processPolizzeCsv(212);
		$this->backupFiles(212);
		$this->execStoredProcedure(212);
	} */

	/**
	 * @batch(description="Incent. 213 My Protection 3")
	 * @param Request $Req
	 * @param Response $Res

	function i213Action(Request $Req, Response $Res) {
		$this->processStaticCsv(213);
		$this->processPolizzeCsv(213);
		$this->backupFiles(213);
		$this->execStoredProcedure(213);
	} */

	/**
	 * @batch(description="Incent. 214 Sagittarius")
	 * @param Request $Req
	 * @param Response $Res

	function i214Action(Request $Req, Response $Res) {
		$this->processStaticCsv(214);
		$this->processPolizzeCsv(214);
		$this->backupFiles(214);
		$this->execStoredProcedure(214);
	} */

	/**
	 * @batch(description="Incent. 215 Target")
	 * @param Request $Req
	 * @param Response $Res

	function i215Action(Request $Req, Response $Res) {
		$this->processStaticCsv(215);
		$this->processPolizzeCsv(215);
		$this->backupFiles(215);
		$this->execStoredProcedure(215);
	} */

	/**
	 * @batch(description="Incent. 219 Extra Building")
	 * @param Request $Req
	 * @param Response $Res

	function i219Action(Request $Req, Response $Res) {
		$this->processStaticCsv(219);
		$this->processPolizzeCsv(219);
		$this->backupFiles(219);
		$this->execStoredProcedure(219);
	} */

	/**
	 * @batch(description="Incent. 220 FocusCliente 2019")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i220Action(Request $Req, Response $Res) {
		$this->processStaticCsv(220);
		$this->processPolizzeCsv(220);
		$this->processClientiCsv(220);
		$this->processRegistrazioniMyAngelCsv(220);
		$this->backupFiles(220);
		$this->execStoredProcedure(220);
	}*/

	/** LIKE 203
	 * @batch(description="Incent. 221 Welcome on board")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i221Action(Request $Req, Response $Res) {
		$this->processStaticCsv(221);
		$this->processPolizzeCsv(221);
		$pdoSt = $this->pdoPrepare('INSERT INTO iniz_221_clienti (agenzia_id, codiceCliente, nome, codFisc, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codiceCliente, :nome, :codFisc, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE nome = :nome2, codFisc = :codFisc2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
		$processFn = function($i, $line, $values) use ($pdoSt) {
			$agenziaID = $values[1];
			$codiceCliente = $values[7];
			$nome = trim($values[2]);
			$codFisc = trim($values[3]);
			$segmento = trim($values[4]);
			$sottosegmento = trim($values[5]);
			$punteggio = trim($values[6]);
			$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codiceCliente'=>$codiceCliente, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'codFisc2'=>$codFisc, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio]);
			return array(true, null);
		};
		$this->processClientiCsv(221, $processFn, 8);
		$this->backupFiles(221);
		$this->execStoredProcedure(221);
	}*/

	/**
	 * @batch(description="Incent. 223/224 Double Protection")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i223Action(Request $Req, Response $Res) {
		$this->processStaticCsv(223);
		$this->processStaticCsv(224);
		$this->processPolizzeCsv(223);
		$this->processPolizzeCsv(224);
		$this->backupFiles(223);
		$this->backupFiles(224);
		$this->execStoredProcedure(223);
	}*/

	/**
	 * @batch(description="Incent. 225 Target 2")
	 * @param Request $Req
	 * @param Response $Res

	function i225Action(Request $Req, Response $Res) {
		$this->processStaticCsv(225);
		$this->processPolizzeCsv(225);
		$this->backupFiles(225);
		$this->execStoredProcedure(225);
	} */

	/**
	 * @batch(description="Incent. 226 Il mix è giusto")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i226Action(Request $Req, Response $Res) {
		$this->processStaticCsv(226);
		$this->processPolizzeCsv(226);
		$this->backupFiles(226);
		$this->execStoredProcedure(226);
	}

	/**
	 * @batch(description="Incent. 227 Target 3")
	 * @param Request $Req
	 * @param Response $Res

	function i227Action(Request $Req, Response $Res) {
		$this->processStaticCsv(227);
		$this->processPolizzeCsv(227);
		$this->backupFiles(227);
		$this->execStoredProcedure(227);
	} */

	/**
	 * @batch(description="Incent. 232 Buona Vita")
	 * @param Request $Req
	 * @param Response $Res

	function i232Action(Request $Req, Response $Res) {
		$this->processStaticCsv(232);
		$this->processPolizzeCsv(232);
		$this->backupFiles(232);
		$this->execStoredProcedure(232);
	} */

	/**
	 * @batch(description="Incent. 234 Buona Vita al Top")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i234Action(Request $Req, Response $Res) {
		$this->processStaticCsv(234);
		$this->processPolizzeCsv(234);
		$this->backupFiles(234);
		$this->execStoredProcedure(234);
	}*/

	/**
	 * @batch(description="Incent. 235 Plus Building")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i235Action(Request $Req, Response $Res) {
		$this->processStaticCsv(235);
		$this->processPolizzeCsv(235);
		$this->backupFiles(235);
		$this->execStoredProcedure(235);
	}*/

	/**
	 * @batch(description="Incent. 236 Stay Safe")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i236Action(Request $Req, Response $Res) {
		$this->processStaticCsv(236);
		$this->processPolizzeCsv(236);
		$this->backupFiles(236);
		$this->execStoredProcedure(236);
	}*/

	/**
	 * @batch(description="Incent. 237 FocusCliente 2020")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i237Action(Request $Req, Response $Res) {
		$this->processStaticCsv(237);
		$this->processPolizzeCsv(237);
		$this->processClientiCsv(237);
		$this->backupFiles(237);
		$this->execStoredProcedure(237);
	}*/

	/**
	 * @batch(description="Incent. 254 FocusCliente 2021")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i254Action(Request $Req, Response $Res) {
		$this->processStaticCsv(254);
		$this->processPolizzeCsv(254);
		$this->processClientiCsv(254);
		$this->backupFiles(254);
		$this->execStoredProcedure(254);
	}

	/**
	 * @batch(description="Incent. 256 Toda Vida")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i256Action(Request $Req, Response $Res) {
		$this->processStaticCsv(256);
		$this->processPolizzeCsv(256);
		$this->backupFiles(256);
		$this->execStoredProcedure(256);
	}*/

	/**
	 * @batch(description="Incent. 257 Rinnoviamoci (1° per.)")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i257Action(Request $Req, Response $Res) {
		$this->processStaticCsv(257);
		$this->processPolizzeCsv(257);
		$this->backupFiles(257);
		$this->execStoredProcedure(257);
	}

	/**
	 * @batch(description="Incent. 258 Rinnoviamoci (2° per.)")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i258Action(Request $Req, Response $Res) {
		$this->processStaticCsv(258);
		$this->processPolizzeCsv(258);
		$this->backupFiles(258);
		$this->execStoredProcedure(258);
	}

	/**
	 * @batch(description="Incent. 259 Rinnoviamoci (3° per.)")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i259Action(Request $Req, Response $Res) {
		$this->processStaticCsv(259);
		$this->processPolizzeCsv(259);
		$this->backupFiles(259);
		$this->execStoredProcedure(259);
	}

	/**
	 * @batch(description="Incent. 260 Ultra Building")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i260Action(Request $Req, Response $Res) {
		$this->processStaticCsv(260);
		$this->processPolizzeCsv(260);
		$this->backupFiles(260);
		$this->execStoredProcedure(260);
	}*/

	/**
	 * @batch(description="Incent. 261 Stay Safe Plus")
	 * @param Request $Req
	 * @param Response $Res
	 */
	/*function i261Action(Request $Req, Response $Res) {
		$this->processStaticCsv(261);
		$this->processPolizzeCsv(261);
		$this->backupFiles(261);
		$this->execStoredProcedure(261);
	}*/

	/**
	 * @batch(description="Incent. 268 Last Chance")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function i268Action(Request $Req, Response $Res) {
		$this->processStaticCsv(268);
		$this->processPolizzeCsv(268);
		$this->backupFiles(268);
		$this->execStoredProcedure(268);
	}

	private function processPolizzeCsv($id, $processFn=null, $csvFields=35) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Import dei CSV POLIZZE ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		if(!is_callable($processFn)) {
			$pdoSt = $this->pdoPrepare('CALL iniz_import_polizze_cvs (?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?, ?,?,?,?,?)');
			$processFn = function($i, $line, $values) use ($pdoSt) {
				$pdoSt->execute($values);
				return [true, null];
			};
		}

		// count BEFORE
		$oldRecords = $this->pdoQuery('SELECT COUNT(*) FROM iniz_polizze WHERE iniziativa_id = '.$id)->fetchColumn();
		$this->log('Numero di Polizze iniziali: '.$oldRecords);
		$this->pdoQuery('UPDATE iniz_polizze SET dataEstrazione = 0 WHERE iniziativa_id = '.$id);

		// process CSV POLIZZE
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_POLIZZE);
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// process CSV POLIZZE ERRATA
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_POLIZZE_ERRATA);
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}

		// count AFTER
		$this->pdoExec('DELETE FROM iniz_polizze WHERE dataEstrazione = 0 AND iniziativa_id = '.$id);
		$newRecords = $this->pdoQuery('SELECT COUNT(*) FROM iniz_polizze WHERE iniziativa_id = '.$id)->fetchColumn();
		$this->log('Numero di Polizze finali: '.$newRecords);
	}

	private function processClientiCsv($id, $processFn=null, $csvFields=9) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Import dei CSV CLIENTI ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		if(!is_callable($processFn)) {
			$pdoSt = $this->pdoPrepare('INSERT INTO iniz_'.$id.'_clienti (agenzia_id, codiceCliente, nome, codFisc, segmentoPrecedente, segmento, sottosegmento, punteggio) VALUES (:agenzia_id, :codiceCliente, :nome, :codFisc, :segmentoPrecedente, :segmento, :sottosegmento, :punteggio) ON DUPLICATE KEY UPDATE nome = :nome2, segmentoPrecedente = :segmentoPrecedente2, segmento = :segmento2, sottosegmento = :sottosegmento2, punteggio = :punteggio2');
			$processFn = function($i, $line, $values) use ($pdoSt) {
				$agenziaID = $values[1];
				$nome = trim($values[2]);
				$codFisc = trim($values[3]);
				$segmentoPrecedente = trim($values[4]);
				$segmento = trim($values[5]);
				$sottosegmento = trim($values[6]);
				$punteggio = trim($values[7]);
				$codiceCliente = trim($values[8]);
				$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codiceCliente'=>$codiceCliente, 'nome'=>$nome, 'nome2'=>$nome, 'codFisc'=>$codFisc, 'segmentoPrecedente'=>$segmentoPrecedente, 'segmentoPrecedente2'=>$segmentoPrecedente, 'segmento'=>$segmento, 'segmento2'=>$segmento, 'sottosegmento'=>$sottosegmento, 'sottosegmento2'=>$sottosegmento, 'punteggio'=>$punteggio, 'punteggio2'=>$punteggio ]);
				return array(true, null);
			};
		}

		$this->pdoQuery('DELETE FROM iniz_'.$id.'_clienti');

		// process CSV CLIENTI
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_CLIENTI);
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}
	}

	private function processRegistrazioniMyAngelCsv($id, $processFn=null, $csvFields=10) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Import dei CSV REGISTRAZIONI MY ANGEL ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		if(!is_callable($processFn)) {
			$pdoSt = $this->pdoPrepare('INSERT INTO iniz_'.$id.'_myangel (agenzia_id, codiceCliente, tipo, CF_PIVA, data, nome, cognome, telefono, email) VALUES (:agenzia_id, :codiceCliente, :tipo, :CF_PIVA, :data, :nome, :cognome, :telefono, :email) ON DUPLICATE KEY UPDATE tipo = :tipo2, data = :data2, nome = :nome2, cognome = :cognome2, telefono = :telefono2, email = :email2');
			$processFn = function($i, $line, $values) use ($pdoSt) {
				$agenziaID = $values[1];
				$tipo = trim($values[2]);
				$CF_PIVA = trim($values[3]);
				$data = trim($values[4]); $d = explode('/', $data); $data = $d[2].'-'.$d[1].'-'.$d[0];
				$nome = trim($values[5]);
				$cognome = trim($values[6]);
				$telefono = trim($values[7]);
				$email = trim($values[8]);
				$codiceCliente = trim($values[9]);
				$pdoSt->execute(['agenzia_id'=>$agenziaID, 'codiceCliente'=>$codiceCliente, 'tipo'=>$tipo, 'tipo2'=>$tipo, 'CF_PIVA'=>$CF_PIVA, 'data'=>$data, 'data2'=>$data, 'nome'=>$nome, 'nome2'=>$nome, 'cognome'=>$cognome, 'cognome2'=>$cognome, 'telefono'=>$telefono, 'telefono2'=>$telefono, 'email'=>$email, 'email2'=>$email]);
				return array(true, null);
			};
		}

		$this->pdoQuery('DELETE FROM iniz_'.$id.'_myangel');

		// process CSV REGISTRAZIONI MY ANGEL
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_REGISTRAZ_MYANGEL);
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}
	}

	private function processStaticCsv($id, $agzDirezione=false, $processFn=null, $csvFields=9) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Setup tabella di status ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>',', 'csvEnclosure'=>'']);
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_STATICO);
		if(!is_callable($processFn)) {
			$pdoSt = $this->pdoPrepare('CALL iniz_'.$id.'_insert_obiettivi (?,?,?,?,?,?,?,?,?)');
			$processFn = function($i, $line, $values) use ($pdoSt) {
				$pdoSt->execute($values);
				return [true, null];
			};
		}

		// insert agenzie of type "DIREZ"
		if($agzDirezione) {
			$this->trace(LOG_DEBUG, 1, __FUNCTION__, 'initialize Status table with agenzie "DIREZ"');
			$agenzie = $this->pdoQuery('SELECT id FROM agenzie WHERE status = "DIREZ"')->fetchAll(\PDO::FETCH_ASSOC);
			$this->log(chr(9).'Agenzie "DIREZ" partecipanti: '.count($agenzie));
			$pdoSt = $this->pdoPrepare('INSERT INTO iniz_'.$id.' (agenzia_id) VALUES (:agenzia_id) ON DUPLICATE KEY UPDATE agenzia_id = agenzia_id');
			foreach ($agenzie as $ag) {
				$pdoSt->execute(['agenzia_id'=>$ag['id']]);
			}
		}

		// process CSV STATICO_INIZIATIVA
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV STATICO: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}
	}

	private function backupFiles($id) {
		if(empty($this->processedFiles)) return;
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$backupDirectory = \metadigit\core\BACKUP_DIR.'CSV/Iniz'.str_pad($id,3,'0',STR_PAD_LEFT).'/'.date('Y-m-d');
		$this->log('Backup dei CSV in: BACKUP/CSV/Iniz'.str_pad($id,3,'0',STR_PAD_LEFT).'/'.date('Y-m-d'));
		if(!file_exists($backupDirectory)) mkdir($backupDirectory, 0700, true);
		foreach($this->processedFiles as $file) copy(UPLOAD_DIR.'/'.$file, $backupDirectory.'/'.$file);
	}

	private function execStoredProcedure($id) {
		$this->log('Aggiornamento dati aggregati e di dettaglio ...');
		$updateDate = $this->pdoQuery('SELECT MIN(dataEstrazione) FROM iniz_polizze WHERE iniziativa_id = '.$id)->fetchColumn();
		list($currentDay, $totalDays) = Incentivazione::calculateDays($id, $updateDate);
		$success = $this->pdoStExecute('CALL iniz_'.$id.'_set_status(:updateDate, :totalDays, :currentDay)', ['updateDate'=>$updateDate, 'totalDays'=>$totalDays, 'currentDay'=>$currentDay]);
		$this->log(chr(9).(($success===false) ? 'ERRORE' : 'OK'));
		$this->pdoStExecute('UPDATE iniz SET dataUpdate = :dataUpdate WHERE id = '.$id, ['dataUpdate'=>$updateDate]);
	}

	private function execStoredProcedure77($id, $dataUpdate) {
		$this->log('Aggiornamento dati aggregati e di dettaglio ...');
		$success = $this->pdoExec('CALL iniz'.$id.'_update()');
		$this->log(chr(9).(($success===false) ? 'ERRORE' : 'OK'));
		$this->pdoStExecute('UPDATE iniz SET dataUpdate = :dataUpdate WHERE id = '.$id, ['dataUpdate'=>$dataUpdate]);
	}

	private function processQuietanze117Csv($id, $processFn=null, $csvFields=8) {
		$this->trace(LOG_DEBUG, 1, __FUNCTION__);
		$this->log('Import dei CSV QUIETANZE ...');
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);
		if(!is_callable($processFn)) {
			$pdoSt = $this->pdoPrepare('INSERT INTO iniz_'.$id.'_quietanze (agenzia_id, polizza, tariffa, dataDecorrenza, nome, dataScadenza, importo, incasso) VALUES (:agenzia_id, :polizza, :tariffa, :dataDecorrenza, :nome, :dataScadenza, :importo, :incasso)');
			$processFn = function($i, $line, $values) use ($pdoSt) {
				$agenziaID = $values[0];
				$polizza = trim($values[1]);
				$tariffa = trim($values[2]);
				$dataDecorrenza = trim($values[3]);
				$nome = trim($values[4]);
				$dataScadenza = trim($values[5]);
				$importo = trim($values[6]);
				$incasso = trim($values[7]);
				$pdoSt->execute(['agenzia_id'=>$agenziaID, 'polizza'=>$polizza, 'tariffa'=>$tariffa, 'dataDecorrenza'=>$dataDecorrenza, 'nome'=>$nome, 'dataScadenza'=>$dataScadenza, 'importo'=>$importo, 'incasso'=>$incasso]);
				return array(true, null);
			};
		}

		$this->pdoQuery('TRUNCATE iniz_'.$id.'_quietanze');

		// process CSV QUIETANZE
		$pattern = str_replace('([0-9]{1,3})', str_pad($id,3,'0',STR_PAD_LEFT), self::PATTERN_QUIETANZE);
		foreach(scandir(UPLOAD_DIR) as $file) {
			if(in_array($file,array('.','..')) || !preg_match($pattern, $file)) continue;
			$this->processedFiles[] = $file;
			$this->log(chr(9).'CSV: '.$file);
			list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.$file, $csvFields, $processFn);
			$this->log(chr(9).chr(9).sprintf('OK/ERR/SKIP/TOT: %s/%s/%s/%s', $ok, $err, $skip, $tot));
			foreach($errors as $error) {
				$msg = ($error['errCode'] == CsvProcessor::ERROR_LINE_EXCEPTION) ?
					'EXCEPTION ['.$error['errData']['code'].'] '.$error['errData']['msg']
					: $error['errMsg'];
				$this->log(chr(9).chr(9).'ROW '.$error['index'].' '.$msg.' DATA: '.$error['data']);
			}
		}
	}
}
