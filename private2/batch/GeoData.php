<?php
namespace batch;
use metadigit\core\cli\Request,
	metadigit\core\cli\Response,
	metadigit\lib\util\csv\CsvProcessor;

class GeoData extends \metadigit\core\console\controller\ActionController implements \metadigit\lib\batch\BatchInterface {
	use \metadigit\core\db\PdoTrait, \metadigit\lib\batch\BatchTrait;

	const FILE_REGIONI_PROVINCE = 'ISTAT_REGIONI_PROVINCE.csv';
	const FILE_COMUNI = 'ISTAT_COMUNI.csv';

	/**
	 * @batch(description="GEO: regioni, province, comuni")
	 * @param Request $Req
	 * @param Response $Res
	 */
	function importAction(Request $Req, Response $Res) {
		$CsvProcessor = new CsvProcessor(['csvDelimiter'=>';', 'csvEnclosure'=>'']);

		// regioni & province
		$pdoRegioni = $this->pdoPrepare('INSERT INTO geo_regioni (id, nome) VALUES (:id, :nome) ON DUPLICATE KEY UPDATE nome = :nome2');
		$pdoProvince = $this->pdoPrepare('INSERT INTO geo_province (id, regione_id, nome, sigla) VALUES (:id, :regione_id, :nome, :sigla) ON DUPLICATE KEY UPDATE nome = :nome2, sigla = :sigla2');
		$processFn = function($i, $line, $values) use ($pdoRegioni, $pdoProvince) {
			$regioneID = $values[5];
			$regioneName = substr($values[9], 0, strpos($values[9].'/', '/'));
			$regioneName = str_replace('-',' ',$regioneName);
			$provinciaID = $values[10];
			$provinciaName = substr($values[13], 0, strpos($values[13].'/', '/'));
			$provinciaInitials = $values[14];
			$pdoRegioni->execute([ 'id'=>$regioneID, 'nome'=>$regioneName, 'nome2'=>$regioneName ]);
			$pdoProvince->execute([ 'id'=>$provinciaID, 'regione_id'=>$regioneID, 'nome'=>$provinciaName, 'nome2'=>$provinciaName, 'sigla'=>$provinciaInitials, 'sigla2'=>$provinciaInitials ]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE_REGIONI_PROVINCE, 15, $processFn);

		// comuni
		$pdoComuni = $this->pdoPrepare('INSERT INTO geo_comuni (id, regione_id, provincia_id, comune_id, nome) VALUES (:id, :regione_id, :provincia_id, :comune_id, :nome) ON DUPLICATE KEY UPDATE nome = :nome2');
		$processFn = function($i, $line, $values) use ($pdoComuni) {
			$id = $values[3];
			$regioneID = $values[0];
			$provinciaID = $values[1];
			$comuneID = $values[2];
			$comuneName = substr($values[8], 0, strpos($values[8].'/', '/'));
			$pdoComuni->execute([ 'id'=>$id, 'regione_id'=>$regioneID, 'provincia_id'=>$provinciaID, 'comune_id'=>$comuneID, 'nome'=>$comuneName, 'nome2'=>$comuneName ]);
			return [true, null];
		};
		list($success, $ok, $err, $skip, $tot, $stats, $errors) = $CsvProcessor->processFile(UPLOAD_DIR.self::FILE_COMUNI, 22, $processFn);
	}
}
