<?php

namespace batch;

use api\apps\incentive\rinnoviamoci\exceptions\ColumnsMismatchException;
use metadigit\core\cli\Request;
use metadigit\core\cli\Response;
use metadigit\core\CoreTrait;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchTrait;
use PHPExcel_IOFactory;
use service\GroupamaUtils;
use batch\utils\SFTPFileDownload;

class PCPolicies
    extends \metadigit\core\console\controller\ActionController
    implements \metadigit\lib\batch\BatchInterface
{

    const CSV_DATA_COLS = 77;

    const SFTP_HOST = 'SFTPGA.groupama.it';
    const SFTP_PORT = 22;
    const SFTP_USER = 'keyassociatisftp';
    const SFTP_PASS = 'onr2tQgCLZRPNm!hG6ys';

    use PdoTrait, BatchTrait, CoreTrait;

    /**
     * @var GroupamaUtils
     */
    protected $utils;

    /**
     * @batch(description="Import file polizze Vita")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function importFileAction(Request $Req, Response $Res)
    {
        /*try {
            $sftpDownloader = new SFTPFileDownload(
                self::SFTP_HOST,
                self::SFTP_PORT,
                self::SFTP_USER,
                self::SFTP_PASS
            );

            $localPath = \metadigit\core\UPLOAD_DIR . "DANNI.csv";
            $sftpDownloader->downloadFile("/DANNI.csv", $localPath);
            $file = $localPath;

        } catch (\Exception $e) {
            $this->l("SFTP Download failed: " . $e->getMessage());
            return false;
        }*/

        if (!$file = $this->getFile($path = \metadigit\core\UPLOAD_DIR . "DANNI.csv")) {
            $this->l("File not found for path: $path");
            return false;
        }

        $this->pdoStExecute("START TRANSACTION");
        try {
            $this->pdoStExecute("delete from pc_data");
            $this->pdoStExecute("ALTER TABLE pc_data AUTO_INCREMENT = 1");
            $this->pdoStExecute("COMMIT");
        } catch (\Exception $e) {
            $this->pdoStExecute("ROLLBACK");
            throw $e;
        }

        $statement = $this->pdoPrepare("INSERT INTO pc_data (
            agenzia_id,
            canale,
            polizza,
            tipoEsitoOperazione,
            dataEffettoContratto,
            dataScadenzaContratto,
            dataEffettoOperazione,
            dataContabileEmissione,
            dataContabileIncasso,
            dataEffettivoIncasso,
            frazionamento,
            numeroCampagna,
            nomeCampagna,
            codiceConvenzione,
            nomeConvenzione,
            tipoProdotto,
            codiceProdotto,
            nomeProdotto,
            flagCollettiva,
            tipoCollettiva,
            premioNetto,
            premioAccessori
        ) VALUES (
            :agenzia_id,
            :canale,
            :polizza,
            :tipoEsitoOperazione,
            :dataEffettoContratto,
            :dataScadenzaContratto,
            :dataEffettoOperazione,
            :dataContabileEmissione,
            :dataContabileIncasso,
            :dataEffettivoIncasso,
            :frazionamento,
            :numeroCampagna,
            :nomeCampagna,
            :codiceConvenzione,
            :nomeConvenzione,
            :tipoProdotto,
            :codiceProdotto,
            :nomeProdotto,
            :flagCollettiva,
            :tipoCollettiva,
            :premioNetto,
            :premioAccessori
        )");

        try {
            // Read and convert files
            $fileLines = $this->readFileWithEncodingConversion($file);
            $this->insert($statement, $fileLines, ";", [$this, 'parsePCDataRow']);

        } catch (\Exception $e) {
            $this->l("Error parsing file: " . $e->getMessage());
            throw $e;
        }
    }

    protected function getFile($filepath)
    {
        $files = glob($filepath);

        if (! ($count = count($files))) {
            $this->l("File $filepath not found.");
            return null;
        }

        if ($count != 1) {
            $this->l("Error: found $count files.");
            return null;
        }

        return $files[0];
    }

    public function l($data)
    {
        if (is_array($data) || is_object($data)) {
            $data = print_r($data, true);
        }

        $this->log($data);
    }

    protected function insert($statement, $data, $separator, $callback)
    {
        array_shift($data);
        $line = 0;

        $result = [
            'OK' => 0,
            'KO' => 0,
            'AGENCY_NOT_FOUND' => 0,
            'DUPLICATE_ENTRY' => 0
        ];

        // Scan CSV file.
        foreach ($data as $row) {
            $rowInfo = null; // Initialize rowInfo before try block

            try {
                // Ask the application to parse the row.
                $rowInfo = call_user_func_array($callback, [str_getcsv($row, $separator), $line]);

                // Insert.
                $statement->execute($rowInfo);
                $result['OK']++;
            }
            catch (\PDOException $ex) {
                $result['KO']++;

                if ($ex->getCode() == 23000) {
                    // Check if it's a duplicate entry error
                    if (strpos($ex->getMessage(), 'Duplicate entry') !== false) {
                        $result['DUPLICATE_ENTRY']++;
                        $this->l("Row " . ($line + 1) . ": Duplicate entry");
                    } else {
                        // Agency not found error
                        $result['AGENCY_NOT_FOUND']++;
                        $this->l("Row " . ($line + 1) . ": Agency not found {$rowInfo['agenzia_id']}");
                    }
                }

                $this->l($rowInfo !== null ? print_r($rowInfo, true) : "Row parsing failed");
                $this->l($ex->getMessage());
            }
            catch (\Exception $ex) {
                $result['KO']++;

                if ($rowInfo !== null) {
                    $this->l("Row " . ($line + 1) . ": Skip agenzia {$rowInfo['agenzia_id']}");
                    $this->l(print_r($rowInfo, true));
                } else {
                    $this->l("Row " . ($line + 1) . ": Row parsing failed");
                }
                $this->l($ex->getMessage());
            }

            $line++;
        }

        $this->log("\nProcessing Summary:");
        $this->log("=====================================");
        $this->log("Total Rows Processed:\t$line");
        $this->log("Successful Imports:\t{$result['OK']}\t(" . round(($result['OK']/$line) * 100, 2) . "%)");
        $this->log("Failed Imports:\t\t{$result['KO']}\t(" . round(($result['KO']/$line) * 100, 2) . "%)");
        $this->log("  - Agency Not Found:\t{$result['AGENCY_NOT_FOUND']}");
        $this->log("  - Duplicate Entries:\t{$result['DUPLICATE_ENTRY']}");
        $this->log("=====================================");
    }

    protected function parsePCDataRow(array $row, $lineNumber = null)
    {
        // Assert columns.
        if (($count = count($row)) < self::CSV_DATA_COLS) {
            throw new ColumnsMismatchException(
                "Columns mismatch: expected " . self::CSV_DATA_COLS . "/ found $count / row: " . print_r($row, true)
            );
        }

        // Trim columns.
        $row = array_map(function ($item) {
            return trim($item);
        }, $row);

        $agenzia_id = $this->utils->convertGroupamaCodeToAgendo(explode(' ', $row[9])[0]);

        return [
            'agenzia_id' => $agenzia_id,
            'canale' => $row[5],
            'polizza' => $row[16],
            'tipoEsitoOperazione' => $row[23],
            'dataEffettoContratto' => $row[18] ? $this->formatDate($row[18]) : null,
            'dataScadenzaContratto' => $row[19] ? $this->formatDate($row[19]) : null,
            'dataEffettoOperazione' => $row[24] ? $this->formatDate($row[24]) : null,
            'dataContabileEmissione' => $row[25] ? $this->formatDate($row[25]) : null,
            'dataContabileIncasso' => $row[26] ? $this->formatDate($row[26]) : null,
            'dataEffettivoIncasso' => $row[27] ? $this->formatDate($row[27]) : null,
            'frazionamento' => $row[28],
            'numeroCampagna' => $row[29],
            'nomeCampagna' => $row[30],
            'codiceConvenzione' => $row[31],
            'nomeConvenzione' => $row[32],
            'tipoProdotto' => $row[35],
            'codiceProdotto' => $row[36],
            'nomeProdotto' => $row[37],
            'flagCollettiva' => $row[45],
            'tipoCollettiva' => $row[46],
            'premioNetto' => $row[62],
            'premioAccessori' => $row[63]
        ];
    }

    protected function formatDate($date)
    {
        if (empty($date)) {
            return null;
        }

        $dateObj = \DateTime::createFromFormat('d/m/Y', $date);
        if ($dateObj === false) {
            $this->l("Invalid date format: $date");
            return null;
        }

        return $dateObj->format('Y-m-d');
    }

    /**
     * Removes time portion from date string
     * @param string $dateString Date string in format like "27/5/2025 00:00:00"
     * @return string Date string without time portion
     */
    protected function removeTimeFromDate($dateString)
    {
        // Split by space and return only the date part
        $parts = explode(' ', $dateString);
        return $parts[0];
    }

    /**
     * Reads a file and converts its encoding to UTF-8 if necessary
     * 
     * @param string $filePath Path to the file
     * @return array Array of lines from the file in UTF-8 encoding
     */
    protected function readFileWithEncodingConversion($filePath)
    {
        $fileContent = file_get_contents($filePath);

        // Check for BOM markers first
        $isUtf8BOM = (substr($fileContent, 0, 3) === "\xEF\xBB\xBF");
        $isUtf16LE = (substr($fileContent, 0, 2) === "\xFF\xFE");
        $isUtf16BE = (substr($fileContent, 0, 2) === "\xFE\xFF");

        if ($isUtf8BOM) {
            $this->l("Detected UTF-8 BOM marker");
            $encoding = 'UTF-8';
        } elseif ($isUtf16LE) {
            $this->l("Detected UTF-16LE BOM marker");
            $encoding = 'UTF-16LE';
        } elseif ($isUtf16BE) {
            $this->l("Detected UTF-16BE BOM marker");
            $encoding = 'UTF-16BE';
        } else {
            // Try to detect encoding, but be more careful with the result
            $detectedEncoding = mb_detect_encoding($fileContent, ['UTF-8', 'UTF-16', 'UTF-16BE', 'UTF-16LE'], true);

            // Validate UTF-8 more thoroughly
            if ($detectedEncoding === 'UTF-8' || mb_check_encoding($fileContent, 'UTF-8')) {
                $this->l("Detected valid UTF-8 encoding for file $filePath");
                $encoding = 'UTF-8';
            } elseif ($detectedEncoding && $detectedEncoding !== 'UTF-8') {
                $this->l("Detected encoding for file $filePath: $detectedEncoding");
                $encoding = $detectedEncoding;
            } else {
                // Last resort: check if it looks like UTF-16 by checking for null bytes
                if (strpos($fileContent, "\x00") !== false) {
                    $this->l("Encoding detection failed but found null bytes. Assuming UTF-16LE as fallback");
                    $encoding = 'UTF-16LE';
                } else {
                    $this->l("Encoding detection failed. Assuming UTF-8 as fallback");
                    $encoding = 'UTF-8';
                }
            }
        }

        if ($encoding !== 'UTF-8') {
            $this->l("Converting file from $encoding to UTF-8");
            $fileContent = mb_convert_encoding($fileContent, 'UTF-8', $encoding);
            // Remove BOM if present after conversion
            $fileContent = str_replace("\xEF\xBB\xBF", '', $fileContent);
            return explode("\n", $fileContent);
        } else {
            // For UTF-8 files, remove BOM if present and return lines
            if ($isUtf8BOM) {
                $fileContent = substr($fileContent, 3); // Remove UTF-8 BOM
                return explode("\n", $fileContent);
            } else {
                return file($filePath, FILE_IGNORE_NEW_LINES);
            }
        }
    }

}
