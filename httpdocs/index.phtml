<?php
$_ = '?2.187.3';
/*
TEST data:
$browscap = [
	'platform' => 'WinXP',
	'platformVersion' => 'unknown',
	'browser' => 'IE',
	'browserVersion' => '8'
]; */
$browserBLOCK = false;
$browserWARNING = false;
switch ($browscap['browser']) {
	case 'IE':
		//if(in_array($browscap['platform'],['Win2000','WinXP','WinVista']) && $browscap['browserVersion'] < '9') $browser = 'install-FF-CH';
		if ($browscap['browserVersion'] < '9')
			$browserBLOCK = 'install-FF-CH';
		elseif ($browscap['browserVersion'] < '11')
			$browserWARNING = 'update-to-FF-CH';
		break;
	case 'Android':
	case 'Android WebView':
		if ($browscap['platformVersion'] < '4')
			$browserBLOCK = 'android-install-FF-CH';
		break;
}
$browserCssClass = 'browser-' . strtolower($browscap['browser']);
$browserCssClass .= ' browser-' . strtolower($browscap['browser']) . '-' . round($browscap['browserVersion'], 0);
?>
<!DOCTYPE html>
<html class="<?= $browserCssClass ?>">

<head>
	<title>PortaleAgendo</title>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/fonts/icomoon/style.css" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/fonts/icoagendo/style.css" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/styles/select2-3.5.2.css" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/styles/ui-select-0.9.6.css" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/styles/pagendo/pagendo.css<?= $_ ?>" />
	<link rel="stylesheet" href="//<?= $staticHost ?>/js/xeditable-0.1.12/xeditable.css" />
	<script type="text/javascript" src="//<?= $staticHost ?>/js/modernizr-2.8.3.js"></script>
</head>

<body id="ng-app" ng-app="app" class="x-flex-vbox" ng-class="cssContext">

	<header class="x-grid x-grid-full">
		<div class="x-grid-row">
			<div class="x-col-6-md x-col-12-sm x-col-12-xs" style="display: flex; align-items: center">
				<a href="/#/"><img src="//<?= $staticHost ?>/img/logo.png" class="x-logo" /></a>
				<img src="//<?= $staticHost ?>/img/logo-groupama-new.png" class="logo-groupama" />
				<ul>
					<li><a id="x-nav-btn" xng-nav-btn="" ng-if="$auth" class="x-btn"><i class="icon-menu-2"></i></a></li>
				</ul>
			</div>
			<div class="x-col-6-md x-col-12-sm x-hide-xs ng-cloak" ng-if="$auth">
				<ul>
					<li id="x-widget-user" ui-sref="account" style="cursor: pointer" title="Profilo utente"><i class="icon-user-3"></i> <strong ng-if="$authData.GHOST" style="color: #ff0000">GHOST</strong> {{ $authData.UNAME }} {{ $authData.AGENZIA }}</li>
					<li ng-if="UI_GHOST"><button ng-click="toggleGhost()" class="x-btn"><i class="icoagendo-user-ghost"></i></button></li>
					<li><a ng-if="!$authData.SSO" xng-auth-logout="" ng-click="doLogout()" class="x-btn"><i class="icon-exit"></i> Logout</a></li>
				</ul>
			</div>
		</div>
	</header>

	<nav id="x-nav-top" xng-nav="nav1" data-options="{ tokenIndex: 1 }">
		<ul>
			<li ng-repeat="item in menu" class="{{item.css}}">
				<a ng-href="{{item.href}}" data-id="{{item.id}}" title="{{item.label}}"><i class="x-hide-lg x-hide-xl {{item.icon}} btn-circle"></i><span>{{item.label}}</span></a>
				<ul ng-if="item.menu">
					<li ng-repeat="item in item.menu">
						<a ng-href="{{item.href}}" data-id="{{item.id}}">{{item.label}}</span></a>
					</li>
				</ul>
			</li>
		</ul>
	</nav>

	<? if ($browserBLOCK) { ?>
	<div class="x-flex-1" style="text-align: center">
		<? if ($browserBLOCK == 'install-FF-CH') { ?>
		<h2 style="color: #646464; line-height: normal;">Il nuovo Portale Agendo è arrivato.<br />Più funzionale, più utile, più bello.</h2>
		<h3 style="color: #e8501d; line-height: normal;">Per navigare sulla nuova versione del sito e proteggere la sicurezza del tuo PC è necessario installare un nuovo browser.</h3>
		<h4 style="color: #646464; line-height: normal;">Scegli il browser che preferisci.</h4>
		<div>
			<!--<a href="http://windows.microsoft.com/ie" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/ie.png"></a> &nbsp;&nbsp;-->
			<a href="http://www.google.com/chrome" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/chrome.png"></a> &nbsp;&nbsp;
			<a href="http://www.firefox.com/" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/firefox.png"></a>
		</div>
		<? } elseif ($browserBLOCK == 'android-install-FF-CH') { ?>
		<h2 style="color: #646464; line-height: normal;">Il nuovo Portale Agendo è arrivato.<br />Più funzionale, più utile, più bello.</h2>
		<h3 style="color: #e8501d; line-height: normal;">Per navigare sulla nuova versione del sito e proteggere la sicurezza del tuo smartphone/tablet è necessario installare un nuovo browser.</h3>
		<h4 style="color: #646464; line-height: normal;">Scegli il browser che preferisci.</h4>
		<div>
			<a href="https://play.google.com/store/apps/details?id=com.android.chrome" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/chrome.png"></a> &nbsp;&nbsp;
			<a href="https://play.google.com/store/apps/details?id=org.mozilla.firefox" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/firefox.png"></a>
		</div>
		<? } ?>
	</div>
	<? } else { ?>

	<!-- GHOST panel -->
	<main ng-if="UI_GHOST_PANEL" ng-controller="app.GhostCtrl" style="padding: 1rem">
		<section class="x-grid x-grid-full" style="border: 1px dashed; padding: 1rem 0">
			<div style="display: block; width: 100%; height: 1.5rem">
				<div ng-click="toggleGhost()" class="x-close">×</div>
			</div>
			<div class="x-grid-row" ng-if="!loading && $authData.UTYPE == 'KA'" style="margin-bottom: 1em; border-bottom: 1px dashed">
				<div class="x-col-4-md">
					<strong style="color: red">KA Super GHOST</strong>
				</div>
				<div class="x-col-4-md">
					<label>Ruolo:
						<select ng-model="ghost.type" ng-options="role for role in roles" ng-change="filterRoles()">
						</select>
					</label>
				</div>
				<div class="x-col-4-md">
					Utente:
					<ui-select ng-model="ghost.user" theme="select2" on-select="doGhost($item.id)" ng-disabled="!adminArray.length" style="min-width: 100%;" class="x-medium">
						<ui-select-match placeholder="Seleziona utente...">{{$select.selected.cognome}} {{$select.selected.nome}}</ui-select-match>
						<ui-select-choices repeat="User in adminArray">
							<div><span ng-bind-html="User.cognome | highlight: $select.search"></span> <span ng-bind-html="User.nome | highlight: $select.search"></span></div>
							<small><span ng-if="User.area">Area {{ User.area }}</span> <span ng-if="User.district">District {{ User.district }}</span></small>
						</ui-select-choices>
					</ui-select>
				</div>
			</div>
			<div class="x-grid-row">
				<div class="x-col-6-md">
					<strong>Per accedere in modalità Ghost inserire nel form un riferimento del nominativo cercato (codice agenzia di appartenenza, nome e/o cognome, località) e selezionare il nominativo tra l'elenco dei risultati.</strong><br />
					Il passaggio a GHOST sarà evidenziato dalla scritta "GHOST" in rosso, in alto a destra, accanto al nome dell'utente selezionato.<br />
					Per tornare alla modalità normale, basterà cliccare sul pulsante "Logout".<br />
					<br />
				</div>
				<div ng-if="!loading" class="x-col-6-md">
					Seleziona:
					<ui-select ng-model="ghost.agenzia" theme="select2" on-select="doGhost($item.id)" style="min-width: 100%;">
						<ui-select-match placeholder="Seleziona utente ...">{{$select.selected.cognome}} {{$select.selected.nome}}</ui-select-match>
						<ui-select-choices repeat="User in usersArray" refresh="search($select.search)" refresh-delay="250">
							<div><span ng-bind-html="User.agenzia_id | highlight: $select.search"></span> <span ng-bind-html="User.nomeAgenzia | highlight: $select.search"></span> - <span ng-bind-html="User.cognome | highlight: $select.search"></span> <span ng-bind-html="User.nome | highlight: $select.search"></span> - <span ng-bind-html="User.type"></span></div>
						</ui-select-choices>
					</ui-select>
				</div>
			</div>
			<div class="x-alert x-green" ng-if="loading">
				Caricamento GHOST in corso... attendere ...
			</div>
		</section>
	</main>

	<main ui-view="" ng-if="$auth" class="x-flex-1 x-overflow-y ng-cloak">
	</main>

	<main ng-if="!$auth" ng-controller="app.LoginCtrl" class="x-flex-1 ng-cloak">
		<!-- LOGIN PANEL -->
		<form id="x-login-panel" xng-auth-form="" ng-if="!pwdForm.active" class="ng-cloak" ng-submit="auth.resolution = resolution; doLogin()">
			<div class="x-form-row" ng-class="{'x-error': errors.login}">
				<!--<i class="icon-user"></i> User ID--> <input type="text" ng-model="auth.login" placeholder="Username" autocomplete="off" /><span class="x-btn-block">{{errors.login}} &nbsp;</span>
			</div>
			<div class="x-form-row" ng-class="{'x-error': errors.password}">
				<!--<i class="icon-key"></i> Password--> <input type="password" ng-model="auth.password" placeholder="Password" autocomplete="off" /><span class="x-btn-block">{{errors.password}} &nbsp;</span>
			</div>
			<div class="x-form-row">
				<a ng-click="auth.resolution = resolution; doLogin()" class="x-btn x-btn-block x-large x-groupama" ng-class="{ 'x-disabled': running }"><i ng-class="{ 'icon-key': !running, 'icon-busy': running }"></i> Login</a>
				<input type="submit" style="position: absolute; left: -9999px; width: 1px; height: 1px;" />
			</div>
			<div class="x-form-row">
				<a ng-click="pwdForm.active = true">Se è il tuo primo accesso o se hai dimenticato la password clicca qui.</i></a>
			</div>
		</form>
		<!-- PASSWORD RESCUE -->
		<div id="x-login-panel" ng-if="pwdForm.active" class="ng-cloak">
			<h4>Recupero password</h4>
			<p>Inserisci l'indirizzo email del tuo account:</p>
			<div class="x-form-row" ng-class="{'x-error': pwdForm.error, 'x-success': pwdForm.ok}">
				<input type="text" ng-model="pwdForm.email" ng-change="pwdForm.error = null" placeholder="Email" /><span class="x-help-inline">{{pwdForm.error}} &nbsp;</span>
			</div>
			<div ng-if="!pwdForm.ok" class="x-form-row">
				<a ng-click="pwdRescue()" class="x-btn x-btn-block x-large x-groupama" ng-class="{ 'x-disabled': pwdForm.running }">Continua</a>
			</div>
			<div ng-if="pwdForm.ok" class="x-form-row">
				Una email è stata inviata a <i>{{pwdForm.email}}</i> con le istruzioni per recuperare la password.<br />
				Controlla la tua casella di posta.
			</div>
			<div class="x-form-row">
				Dal 2 genaio 2017 sarà operativo il nuovo sistema di gestione password di Portale Agendo.<br /> <a href="//<?= $staticHost ?>/assets/PAgendo-sistema_gestione_password.pdf" target="_blank">Scopri cosa cambia</a>
			</div>
		</div>
		<? if ($browserWARNING == 'update-to-FF-CH') { ?>
		<div style="text-align: center">
			<h3 style="color: #e8501d; line-height: normal;">Naviga in sicurezza, aggiorna il tuo browser.</h3>
			<h4 style="color: #646464; line-height: normal;">Sul tuo PC è installata una vecchia versione di Internet Explorer. Tutela la sicurezza dei tuoi dati con un browser di ultima generazione.</h4>
			<div>
				<!--<a href="http://windows.microsoft.com/ie" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/ie.png"></a> &nbsp;&nbsp;-->
				<a href="http://www.google.com/chrome" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/chrome.png"></a> &nbsp;&nbsp;
				<a href="http://www.firefox.com/" target="_blank"><img src="//<?= $staticHost ?>/img/browsers/firefox.png"></a>
			</div>
		</div>
		<? } ?>
	</main>
	<? } ?>

	<div xng-ajax-spinner="" id="x-ajax-spinner">Caricamento dati ...</div>
	<div xng-growl=""></div>

	<!-- REQUIRED: Jquery + AngularJs -->
	<script type="text/javascript" src="//<?= $staticHost ?>/js/jquery-2.1.3.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-1.3.15/angular.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-1.3.15/angular-animate.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-1.3.15/angular-messages.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-1.3.15/angular-sanitize.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-1.3.15/i18n/angular-locale_it.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/angular-ui-router-0.2.13.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/ui-select-0.9.6.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/checklist-model-0.10.0.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-auth.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-data.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-storage.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui-form.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui-growl.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui-modal.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui-nav.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xng-0.99.6/xng-ui-tabs.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/ng-table-3.0.1/ng-table.min.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/xeditable-0.1.12/xeditable.js"></script>
	<!-- Flow.js -->
	<script type="text/javascript" src="//<?= $staticHost ?>/js/flow-2.9.0.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/fusty-flow-1.1.0.js"></script>
	<script type="text/javascript" src="//<?= $staticHost ?>/js/ng-flow-2.6.0.js"></script>
	<!-- Chart.js -->
	<script type="text/javascript" src="//<?= $staticHost ?>/js/chartjs-2.5.0/Chart.bundle.js"></script>
	<!-- angular-ui-bootstrap -->
	<script type="text/javascript" src="//<?= $staticHost ?>/js/ui-bootstrap-2.5.0/ui-bootstrap-tpls-2.5.0.min.js"></script>
	<!-- APP modules & controllers -->
	<script type="text/javascript" src="app.js<?= $_ ?>"></script>
	<script type="text/javascript" src="account/account.js<?= $_ ?>"></script>
	<script type="text/javascript" src="dashboard/dashboard.js<?= $_ ?>"></script>
	<script type="text/javascript" src="privacy/privacy.js<?= $_ ?>"></script>
	<script type="text/javascript" src="download/download.js<?= $_ ?>"></script>
	<script type="text/javascript" src="formazione/formazione.js<?= $_ ?>"></script>
	<script type="text/javascript" src="gare/gare.js<?= $_ ?>"></script>
	<script type="text/javascript" src="help-desk/help-desk.js<?= $_ ?>"></script>
	<script type="text/javascript" src="incentivazioni/incentivazioni.js<?= $_ ?>"></script>
	<script type="text/javascript" src="incentivazioni/96/96.js<?= $_ ?>"></script>
	<script type="text/javascript" src="marketing/marketing.js<?= $_ ?>"></script>
	<script type="text/javascript" src="myplace/myplace.js<?= $_ ?>"></script>
	<script type="text/javascript" src="news/news.js<?= $_ ?>"></script>
	<script type="text/javascript" src="iniz-comm/iniziative.js<?= $_ ?>"></script>
	<script type="text/javascript" src="iniz-comm/list/list.js<?= $_ ?>"></script>
	<script type="text/javascript" src="iniz-comm/detail/detail.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/agenzie/agenzie.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/iniziative/iniziative.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/news/news.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/quarantena/quarantena.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/rassegnastampa/rassegnastampa.js<?= $_ ?>"></script>
	<script type="text/javascript" src="admin/users/users.js<?= $_ ?>"></script>
	<script type="text/javascript" src="directives/agencies.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/apps.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/anagrafica/anagrafica.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/contributi/contributi.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/reportistica/reportistica.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/santino/santino.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/tassozero/tassozero.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/welcomeback/welcomeback.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/pianiagenzia2/pianiagenzia2.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/ivass.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table1/table1.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table1_1/table1_1.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table2/table2.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table3/table3.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table5/table5.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table7/table7.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/table8/table8.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/ivass/banche/banche.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/avanzamenti/avanzamenti.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/neo/neo.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/webinar/webinar.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/gdpr/gdpr.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/antiriciclaggio/antiriciclaggio.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/accordo2.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/directives/agencies.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/angular-input-masks-standalone.min.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/summary/summary.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/detail/detail.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/setup/setup.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/accordo2/agreements/agreements.js<?= $_ ?>"></script>
	<script type="text/javascript" src="incentive/incentive.js<?= $_ ?>"></script>
	<script type="text/javascript" src="apps/agit/agit.js<?= $_ ?>"></script>
	<script type="text/javascript">
		var staticHost = '<?php print $staticHost ?>';
	</script>

</body>

</html>
