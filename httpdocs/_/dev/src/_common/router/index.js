import { createRouter, createWebHistory} from 'vue-router';
import { isProduction } from '@/config';
import Dashboard from "@/apps/Dashboard.vue";
import AdminUsersRouting from "@/apps/admin-users/AdminUsersRouting.js";
import AdminNewsRouting from "@/apps/admin-news/AdminNewsRouting.js";
import AdminAgenzieRouting from '@/apps/admin-agenzie/AdminAgenzieRouting';
import FormazioneNeo from '@/apps/formazione-neo/FormazioneNeo';
import Agit from '@/apps/agit/Agit';
import InizComm from "@/apps/iniz-comm/InizComm.js";
import MyPage from "@/apps/my-page/MyPageRouting.js";
import Contributi from "../../apps/contributi/Contributi";
import Avanzamenti from "../../apps/avanzamenti/Avanzamenti";
import GestoreEventi from "@/apps/gestore-eventi/GestoreEventi.js";
import FormazioneBackend from "@/apps/formazione-backend/FormazioneBackend.js";
import Gare from '@/apps/gare/Gare';
import Incentivazioni from '@/apps/incentivazioni/Incentivazioni';
import Download from '@/apps/download/Download';
//import Analytics from '@/apps/analytics/Analytics';
import AccordoEconomicoRouting from "@/apps/accordo-economico/AccordoEconomicoRouting.js";

const router = createRouter({
    history: createWebHistory('/_/#!/'),
    routes: [
        {
            path: '/',
            name: 'dashboard',
            component: Dashboard,
            meta: {
                breadcrumb: 'Home'
            }
        },
        ...AdminUsersRouting,
        ...AdminNewsRouting,
        ...AdminAgenzieRouting,
        ...FormazioneNeo,
        ...Agit,
        ...InizComm,
        ...MyPage,
        ...Contributi,
        ...GestoreEventi,
        ...FormazioneBackend,
        ...Gare,
        ...Incentivazioni,
        ...Download,
        //...Analytics,
        ...AccordoEconomicoRouting,
        ...Avanzamenti,
        ...(!isProduction ? [
            {
                path: '/test',
                name: 'test',
                component: "<test</div>"
            },
            {
                path: '/test-upload-chunk',
                name: 'Test Upload Chunk',
                component: () => import('@/apps/Tests/TestUploadChunk.vue'),
            },
        ]: []
        )  ,
    ]

});

export default router;
