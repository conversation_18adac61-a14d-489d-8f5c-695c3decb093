<script setup>
import {inject, reactive, ref, watch, computed, watchEffect} from "vue";
import {PhArrowLeft, PhFloppyDiskBack, PhInfo, PhPrinter, PhScroll, PhSwap, PhX} from "@phosphor-icons/vue";
import {useRoute, useRouter} from "vue-router";
import {accordo2Api} from "@/apps/accordo-economico/accordo2Api.js";
import {formatDatetime, formatNumber} from "@/libs/formatter.js";
import InputNumber from "primevue/inputnumber";
import {useUserStore} from "@/_common/stores/user.js";
import {toast} from "vue3-toastify";
import RadioButton from 'primevue/radiobutton';
import RadioButtonGroup from 'primevue/radiobuttongroup';
import Dialog from "primevue/dialog";
import {geoApi} from "@/_common/api/geo.js";
import StatusPillFormatter from "@/apps/accordo-economico/StatusPillFormatter.vue";

const route = useRoute();
const router = useRouter();
const props = defineProps(['year', 'agenzia_id', 'revisionId'])
const selectedYear = inject('selectedYear');
const authData = useUserStore();
const agencyInfo = ref(await accordo2Api.getAgencyInfo(props.agenzia_id));
const districts = ref(await geoApi.getDistricts());
const areas = ref(await geoApi.getAreas());

const agreement = ref(await accordo2Api.getAgreement(props.agenzia_id, props.year));
const revisions = ref(await accordo2Api.revisions(agreement.value.id));
const selectedRevisionId = ref(props.revisionId || revisions.value[0]?.id);

// Update URL if needed
if (selectedRevisionId.value && selectedRevisionId.value !== props.revisionId) {
  router.replace({
    name: 'profile',
    params: {
      ...route.params,
      revisionId: selectedRevisionId.value
    }
  });
}

const selectedRevision = ref({});
let RPSlots = reactive(await accordo2Api.ramiPrefSlots(agreement.value.fasciaRamiPref, props.year));
let vitaSlots = reactive(await accordo2Api.vitaSlots(agreement.value.fasciaVita, props.year));
let showVitaSlots = ref(false);
let showRPSlots = ref(false);
const slotsList = reactive(await accordo2Api.slots(props.year));
let showRPSlotsSelect = ref(false);
const newMessage = ref({
    timestamp: null, text: '', author: '', userType: ''
});
const showAgencyInfoModal = ref(false);

const areaInfo = computed(() => {
    if (!areas.value?.data || !agencyInfo.value?.area) {
        return null;
    }

    return areas.value.data.find(area => area.id === agencyInfo.value.area);
});

const districtInfo = computed(() => {
    if (!districts.value?.data || !agencyInfo.value?.district) {
        return null;
    }

    return districts.value.data.find(district => district.id === agencyInfo.value.district);
});

// Form data with direct reactive properties
const formData = reactive({
    id: null,
    accordo_id: null,
    /*user_id: authData.UID,
    mode: 'APP',
    isLatest: 1,*/

    vitaBaseCalcoloObj: 0,
    vitaAnnL1Obj: 0,
    vitaAnnL1ObjPezzi: 0,
    vitaAnnL1IncassiCur: 0,
    vitaAnnL1Rappel: 0,
    vitaAnnL2Obj: null,
    vitaAnnL2IncassiCur: 0,
    vitaAnnL2ExtraRappel: 0,
    vitaSlots: [],
    vitaMaxRappel: 0,
    /* RP - incassi */
    ramiPrefSemIncassiPrev: 0,
    ramiPrefAnnL1IncassiPrev: 0,
    /* RP - aliquote obiettivo */
    ramiPrefSemObj: 0,
    ramiPrefAnnL1Obj: 0,
    ramiPrefAnnL2Obj: 0,
    ramiPrefAnnL2ObjPersonal: 0,
    /* RP - obiettivo incremento */
    ramiPrefSemIncremento: 0,
    ramiPrefAnnL1Incremento: 0,
    ramiPrefAnnL2Incremento: 0,
    ramiPrefAnnL2IncrementoPersonal: 0,
    /* RP - obiettivo emessi */
    ramiPrefSemIncassiCur: 0,
    ramiPrefAnnL1IncassiCur: 0,
    ramiPrefAnnL2IncassiCur: 0,
    ramiPrefAnnL2IncassiCurPersonal: 0,
    /* RP - rappel */
    ramiPrefSemRappel: 0,
    ramiPrefAnnL1Rappel: 0,
    ramiPrefAnnL2Rappel: 0,
    ramiPrefAnnL2RappelPersonal: 0,

    year: props.year,
    statusChangeMessage: [],
    status: null

});

// Validation errors
const errors = reactive({
    vitaAnnL2IncassiCur: null,
    vitaAnnL2ExtraRappel: null,
    ramiPrefSemIncassiCur: null,
    ramiPrefAnnL1IncassiCur: null,
    ramiPrefAnnL2IncassiCur: null,
    ramiPrefAnnL2IncassiCurPersonal: null,
    ramiPrefAnnL1Rappel: null,
    ramiPrefAnnL2Rappel: null,
    ramiPrefAnnL2RappelPersonal: null,
});

// Add this computed property after your other computed properties
const hasErrors = computed(() => {
    if (authData.UTYPE === 'AMMINISTRATORE') {
        return false;
    }
    return Object.values(errors).some(error => error !== null);
});

// Transform revisions data
revisions.value = revisions.value.map((revision, index) => ({
    ...revision,
    label: index === 0
        ? `Ultima versione - ${revision.mode}`
        : `${revision.mode} - ${formatDatetime(revision.updatedAt)}`
}));

function formatStatusChangeMessage(message) {
    if (!message || message === "") {
        formData.statusChangeMessage = [];
        return;
    }

    try {
        formData.statusChangeMessage = typeof message === 'string' ? JSON.parse(message) : message;
    } catch (e) {
        console.error('Error parsing statusChangeMessage:', e);
        formData.statusChangeMessage = [];
    }
}

// Update selectedRevision and formData when selectedRevisionId changes
watch(selectedRevisionId, (newId) => {
    const found = revisions.value.find(rev => rev.id === Number(newId));
    if (found) {
        selectedRevision.value = { ...found };
        // Update formData with the selected revision data
        Object.keys(formData).forEach(key => {
            if (key in found) {
                formData[key] = found[key];
            }
        });
        formData.vitaSlots = vitaSlots;
        formData.RPSlots = RPSlots;

        // Format the statusChangeMessage
        formatStatusChangeMessage(found.statusChangeMessage);
        checkObjAllowed('vita');
        checkObjAllowed('ramiPref', 'Personal');
    }
}, { immediate: true });

watch(() => formData.vitaAnnL2Obj, (value) => {
    calculateVitaObjPerc('Ann', 'L2');
});

// Watch for vitaAnnL2IncassiCur changes to validate and calculate vitaAnnL2Obj
watch(() => formData.vitaAnnL2IncassiCur, (value) => {
    errors.vitaAnnL2IncassiCur = value <= formData.vitaAnnL1IncassiCur
        ? 'L\'obiettivo personalizzato deve essere maggiore dell\'obiettivo annuale di 1° livello'
        : null;

    calculateVitaObj();
});

// Watch for vitaAnnL2ExtraRappel changes to validate against max rappel
watch(() => formData.vitaAnnL2ExtraRappel, (value) => {
    errors.vitaAnnL2ExtraRappel = value > formData.vitaMaxRappel
        ? 'L\'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.'
        : null;
});

function onChangeRevision(e) {
    selectedRevisionId.value = e.target.value;
    router.push({
        name: 'profile',
        params: {
            ...route.params,
            revisionId: selectedRevisionId.value
        }
    });
}

function formatAuthor () {
    let author = '';
    switch (authData.UTYPE) {
        case 'AREAMGR' :
            author = 'Area Manager';
            break;
        case 'DISTRICTMGR' :
            author = 'District Manager';
            break;
    }
    return author + ' ' + authData.UNAME;
}

async function onAgencyDetailSubmit() {
    // Check if NewMessage.text is not empty and add to statusChangeMessage array
    if (newMessage.value.text && newMessage.value.text.trim() !== '') {
        newMessage.value.timestamp = new Date().toLocaleDateString('it-IT', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        }).replace(',', ' -');
        if (authData.UTYPE === 'AMMINISTRATORE' || authData.UTYPE === 'KA') {
            newMessage.value.author = 'Compagnia';
            newMessage.value.userType = 'AMMINISTRATORE';
        }
        else {
            newMessage.value.author = formatAuthor();
            newMessage.value.userType = 'MANAGER';
        }
        formData.statusChangeMessage.push(newMessage.value);
    }

    try {
        // Call the API to update the revision
        const response = await accordo2Api.updateRevision(selectedRevisionId.value, formData);

        if (!response.success) {
            toast.error(response.message);
            return;
        }

        // Reset the message input
        newMessage.value = {
            timestamp: null, text: '', author: '', userType: ''
        };

        // Refresh revisions list
        await refreshRevisions();

        toast.success(response.message);
    } catch (error) {
        toast.error('Errore durante il salvataggio');
        console.error('Error saving revision:', error);
    }
}

function isReadonly() {
    return authData.UTYPE !== 'KA' && authData.UTYPE !== 'AMMINISTRATORE';
}

function calculateVitaObjPerc(type, level = '', personal = '') {

    let newRate = formData['vita' + type + level + 'Obj' + personal];
    if (! newRate) {
        formData['vita' + type + level + 'Incremento' + personal] = 0;
        formData['vita' + type + level + 'IncassiCur' + personal] = 0;
        return;
    }
    let base = formData['vitaAnnL1IncassiCur'];
    formData['vita' + type + level + 'Incremento' + personal] = base * newRate / 100;
    formData['vita' + type + level + 'IncassiCur' + personal] = base + formData['vita' + type + level + 'Incremento' + personal];
    checkObjAllowed('vita');
}

function calculateVitaObj() {
    // Check if vitaAnnL2IncassiCur is less than or equal to vitaAnnL1IncassiCur
    if (formData.vitaAnnL2IncassiCur <= formData.vitaAnnL1IncassiCur) {
        formData.vitaAnnL2Obj = null;
        return;
    }
    
    // Calculate the percentage increase
    const newObjective = ((formData.vitaAnnL2IncassiCur / formData.vitaAnnL1IncassiCur) * 100 - 100).toFixed(2);
    formData.vitaAnnL2Obj = parseFloat(newObjective);
    checkObjAllowed('vita');
}

function checkObjAllowed(type, personal = "") {

    // Check that obj is allowed and set max rappel
    let isAllowed = false;
    const slotsKey = type + 'Slots';
    const maxRappelKey = type + 'MaxRappel';
    const objAllowedKey = type + 'ObjAllowed';

    // Access the slots array
    const slots = type === 'vita' ? vitaSlots : RPSlots;

    if (slots && Array.isArray(slots)) {
        slots.forEach(function(value, key) {
            slots[key].selected = false;
            if (formData[type + 'AnnL2Obj' + personal] <= value.obj && !isAllowed) {
                isAllowed = true;
                formData[maxRappelKey] = value.rappelMax;
                slots[key].selected = true;
            }
        });
    }

    formData[objAllowedKey] = isAllowed;

    if (formData[type + 'AnnL2Obj' + personal] === 0) {
        return true;
    }

    return isAllowed;
}

function onChangeRPSlot() {
    showRPSlotsSelect.value = false;
    if ( confirm('Sei sicuro di voler cambiare la fascia dell\'Agenzia?') ) {
        accordo2Api.updateAgreement(props.agenzia_id, agreement.value)
            .then(res => {
                if (res.success) {
                    toast.success('Fascia aggiornata.');
                } else {
                    toast.error('Errore imprevisto.');
                }
            });
    }
}

function checkObjRPIsValid(value, type, personal = '') {

    if (!value) return true;

    if (personal) {
        errors.ramiPrefAnnL2IncassiCurPersonal = value <= formData['ramiPrefAnnL2IncassiCur']
            ? 'L\'obiettivo personalizzato deve essere maggiore dell\'Obiettivo Annuale 2° Livello.'
            : null;
    }
    if ( type === 'AnnL2' && ! personal) {
        errors.ramiPrefAnnL2IncassiCur = value <= formData['ramiPrefAnnL1IncassiCur']
            ? 'L\'Obiettivo Annuale 2° Livello deve essere maggiore dell\'Obiettivo Annuale 1° Livello.'
            : null;
    }
    if ( type === 'AnnL1' && ! personal) {
        errors.ramiPrefAnnL1IncassiCur = value <= formData['ramiPrefAnnL1IncassiPrev']
            ? 'L\'Obiettivo Annuale 1° Livello deve essere maggiore dell\'Incassi Emessi anno precedente.'
            : null;
    }

}

function calculateRPObj(type, personal) {
    let input = '';
    let target = '';
    let obj = '';
    if (personal) {
        input = 'ramiPrefAnnL2IncassiCurPersonal';
        target = 'ramiPrefAnnL2IncassiCur';
        obj = 'ramiPrefAnnL2ObjPersonal';
    }
    if ( type === 'AnnL2' && ! personal) {
        input = 'ramiPrefAnnL2IncassiCur';
        target = 'ramiPrefAnnL1IncassiCur';
        obj = 'ramiPrefAnnL2Obj';
    }
    if ( type === 'AnnL1' && ! personal) {
        input = 'ramiPrefAnnL1IncassiCur';
        target = 'ramiPrefAnnL1IncassiPrev';
        obj = 'ramiPrefAnnL1Obj';
    }
    // Lascio vuota l'aliquota se l'obiettivo personalizzato è minore dell'obiettivo anno precedente
    if (formData[input] <= formData[target]) {
        calculateIncrement(type, personal);
        return formData[obj] = null;
    }
    // Calcolo aliquota
    formData[obj] = ( formData[input] / formData['ramiPrefAnnL1IncassiPrev'] ) * 100 - 100;
    if (personal) {
        checkObjAllowed('ramiPref', 'Personal');
    }
    calculateIncrement(type, personal)
}

function calculateIncrement (type, personal = '') {
    // Calcolo la differenza tra obiettivo anno corrente (livello è dinamico) e incassi anno precedente
    let diff = formData[`ramiPref${type}IncassiCur${personal}`] - formData['ramiPrefAnnL1IncassiPrev'];

    // Se la differenza è negativa l'obiettivo non è valido
    if (diff <= 0) diff = 0;

    formData[`ramiPref${type}Incremento${personal}`] = diff;
}

watch(() => formData.ramiPrefSemObj, (value) => {
    calculateIncassoAndIncrement('Sem');
});

watch(() => formData.ramiPrefAnnL1Obj, (value) => {
    calculateIncassoAndIncrement('Ann', 'L1');
});

watch(() => formData.ramiPrefAnnL2Obj, (value) => {
    calculateIncassoAndIncrement('Ann', 'L2');
});

watch(() => formData.ramiPrefAnnL2ObjPersonal, (value) => {
    calculateIncassoAndIncrement('Ann', 'L2', 'Personal');
});

function calculateIncassoAndIncrement(type, level = '', personal = '') {
    let newRate = formData['ramiPref' + type + level + 'Obj' + personal];
    if (! newRate) {
        formData['ramiPref' + type + level + 'Incremento' + personal] = 0;
        formData['ramiPref' + type + level + 'IncassiCur' + personal] = 0;
        return;
    }
    let base = level ? formData['ramiPref' + type + 'L1IncassiPrev'] : formData['ramiPref' + 'SemIncassiPrev' + personal];
    formData['ramiPref' + type + level + 'Incremento' + personal] = base * newRate / 100;
    formData['ramiPref' + type + level + 'IncassiCur' + personal] = base + formData['ramiPref' + type + level + 'Incremento' + personal];
    checkObjAllowed('ramiPref', 'Personal');
}

function backToDetail() {
    router.push({
        name: 'detail',
        params: {
            year: selectedYear.value,
            agenzia_id: null,
            revisionId: null
        }
    });
    //router.push(`/accordo-economico/${selectedYear.value}/dettaglio`)
}

watch(() => formData, (newValue) => {
    if (newValue) {
        formatStatusChangeMessage(newValue.statusChangeMessage);
    }
}, {immediate: true});

watchEffect(() => {
    // This will automatically track all reactive dependencies
    const annL1 = formData.ramiPrefAnnL1IncassiCur;
    const annL2 = formData.ramiPrefAnnL2IncassiCur;
    const annL2Personal = formData.ramiPrefAnnL2IncassiCurPersonal;

    // Perform all calculations when any dependency changes
    calculateRPObj('AnnL1');
    checkObjRPIsValid(annL1, 'AnnL1');

    calculateRPObj('AnnL2');
    checkObjRPIsValid(annL2, 'AnnL2');

    calculateRPObj('AnnL2', 'Personal');
    checkObjRPIsValid(annL2Personal, 'AnnL2', 'Personal');
});

const statusLabels = [
    {id: 'DA_LAVORARE', label: 'Da lavorare', color: '#3E4154', available: 'ADMIN'},
    {id: 'IN_LAVORAZIONE', label: 'In lavorazione', color: '#737583', available: 'ADMIN|MANAGER'},
    {id: 'DA_RILAVORARE', label: 'Da rilavorare', color: '#B40011', available: 'ADMIN|COMPANY'},
    {id: 'ATTESA_RICALCOLO', label: 'Da ricalcolare', color: '#FF6100', available: 'ADMIN|MANAGER'},
    {id: 'SOTTOSCRITTO', label: 'Sottoscritto', color: '#018600', available: 'ADMIN|MANAGER'},
];

const checkAvailableState = function (status, formStatusValue) {
    let show = false;
    if (( authData.UTYPE === 'AREAMGR' || authData.UTYPE === 'DISTRICTMGR') && formStatusValue === 'ATTESA_RICALCOLO') {
        return show;
    }
    switch (authData.UTYPE) {
        case "KA":
            show = status.available.includes('ADMIN');
            break;
        case "AMMINISTRATORE":
            show = status.available.includes('ADMIN'); // old COMPANY
            break;
        case "AREAMGR":
        case "DISTRICTMGR":
            show = status.available.includes('MANAGER');
            break;
    }
    return show;
};

function checkRappelAllowed(value, type) {
    return value <= formData[type + 'MaxRappel'];
}

watchEffect(() => {
    // This will automatically track all reactive dependencies
    const annL1 = formData.ramiPrefAnnL1Rappel;
    const annL2 = formData.ramiPrefAnnL2Rappel;
    const annL2Personal = formData.ramiPrefAnnL2RappelPersonal;

    // Perform all calculations when any dependency changes
    errors.ramiPrefAnnL1Rappel = ! checkRappelAllowed(annL1, 'ramiPref')
        ? 'L\'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.'
        : null;
    errors.ramiPrefAnnL2Rappel = ! checkRappelAllowed(annL2, 'ramiPref')
        ? 'L\'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.'
        : null;
    errors.ramiPrefAnnL2RappelPersonal = ! checkRappelAllowed(annL2Personal, 'ramiPref')
        ? 'L\'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.'
        : null;

});

function formatTypeLabel(string) {
    switch (string) {
        case 'STANDARD': return 'Standard';
        case 'PERSONALIZZATO_RAMIPREF': return 'Pers. Rami Pref.';
        case 'PERSONALIZZATO_VITA': return 'Pers. Vita';
        case 'PERSONALIZZATO_COMBO': return 'Pers. Combo';
        default: return '-';
    }
}

function downloadAgencyHistory() {
    window.location.href = '/api/apps/accordo2/exportStatus?agency=' + props.agenzia_id + '&year=' + props.year;
}

async function refreshRevisions() {
    try {
        // Fetch updated revisions
        revisions.value = await accordo2Api.revisions(props.revisionId);

        // Transform revisions data
        revisions.value = revisions.value.map((revision, index) => ({
            ...revision,
            label: index === 0
                ? `Ultima versione - ${revision.mode}`
                : `${revision.mode} - ${formatDatetime(revision.updatedAt)}`
        }));

        // Set the selected revision to the first one (latest)
        selectedRevisionId.value = revisions.value[0]?.id;

        // Update the URL to reflect the new revision ID
        await router.push({
            name: 'profile',
            params: {
                ...route.params,
                revisionId: selectedRevisionId.value
            }
        });
    } catch (error) {
        toast.error('Errore durante l\'aggiornamento delle revisioni');
        console.error('Error refreshing revisions:', error);
    }
}
</script>

<template>
    <Dialog v-model:visible="showAgencyInfoModal" modal :header="props.agenzia_id" :style="{ width: '25rem' }">
        <div class="mb-3">Agenzia: <strong>{{agencyInfo.localita}}</strong></div>
        <div class="mb-3">Provincia: <strong>{{agencyInfo.provincia}}</strong></div>
        <div class="mb-3">Area: <strong>{{areaInfo.nome}}</strong></div>
        <div class="mb-3">District Manager: <strong>{{districtInfo.nome}}</strong></div>
        <div class="mb-3">OBJ Sviluppo: <StatusPillFormatter :label="selectedRevision?.status" /></div>
        <div class="mb-5">OBJ Personalizzato: <strong>{{formatTypeLabel(agreement.type)}}</strong></div>
        <div class="flex justify-center">
            <a :href="'/api/apps/accordo2/exportStatus?agency=' + props.agenzia_id + '&year=' + props.year" class="btn primary"><PhScroll size="22" class="inline mr-1" />Scarica la Storia</a>
        </div>
    </Dialog>
    <div class="container">
        <div class="card">
            <div class="card-body">
                <button type="button" class="btn secondary" @click="backToDetail"><PhArrowLeft size="22" /> Torna a dettaglio</button>
            </div>

            <div class="card-header !pt-0">
                <div class="title">{{props.agenzia_id}} - Scheda Accordo Economico {{selectedYear}} <PhInfo @click="showAgencyInfoModal = true" size="28" class="inline cursor-pointer"/></div>
            </div>

            <div class="card-body">
                <div class="flex gap-5 items-center mb-14">
                    <div class="form-group !mb-0">
                        <select class="!w-fit" v-model="selectedRevisionId" @change="onChangeRevision">
                            <option v-for="revision in revisions" :key="revision.id" :value="revision.id">{{ revision.label }}</option>
                        </select>
                    </div>
                    <div class="text-lg font-bold text-danger">Deadline sottoscrizione: 16/9/2025</div>
                </div>

                <div class="mb-10">
                    <div class="text-2xl text-primary mb-5">Nuova produzione TCM Vita</div>

                    <div class="grid grid-cols-2 gap-5 max-w-[1000px]">
                        <div class="col-span-1">
                            <div class="text-lg font-bold mb-5">Obiettivo annuale 1° livello</div>

                            <!-- Vita 1° livello - Base Calcolo Obiettivo -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Base Calcolo Obiettivo (<a v-tooltip="'Scarica Excel Base Calcolo Obiettivo'" class="font-bold" target="_blank" :href="`/api/apps/accordo2/base?id=${props.agenzia_id}&year=${props.year}`">Excel</a>)</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            €
                                            <InputNumber
                                                v-model="formData.vitaBaseCalcoloObj"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="0"
                                                :maxFractionDigits="0"
                                                locale="it-IT"
                                                :readonly="isReadonly()"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold']
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vita 1° livello - Numero Polizze -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Numero Polizze</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            <InputNumber
                                                v-model="formData.vitaAnnL1ObjPezzi"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="0"
                                                :maxFractionDigits="0"
                                                locale="it-IT"
                                                :readonly="isReadonly()"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold']
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vita 1° livello - Obiettivo al 31/12 -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Obiettivo al 31/12/{{props.year}}</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            €
                                            <InputNumber
                                                v-model="formData.vitaAnnL1IncassiCur"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="0"
                                                :maxFractionDigits="0"
                                                locale="it-IT"
                                                :readonly="isReadonly()"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold']
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vita 1° livello - Aliquota Rappel -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Aliquota Rappel</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            %
                                            <InputNumber
                                                v-model="formData.vitaAnnL1Rappel"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                locale="it-IT"
                                                :readonly="isReadonly()"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold']
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-span-1">
                            <div class="text-lg font-bold mb-5">Obiettivo annuale personalizzato <PhInfo @click="showVitaSlots = true" size="24" class="inline cursor-pointer"/></div>

                            <div v-if="showVitaSlots" class="inset-shadow p-5 mb-5">
                                <PhX size="28" class="absolute right-3 top-3 cursor-pointer" @click="showVitaSlots = false" />
                                <div class="font-bold mb-5">Fascia Agenzia: {{agreement.fasciaVita}}</div>
                                <table>
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th>Fino a Obj %</th>
                                        <th>Rappel % max</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="row in vitaSlots" :key="row.pers" :class="{'highlight' : row.selected }">
                                        <td>PERS. {{row.pers}}</td>
                                        <td>{{formatNumber(row.obj)}}</td>
                                        <td>{{formatNumber(row.rappelMax)}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Vita Personalizzato - Aliquota Obiettivo -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Aliquota Obiettivo</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            %
                                            <InputNumber
                                                v-model="formData.vitaAnnL2Obj"
                                                @input="(e) => (formData.vitaAnnL2Obj = e.value)"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                locale="it-IT"
                                                :readonly="isReadonly()"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold']
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vita Personalizzato - Obiettivo al 31/12 -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Obiettivo al 31/12/{{props.year}}</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            €
                                            <InputNumber
                                                v-model="formData.vitaAnnL2IncassiCur"
                                                @input="(e) => (formData.vitaAnnL2IncassiCur = e.value)"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="0"
                                                :maxFractionDigits="0"
                                                locale="it-IT"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold', { 'has-error': errors.vitaAnnL2IncassiCur }]
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-error" v-if="errors.vitaAnnL2IncassiCur">
                                    {{ errors.vitaAnnL2IncassiCur }}
                                </div>
                            </div>

                            <!-- Vita Personalizzato - Aliquota Rappel -->
                            <div class="mb-2">
                                <div class="flex items-start justify-between">
                                    <div>Aliquota Rappel</div>
                                    <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                        <div class="flex items-center gap-2">
                                            %
                                            <InputNumber
                                                v-model="formData.vitaAnnL2ExtraRappel"
                                                @input="(e) => (formData.vitaAnnL2ExtraRappel = e.value)"
                                                class="max-w-[100px]"
                                                :useGrouping="true"
                                                :minFractionDigits="2"
                                                :maxFractionDigits="2"
                                                locale="it-IT"
                                                :pt="{
                                                    pcInputText: {
                                                        root: ['text-right font-bold', { 'has-error': errors.vitaAnnL2ExtraRappel }]
                                                    }
                                                }"
                                            />
                                        </div>
                                    </div>
                                </div>
                                <div class="form-error" v-if="errors.vitaAnnL2ExtraRappel">
                                    {{ errors.vitaAnnL2ExtraRappel }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-10">

                    <div class="text-2xl text-primary mb-5 flex items-center">
                        Incassi RP {{selectedYear}} Fascia
                        <span class="ml-1" v-if="!showRPSlotsSelect">{{agreement.fasciaRamiPref}}</span>
                        <select v-if="showRPSlotsSelect" v-model="agreement.fasciaRamiPref" @change="onChangeRPSlot">
                            <option v-for="slot in slotsList" :key="slot.id" :value="slot.name">{{slot.name}}</option>
                        </select>
                        <button type="button" class="btn compact secondary btn-icon ml-2" v-tooltip="'Cambia fascia'" @click="showRPSlotsSelect = !showRPSlotsSelect"><PhSwap size="20" class="inline" /></button>
                    </div>

                    <div class="grid grid-cols-5 gap-x-5 gap-y-2">

                        <div class="col-span-3"></div>
                        <div class="col-span-2">
                            <div class="inset-shadow p-5 mb-5" v-if="showRPSlots">
                                <PhX size="28" class="absolute right-3 top-3 cursor-pointer" @click="showRPSlots = false" />
                                <div class="font-bold mb-5">Fascia Agenzia: {{agreement.fasciaRamiPref}}</div>
                                <table>
                                    <thead>
                                    <tr>
                                        <th></th>
                                        <th>Fino a Obj %</th>
                                        <th>Rappel % max</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for="row in RPSlots" :key="row.pers" :class="{'highlight' : row.selected }">
                                        <td>PERS. {{row.pers}}</td>
                                        <td>{{formatNumber(row.obj)}}</td>
                                        <td>{{formatNumber(row.rappelMax)}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- RP - Intestazioni -->
                        <div class="col-span-1 flex items-center">
                            <div class="text-lg font-bold">Obiettivo</div>
                        </div>
                        <div class="col-span-1">
                            <div class="font-bold leading-tight text-right">Semestrale<br><span class="text-sm">al 30/06/{{selectedYear}}</span></div>
                        </div>
                        <div class="col-span-1">
                            <div class="font-bold leading-tight text-right">Annuale 1° liv.<br><span class="text-sm">al 31/12/{{selectedYear}}</span></div>
                        </div>
                        <div class="col-span-1">
                            <div class="font-bold leading-tight text-right">Annuale 2° liv.<br><span class="text-sm">al 31/12/{{selectedYear}}</span></div>
                        </div>
                        <div class="col-span-1">
                            <div class="font-bold leading-tight text-right">Annuale Pers.<br><span class="text-sm">al 31/12/{{selectedYear}}</span> <PhInfo @click="showRPSlots = true" size="24" class="inline cursor-pointer"/></div>
                        </div>

                        <!-- RP - Incassi anno precedente -->
                        <div class="col-span-1">Incassi RP emessi {{selectedYear - 1}}</div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefSemIncassiPrev"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL1IncassiPrev"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1"></div>
                        <div class="col-span-1"></div>

                        <!-- RP - Aliquota Obiettivo -->
                        <div class="col-span-1">Aliquota Obiettivo</div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefSemObj"
                                        @input="(e) => (formData.ramiPrefSemObj = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL1Obj"
                                        @input="(e) => (formData.ramiPrefAnnL1Obj = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2Obj"
                                        @input="(e) => (formData.ramiPrefAnnL2Obj = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2ObjPersonal"
                                        @input="(e) => (formData.ramiPrefAnnL2ObjPersonal = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- RP - Obiettivo incremento -->
                        <div class="col-span-1">Obiettivo di incremento R.P.</div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefSemIncremento"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL1Incremento"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2Incremento"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2IncrementoPersonal"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>

                        <!-- RP - Incassi anno corrente -->
                        <div class="col-span-1">Incassi RP emessi anno corrente</div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefSemIncassiCur"
                                        @input="(e) => (formData.ramiPrefSemIncassiCur = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL1IncassiCur"
                                        @input="(e) => (formData.ramiPrefAnnL1IncassiCur = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL1IncassiCur }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL1IncassiCur">
                                    {{ errors.ramiPrefAnnL1IncassiCur }}
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2IncassiCur"
                                        @input="(e) => (formData.ramiPrefAnnL2IncassiCur = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL2IncassiCur }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL2IncassiCur">
                                    {{ errors.ramiPrefAnnL2IncassiCur }}
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    €
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2IncassiCurPersonal"
                                        @input="(e) => (formData.ramiPrefAnnL2IncassiCurPersonal = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="0"
                                        :maxFractionDigits="0"
                                        locale="it-IT"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL2IncassiCurPersonal }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL2IncassiCurPersonal">
                                    {{ errors.ramiPrefAnnL2IncassiCurPersonal }}
                                </div>
                            </div>
                        </div>

                        <!-- RP - Rappel -->
                        <div class="col-span-1">Aliquota Rappel</div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefSemRappel"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :readonly="isReadonly()"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold']
                                            }
                                        }"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL1Rappel"
                                        @input="(e) => (formData.ramiPrefAnnL1Rappel = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL1Rappel }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL1Rappel">
                                    {{ errors.ramiPrefAnnL1Rappel }}
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2Rappel"
                                        @input="(e) => (formData.ramiPrefAnnL2Rappel = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL2Rappel }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL2Rappel">
                                    {{ errors.ramiPrefAnnL2Rappel }}
                                </div>
                            </div>
                        </div>
                        <div class="col-span-1">
                            <div class="form-group flex flex-col items-end gap-1 !mb-0">
                                <div class="flex items-center gap-2">
                                    %
                                    <InputNumber
                                        v-model="formData.ramiPrefAnnL2RappelPersonal"
                                        @input="(e) => (formData.ramiPrefAnnL2RappelPersonal = e.value)"
                                        class="max-w-[100px]"
                                        :useGrouping="true"
                                        :minFractionDigits="2"
                                        :maxFractionDigits="2"
                                        locale="it-IT"
                                        :pt="{
                                            pcInputText: {
                                                root: ['text-right font-bold', { 'has-error': errors.ramiPrefAnnL2RappelPersonal }]
                                            }
                                        }"
                                    />
                                </div>
                                <div class="form-error" v-if="errors.ramiPrefAnnL2RappelPersonal">
                                    {{ errors.ramiPrefAnnL2RappelPersonal }}
                                </div>
                            </div>
                        </div>

                    </div>

                </div>

                <div>

                    <div class="text-2xl text-primary mb-5">Stato della lavorazione</div>
                    <div class="flex flex-wrap gap-4 mb-5">
                        <template v-for="status in statusLabels">
                            <div class="flex items-center gap-1" v-if="checkAvailableState(status, formData.status)">
                                <RadioButton v-model="formData.status" :inputId="status.id" name="status" :value="status.id" size="large" />
                                <label :for="status.id" class="text-lg cursor-pointer">
                                    <span class="detail-label border-2 border-transparent" :class="[{ 'border-2 border-primary' : formData.status === status.id }, formData.status === status.id ? 'opacity-full' : 'opacity-50']" :style="{'background-color' : status.color}">{{status.label}}</span>
                                </label>
                            </div>

                        </template>
                    </div>

                    <div class="text-lg font-bold text-danger mb-3">Indicare ed aggiornare tempestivamente lo stato della lavorazione.</div>

                    <div class="mb-3">È possibile effettuare e salvare variazioni parziali usando il pulsante “SALVA” (il DM vedrà comunque le modifiche parziali). Completate le operazioni e definiti i nuovi valori, selezionare lo status “DA RILAVORARE” e poi cliccare su “SALVA” (il comando lancia anche una e-mail di avvertimento verso il DM e l’AM di competenza).</div>

                    <div v-if="formData.year < 2022">
                        <div class="form-group">
                            <textarea name="text" rows="6" v-model="formData.statusChangeMessage" placeholder="Scrivi qui il tuo messaggio" />
                        </div>
                    </div>
                    <div v-else>
                        <div v-if="formData.statusChangeMessage.length" v-for="message in formData.statusChangeMessage" :key="message.id">
                            <div class="feed-container mb-5" :class="{ 'ml-5' : message.userType !== 'AMMINISTRATORE' }">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <strong>{{message.author}}</strong> - {{ message.timestamp}}
                                    </div>
                                </div>
                                <div class="mt-2 inset-shadow p-3 w-fit">
                                    {{message.text}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-8">
                            <textarea class="w-max"  rows="6" v-model="newMessage.text" placeholder="Scrivi qui il tuo messaggio" />
                        </div>
                    </div>

                </div>

            </div>

            <div class="card-body flex justify-between border-t">
                <a target="_blank" :href="`/api/apps/accordo2/pdf/stampa.html?agreementId=${agreement.id}`" type="button" class="btn primary"><PhPrinter :size="22" class="inline mr-1" />Stampa</a>
                <button type="button" class="btn success" :disabled="hasErrors" @click="onAgencyDetailSubmit()">
                    <PhFloppyDiskBack :size="22" class="inline mr-1" /> Salva
                </button>
            </div>

        </div>
    </div>
</template>

<style scoped>
.form-error {
    text-align: right;
}
.inset-shadow {
    @apply rounded-xl relative;
    background-color: #fbfbfb;
    box-shadow: inset 0px 0px 10px #00000029;

    tr.highlight {
        background-color: #d7edd2;
        td {
            border: 2px solid #b6b6b6 !important;
            @apply font-bold;
        }
    }

    td {
        border: 1px solid #b6b6b6 !important;
    }
}
.detail-label {
    @apply rounded-3xl text-white px-3 py-1;
}
</style>
