<script setup>

    import {ref, watch, inject} from "vue";
    import {<PERSON><PERSON>loppyDisk, PhPlus, PhX, PhArrowRight} from "@phosphor-icons/vue";
    import InputNumber from "primevue/inputnumber";
    import {accordo2Api} from "@/apps/accordo-economico/accordo2Api.js";

    const selectedYear = inject('selectedYear');

    // Convert to refs for proper reactivity
    let setupObjData = ref(null);
    let tables = ref({
        rp: [],
        life: []
    });

    // Initial data loading
    async function loadInitialData() {
        try {
            setupObjData = await accordo2Api.setupObj(selectedYear.value);
            createTables(setupObjData);
        } catch (error) {
            console.error("Error loading setupObj data:", error);
        }
    }
    await loadInitialData();

    function createTables(data)
    {
        tables.value = {
            rp: [],
            life: [],
        };

        if (data && data.rp) {
            const keys = Object.keys(data.rp);

            if (keys.length > 0 && Array.isArray(data.rp[keys[0]])) {
                const numRows = data.rp[keys[0]].length;

                for (let i = 0; i < numRows; i++) {
                    let currentRow = {};
                    for (const key of keys) {
                        if (Object.hasOwnProperty.call(data.rp, key) && Array.isArray(data.rp[key])) {
                            if (data.rp[key][i] !== undefined) {
                                currentRow[key] = data.rp[key][i];
                            } else {
                                currentRow[key] = null;
                            }
                        }
                    }
                    tables.value.rp.push(currentRow);
                }
            }
        }

        if (data && data.life) {
            const keys = Object.keys(data.life);

            if (keys.length > 0 && Array.isArray(data.life[keys[0]])) {
                const numRows = data.life[keys[0]].length;

                for (let i = 0; i < numRows; i++) {
                    let currentRow = {};
                    for (const key of keys) {
                        if (Object.hasOwnProperty.call(data.life, key) && Array.isArray(data.life[key])) {
                            if (data.life[key][i] !== undefined) {
                                currentRow[key] = data.life[key][i];
                            } else {
                                currentRow[key] = null;
                            }
                        }
                    }
                    tables.value.life.push(currentRow);
                }
            }
        }
    }

    function rebuildData(tables) {
        const result = {
            rp: {},
            life: {},
        };

        function transform(tableArray) {
            const columnData = {};

            if (tableArray.length === 0) return columnData;

            const keys = Object.keys(tableArray[0]);

            for (const key of keys) {
                columnData[key] = tableArray.map(row => row[key] !== undefined ? row[key] : null);
            }

            return columnData;
        }

        if (tables && Array.isArray(tables.rp)) {
            result.rp = transform(tables.rp);
        }

        if (tables && Array.isArray(tables.life)) {
            result.life = transform(tables.life);
        }

        return result;
    }

    function getNextLetter()
    {
        let objectLength =  Object.keys(setupObjData.rp.takings).length - 1;
        let lastItemKey = Object.keys(setupObjData.rp.takings)[objectLength];
        let currentLetter = setupObjData.rp.takings[lastItemKey].name.toUpperCase();
        // Mysterious script capable of getting next letter in alphabet
        return currentLetter.substring(0, currentLetter.length - 1) + String.fromCharCode(currentLetter.charCodeAt(currentLetter.length - 1) + 1);
    }

    function getLowerBound(type)
    {
        let objectLength =  Object.keys(setupObjData[type].takings).length - 1;
        return setupObjData[type].takings[objectLength].upperBound;
    }

    function createStartingRow(type)
    {
        let emptyObj = {obj: 0, rappel: 0};
        setupObjData[type].takings.push({name: 0, lowerBound: 0, upperBound: 0});
        let i = 1;
        while (i < 5) {
            setupObjData[type]['custom' + i] = [];
            setupObjData[type]['custom' + i].push(emptyObj);
            i++;
        }
    }

    const addRow = function(type)
    {
        if (! setupObjData[type].takings.length) {
            createStartingRow(type);
            createTables(setupObjData);
            return;
        }
        // Forced to make a switch because of dumb naming logic
        switch (type) {
            case 'rp':
                // This if is necessary because first row always has 0 as name, after that name starts having letters.
                if (setupObjData.rp.takings.length === 1) {
                    // Push new taking entry with dynamic values
                    setupObjData.rp.takings.push({name: 'A', lowerBound: getLowerBound('rp'), upperBound: getLowerBound('rp')});
                }
                else {
                    // Push new taking entry with dynamic values
                    setupObjData.rp.takings.push({name: getNextLetter(), lowerBound: getLowerBound('rp'), upperBound: getLowerBound('rp')});
                }

                // Dynamically index property names by iterating object entries
                Object.entries(setupObjData.rp).forEach(item => {
                    // Skip takings which has different logic
                    if ( item[0] !== 'takings' ) {
                        setupObjData.rp[item[0]].push({ obj: 0,rappel: 0 });
                    }
                });
                break;
            case 'life':
                // Push new taking entry with dynamic values
                setupObjData.life.takings.push({name: setupObjData.life.takings.length.toString(), lowerBound: getLowerBound('life'), upperBound: getLowerBound('life')});
                Object.entries(setupObjData.life).forEach(item => {
                    // Skip takings which has different logic
                    if ( item[0] !== 'takings' ) {
                        setupObjData.life[item[0]].push({ obj: 0,rappel: 0 });
                    }
                });
                break;
        }
        createTables(setupObjData);
    };

    const removeRow = function(type) {
        // Dynamically index property names by iterating object entries
        Object.entries(setupObjData[type]).forEach(item => {
            setupObjData[type][item[0]].pop();
        });
        createTables(setupObjData);
    };

    async function saveSetup() {
        const res = await accordo2Api.saveSetupObj(selectedYear.value, rebuildData(tables.value));
        if (! res.data.data) {
            alert('Errore imprevisto.');
            //$growl.error('Errore imprevisto.', { icon: 'icon-close', ttl: 2000 });
            return;
        }
        alert('Dati salvati.');
        //$growl.success('Dati salvati.', { icon: 'icon-close', ttl: 2000 });
    }

    // Watch for year changes
    watch(selectedYear, async (newYear) => {
        await loadInitialData();
    }, { immediate: true });
</script>

<template>
    <div class="container">

        <div class="card">

            <!-- RAMI PREFERITI -->
            <div class="card-header">
                <div class="title title-sm !font-normal">
                    <div>Setup Obiettivo</div>
                    <div class="subtitle">Rami preferiti</div>
                </div>
            </div>

            <div class="card-body body-fit">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                        <tr>
                            <th colspan="3">INCASSO RAMI DANNI</th>
                            <th colspan="3">PERSONALIZZATO 1</th>
                            <th colspan="3">PERSONALIZZATO 2</th>
                            <th colspan="3">PERSONALIZZATO 3</th>
                            <th colspan="3">PERSONALIZZATO 4</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th>Da &euro;</th>
                            <th>A &euro;</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                        </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(row, index) in tables.rp" :key="index">
                                <td>FASCIA {{ row.takings.name }}</td>
                                <td v-if="index !== tables.rp.length - 1 || tables.rp.length === 1">
                                    <InputNumber
                                            v-model="row.takings.lowerBound"
                                            inputId="x"
                                            class="max-w-[100px]"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td v-if="index !== tables.rp.length - 1 || tables.rp.length === 1">
                                    <InputNumber
                                            v-model="row.takings.upperBound"
                                            inputId="x"
                                            class="max-w-[100px]"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td colspan="2" v-if="index === tables.rp.length - 1 && tables.rp.length > 1">
                                    Oltre il limite fascia
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom1.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom1.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom2.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom2.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom3.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom3.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom4.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom4.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex gap-x-2 p-5">
                    <button class="btn secondary" type="button" @click="addRow('rp')">
                        <PhPlus :size="22" class="inline mr-1" /> Aggiungi Fascia
                    </button>
                    <button class="btn secondary" type="button" @click="removeRow('rp')" :disabled="tables.rp.length <= 1">
                        <PhX :size="22" class="inline mr-1" /> Elimina Fascia
                    </button>
                </div>

            </div>

            <!-- VITA -->
            <div class="card-header">
                <div class="title title-sm !font-normal">
                    <div class="subtitle">Vita</div>
                </div>
            </div>

            <div class="card-body body-fit">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                        <tr>
                            <th colspan="3">NP VITA</th>
                            <th colspan="3">PERSONALIZZATO 1</th>
                            <th colspan="3">PERSONALIZZATO 2</th>
                            <th colspan="3">PERSONALIZZATO 3</th>
                            <th colspan="3">PERSONALIZZATO 4</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th>Da &euro;</th>
                            <th>A &euro;</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                            <th>Fino a<br/>Obj %</th>
                            <th></th>
                            <th>Rappel<br/>% max</th>
                        </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(row, index) in tables.life" :key="index">
                                <td>FASCIA {{ row.takings.name }}</td>
                                <td v-if="index !== tables.life.length - 1 && tables.life.length > 1">
                                    <InputNumber
                                            v-model="row.takings.lowerBound"
                                            inputId="x"
                                            class="max-w-[100px]"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td v-if="index !== tables.life.length - 1 && tables.life.length > 1">
                                    <InputNumber
                                            v-model="row.takings.upperBound"
                                            inputId="x"
                                            class="max-w-[100px]"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td colspan="2" v-if="index === tables.life.length - 1 && tables.life.length > 1">
                                    Oltre il limite fascia
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom1.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom1.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom2.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom2.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom3.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom3.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>

                                <td>
                                    <InputNumber
                                            v-model="row.custom4.obj"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                                <td class="arrow"><PhArrowRight :size="22" /></td>
                                <td>
                                    <InputNumber
                                            v-model="row.custom4.rappel"
                                            inputId="minmaxfraction"
                                            class="max-w-[70px]"
                                            :minFractionDigits="2"
                                            :maxFractionDigits="5"
                                            :pt="{
                                                pcInputText: {
                                                    root: 'text-center'
                                                }
                                            }"
                                            fluid
                                    />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex gap-x-2 p-5">
                    <button class="btn secondary" type="button" @click="addRow('life')">
                        <PhPlus :size="22" class="inline mr-1" /> Aggiungi Fascia
                    </button>
                    <button class="btn secondary" type="button" @click="removeRow('life')" :disabled="tables.life.length <= 1">
                        <PhX :size="22" class="inline mr-1" /> Elimina Fascia
                    </button>
                </div>
            </div>

            <div class="card-footer flex justify-end">
                <button class="btn success" type="button" @click="saveSetup()">
                    <PhFloppyDisk :size="22" class="inline mr-1" /> Salva
                </button>
            </div>

        </div>

    </div>
</template>

<style scoped>
    th {
        text-align: center
    }
    .subtitle {
        color: #005A9C;
        padding-top: 1.5rem;
    }
    .card-footer {
        border-top: 1px solid #e4e6ec;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        padding-top: 1.5rem;
        padding-bottom: 2.5rem;
    }
    td {
        padding: 10px 0;
        text-align: center;
        @apply font-bold;
    }
    td input {
        width: 70px !important;
        text-align: center;
        border: 1px solid #e4e6ec;
    }
    td.arrow {
        padding: 0;
        width: 22px;
        text-align: center;
    }
</style>
