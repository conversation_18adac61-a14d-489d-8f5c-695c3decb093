<script>
import {KeyIcon, ArrowLongUpIcon, ArrowLongDownIcon, ChevronUpDownIcon} from "@heroicons/vue/24/outline/index.js";
import {
    TransitionRoot,
    TransitionChild,
    Dialog,
    DialogPanel,
    DialogTitle,
    Combobox, ComboboxInput, ComboboxOptions, ComboboxOption, ComboboxButton
} from '@headlessui/vue'
import {Field} from "vee-validate";
import {usersApi} from "@/_common/api/users.js";
import axios from "axios";
import {useUserStore} from "@/_common/stores/user.js";
import {ref} from "vue";

export default {
    name: "Ghost",
    components: {
        KeyIcon, ArrowLongUpIcon, ArrowLongDownIcon, ChevronUpDownIcon,
        TransitionRoot,TransitionChild,
        Dialog, DialogPanel, DialogTitle,
        Combobox, ComboboxInput, ComboboxOptions, ComboboxOption, ComboboxButton, Field
    },
    data() {
        return {
            isOpen: false,
            selectedUser: null,
            selectedSuperUser: null,
            selectedType: null,
            filteredUsers: null,
            filteredSuperUsers: null,
            originalSuperUsers: null,
            authData: ref(useUserStore()),
            userTypes: [
                {
                    id: 'AMMINISTRATORE',
                    label: 'Amministratore'
                },
                {
                    id: 'DIREZ',
                    label: 'Direzione'
                },
                {
                    id: 'AREAMGR',
                    label: 'Area Manager'
                },
                {
                    id: 'DISTRICTMGR',
                    label: 'District Manager'
                },
                {
                    id: 'ASV',
                    label: 'ASV'
                },
                {
                    id: 'FORMAZIONE',
                    label: 'Formazione'
                },
                {
                    id: 'AREAMGR_COLLAB',
                    label: 'Collaboratore AM'
                },
                {
                    id: 'BANCHE',
                    label: 'Banche'
                },
                {
                    id: 'IVASS',
                    label: 'Ivass'
                },
                {
                    id: 'MYPAGE',
                    label: 'My Page'
                }
            ],
            users: []
        }
    },
    watch: {
        selectedUser(newValue) {
            if (newValue) {
                this.loginGhost(newValue.id);
            }
        },
        selectedSuperUser(newValue) {
            if (newValue) {
                this.loginGhost(newValue.id);
            }
        }
    },
    methods: {
        toggleModal: function () {
            this.isOpen = !this.isOpen
        },
        onTyping (value) {
            if (value.length < 3) {
                return
            }

            // Annullo timeout
            clearTimeout(this._timer)

            // Creo nuovo timeout
            this._timer = setTimeout(() => {
                this.getFilteredData(value)
            }, 500)

        },
        onTypingSuper (value) {
            if (!value) {
                this.filteredSuperUsers = this.originalSuperUsers
            }
            this.filteredSuperUsers = this.filteredSuperUsers.filter(user => {
                return user.cognome.toLowerCase().includes(value.toLowerCase())
            })
        },
        resetSuperUser () {
            this.filteredSuperUsers = this.originalSuperUsers
        },
        async onTypeSelection (selectedType) {
            const resp = await usersApi.searchByType(selectedType)
            this.originalSuperUsers = resp.data;
            this.filteredSuperUsers = this.originalSuperUsers;
        },
        async getFilteredData(query) {
            const resp = await usersApi.ghostSearch(query)
            this.filteredUsers = resp.data;
        },
        async loginGhost(userId) {
            const resp = await axios.post(`/auth/loginGhost`, {id: userId});
            if (resp.data.success) {
                window.location.href = '/';
            }
        }
    }
}
</script>

<template>
    <div class="toggle" @click="toggleModal">
        <KeyIcon class="size-6" />
    </div>

    <TransitionRoot appear :show="isOpen" as="template">
        <Dialog as="div" @close="toggleModal" class="relative z-10">
            <TransitionChild
                as="template"
                enter="duration-300 ease-out"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="duration-200 ease-in"
                leave-from="opacity-100"
                leave-to="opacity-0"
            >
                <div class="fixed inset-0 bg-black/25" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-y-auto">
                <div
                    class="flex min-h-full items-center justify-center p-4 text-center"
                >
                    <TransitionChild
                        as="template"
                        enter="duration-300 ease-out"
                        enter-from="opacity-0 scale-95"
                        enter-to="opacity-100 scale-100"
                        leave="duration-200 ease-in"
                        leave-from="opacity-100 scale-100"
                        leave-to="opacity-0 scale-95"
                    >
                        <DialogPanel
                            class="w-full max-w-xl transform rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all"
                        >
                            <DialogTitle as="h3" class="text-lg font-bold leading-6 text-gray-900">👻 Ghost</DialogTitle>

                            <div class="mt-2">

                                <p class="text-sm">Digitare il cognome dell'utente o il codice Agenzia.</p>
                                <p class="text-sm">
                                    È possibile navigare i risultati con <kbd class="inline-flex rounded-md bg-gray-700 px-2 py-0.5 text-xs text-white last-of-type:mr-[0.5ch]"><ArrowLongUpIcon class="h-3 text-white" /></kbd> <kbd class="inline-flex rounded-md bg-gray-700 px-2 py-0.5 text-xs text-white last-of-type:mr-[0.5ch]"><ArrowLongDownIcon class="h-3 text-white" /></kbd> della tastiera.
                                </p>
                                <p class="text-sm mb-3">Premere <kbd class="inline-flex rounded-md bg-gray-700 px-2 py-0.5 text-xs text-white last-of-type:mr-[0.5ch]">Esc</kbd> per annullare la selezione, <kbd class="inline-flex rounded-md bg-gray-700 px-2 py-0.5 text-xs text-white last-of-type:mr-[0.5ch]">Invio</kbd> per confermarla.</p>

                                <div v-if="authData.UTYPE === 'KA'" class="mb-1">
                                    <div class="grid grid-cols-4 gap-2">
                                        <div class="col-span-4 sm:col-span-2">
                                            <div class="form-group">
                                                <label for="selectedType">
                                                    Ruolo
                                                </label>
                                                <Field v-slot="{ value }" v-model="selectedType" name="selectedType" as="select" @change="onTypeSelection($event.target.value)">
                                                    <option v-for="userType in userTypes" :key="userType.id" :value="userType.id" :selected="userType.id">{{ userType.label }}</option>
                                                </Field>
                                            </div>
                                        </div>
                                        <div class="col-span-4 sm:col-span-2">
                                            <div class="form-group">
                                                <label for="selectedType">
                                                    Utente
                                                </label>
                                                <Combobox as="div" v-model="selectedSuperUser" class="relative rounded-lg bg-white border" :disabled="!selectedType">
                                                    <ComboboxInput
                                                        class="w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0"
                                                        @change="onTypingSuper($event.target.value)"/>
                                                    <ComboboxButton
                                                        class="absolute inset-y-0 right-0 flex items-center pr-2"
                                                    >
                                                        <ChevronUpDownIcon
                                                            class="h-5 w-5 text-gray-400"
                                                            aria-hidden="true"
                                                        />
                                                    </ComboboxButton>

                                                    <transition
                                                        enter-active-class="transition duration-100 ease-out"
                                                        enter-from-class="transform scale-95 opacity-0"
                                                        enter-to-class="transform scale-100 opacity-100"
                                                        leave-active-class="transition duration-75 ease-out"
                                                        leave-from-class="transform scale-100 opacity-100"
                                                        leave-to-class="transform scale-95 opacity-0"
                                                        @after-leave="resetSuperUser()"
                                                    >
                                                        <ComboboxOptions class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white p-2 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-10">

                                                            <div v-if="filteredSuperUsers?.length === 0" class="relative cursor-default select-none px-3 py-1.5 text-gray-700">
                                                                Nessun risultato
                                                            </div>

                                                            <ComboboxOption
                                                                as="div"
                                                                v-for="user in filteredSuperUsers"
                                                                :key="user"
                                                                :value="user"
                                                                class="px-3 py-1.5 mb-1 ui-active:bg-blue-500 ui-active:text-white ui-active:rounded-md border-b border-b-gray-300"
                                                            >
                                                                <div>{{ user.nome }} {{user.cognome}}</div>
                                                                <div class="flex text-sm">
                                                                    <div v-if="user.type === 'AREAMGR'">Area {{ user.area }}</div>
                                                                    <div v-if="user.type === 'DISTRICTMGR'">Area {{ user.area }} - District {{user.district}}</div>
                                                                </div>
                                                            </ComboboxOption>

                                                        </ComboboxOptions>

                                                    </transition>

                                                </Combobox>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="">Agente</label>
                                    <Combobox as="div" v-model="selectedUser" class="relative rounded-lg bg-white border">
                                        <ComboboxInput
                                            class="w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0"
                                            @change="onTyping($event.target.value)"/>

                                        <transition
                                            enter-active-class="transition duration-100 ease-out"
                                            enter-from-class="transform scale-95 opacity-0"
                                            enter-to-class="transform scale-100 opacity-100"
                                            leave-active-class="transition duration-75 ease-out"
                                            leave-from-class="transform scale-100 opacity-100"
                                            leave-to-class="transform scale-95 opacity-0"
                                        >

                                            <ComboboxOptions class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white p-2 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-10">

                                                <div v-if="filteredUsers?.length === 0" class="relative cursor-default select-none px-3 py-1.5 text-gray-700">
                                                    Nessun risultato
                                                </div>

                                                <ComboboxOption
                                                    as="div"
                                                    v-for="user in filteredUsers"
                                                    :key="user"
                                                    :value="user"
                                                    class="px-3 py-1.5 mb-1 ui-active:bg-blue-500 ui-active:text-white ui-active:rounded-md border-b border-b-gray-300"
                                                >
                                                    <div>{{ user.nome }} {{user.cognome}}</div>
                                                    <div class="flex text-sm">
                                                        <div class="mr-1 font-bold">{{ user.type }}</div>
                                                        <div v-if="user.type === 'AGENTE' || user.type === 'INTERMEDIARIO'">- {{ user.agenzia_id }}</div>
                                                        <div v-if="user.type === 'AREAMGR'">- Area {{ user.area }}</div>
                                                        <div v-if="user.type === 'DISTRICTMGR'">- Area {{ user.area }} - District {{user.district}}</div>
                                                    </div>
                                                </ComboboxOption>

                                            </ComboboxOptions>

                                        </transition>

                                    </Combobox>
                                </div>

                            </div>

                            <div class="mt-4 text-right">
                                <button type="button" class="btn secondary" @click="toggleModal">Chiudi</button>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<style scoped>
.toggle {
    @apply fixed bottom-0 right-2 bg-white p-2 pb-4 border-2 border-b-0 border-green-700 cursor-pointer;
    transition: all 0.2s ease-in-out;
    border-top-left-radius: 25px;
    border-top-right-radius: 25px;
    margin-bottom: -10px;
    svg {
        @apply text-green-700;
        transition: all 0.2s ease-in-out;
    }

    &:hover {
        transform: translateY(-10px);
        @apply bg-green-700;
        svg {
            @apply text-white;
        }
    }
}
</style>