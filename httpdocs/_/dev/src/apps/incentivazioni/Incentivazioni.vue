<script setup>
import IncentivazioniCard from './IncentivazioniCard.vue';
import InCorsoIcon from './icons/InCorsoIcon.vue';
import InElaborazioneIcon from './icons/DatiInElaborazioneIcon.vue';
import TerminataIcon from './icons/TerminataIcon.vue';
import LiquidataIcon from './icons/LiquidataIcon.vue';
import { useRoute } from 'vue-router'
import { incentivazioniApi } from "./api/incentivazioni";

const route = useRoute()
const year = route.params.year

const incentive = (await incentivazioniApi.getByYear(year))?.data
    .sort((a, b) => b.id - a.id);

</script>

<template>
  <div class="container">
    <div class="flex justify-between mb-8  flex-wrap">
      <h1 class="text-2xl font-semibold mb-5 md:mb-0">Incentivazioni {{ year }}</h1>
      <div class="flex flex-wrap">
        <p class="flex items-center mr-8">
          <InCorsoIcon class="mr-2" /> In Corso
        </p>
        <p class="flex items-center mr-8">
          <InElaborazioneIcon class="mr-2" /> Dati in elaborazione
        </p>
        <p class="flex items-center mr-8">
          <TerminataIcon class="mr-2" /> Dati Finali
        </p>
        <p class="flex items-center mr-8">
          <LiquidataIcon class="mr-2" /> Liquidata
        </p>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
      <IncentivazioniCard v-for="i in incentive" :incentive="i" />
    </div>
  </div>
</template>
