<script setup>
import { ref } from 'vue';
import {PhCaretDoubleDown, PhCaretDoubleUp} from '@phosphor-icons/vue';
import {raccoltaVincente2025Api} from "@/apps/incentivazioni/2025/raccolta-vincente-2025/api/raccoltaVincente2025Api.js";
import {formatCurrency, formatDate} from "@/libs/formatter.js";

const agencyStatus = ref({
    nuoviClienti: 0  // Initialize with default values
})
const incentiveData = ref('')
const payable = ref(false)
const video1Url = ref('')
const video2Url = ref('')
const video3Url = ref('')
const video1PosFile = `Prima Colonna POS.gif`
const video1NegFile = `Prima Colonna NEG.gif`
const video2PosFile = `Seconda Colonna POS.gif`
const video2NegFile = `Seconda Colonna NEG.gif`
const video3PosFile = `Terza Colonna POS.gif`
const video3NegFile = `Terza Colonna NEG.gif`

refreshStatus()

async function refreshStatus() {
    agencyStatus.value = await raccoltaVincente2025Api.getStatus()
    incentiveData.value = await raccoltaVincente2025Api.lastUpdate()
    payable.value = agencyStatus.value.incentPPT + agencyStatus.value.incentClients + agencyStatus.value.incentOpen >= 750
    video1Url.value = agencyStatus.value.incentPPT + agencyStatus.value.incentClients >= 750 && agencyStatus.value.incentPPT ? video1PosFile : video1NegFile
    video2Url.value = agencyStatus.value.incentPPT + agencyStatus.value.incentClients >= 750 && agencyStatus.value.incentClients ? video2PosFile : video2NegFile
    video3Url.value = agencyStatus.value.incentOpen ? video3PosFile : video3NegFile
}

const showData = ref(false);

const toggleData = () => {
    showData.value = !showData.value;
};

const maxClientiLvl = 5;

const logoUrl = `https://${window.staticHost}/themes/incentivazioni/media/raccolta-vincente-2025/logo.svg`

</script>

<template>
    <div class="lg:px-10 py-5 bg-[#CFEDFB] min-h-screen racc-vinc-2025">
        <section class="bg-[#CFEDFB] min-h-screen container">

            <!-- Header -->
            <div class="flex justify-between items-center text-[#1476BC] mb-5 flex-wrap">
                <img :src="logoUrl" class="object-contain w-[396px] h-[128px] " alt="Raccolta Vincente 2025" />
                <div class="flex items-center divide-x-[2.3px] divide-[#1476BC] space-x-2 h-[40px] pl-1">
                    <a href="/api/apps/raccolta-vincente-2025/policies-detail" class="text-lg font-bold pr-4 text-nowrap ">Dettaglio Polizze</a>
                    <a href="/api/apps/raccolta-vincente-2025/pdf" class="text-lg font-bold pl-4  ">Regolamento</a>
                </div>
            </div>

            <div class="w-full relative hidden lg:grid grid-cols-3 mb-5">
                <img :src="`https://${window.staticHost}/themes/incentivazioni/media/raccolta-vincente-2025/${video1Url}`" alt="" />
                <img :src="`https://${window.staticHost}/themes/incentivazioni/media/raccolta-vincente-2025/${video2Url}`" alt="" />
                <img :src="`https://${window.staticHost}/themes/incentivazioni/media/raccolta-vincente-2025/${video3Url}`" alt="" />
            </div>

            <!-- Footer -->
            <div class="h-[20px] border-[#1476BC]  border-[2.2px] border-t-0 rounded-b-xl px-5 mb-5">
                <span class="block transform -translate-y-3 text-lg uppercase text-[#1476BC] ">
                    Dati aggiornati al: {{formatDate(incentiveData.lastUpdateLabel)}}
                </span>
            </div>

            <div class="hidden lg:grid grid-cols-1 lg:grid-cols-3 border-[#1476BC] border-2">

                <!-- Card 1 -->
                <div class="bg-[#00ADEF] font-bold pt-5 px-5 lg:px-10 pb-2 flex flex-col justify-between">
                    <h3 class="block text-3xl font-bold text-white uppercase ">
                        Investimento e programma per te
                    </h3>
                    <p class="block self-end text-4xl text-white font-black mt-2" :class="{'desaturate': !payable}">{{formatCurrency(agencyStatus.incentPPT)}}</p>
                </div>

                <!-- Card 2 -->
                <div class="bg-[#90288F] font-bold pt-5 px-5 lg:px-10 pb-2 flex flex-col justify-between border-[#1476BC] border-x-2">
                    <h3 class="block text-3xl font-bold text-white uppercase ">
                        Investimento nuovi clienti vita
                    </h3>
                    <p class="block self-end text-4xl text-white font-black mt-2" :class="{'desaturate': !payable}">{{formatCurrency(agencyStatus.incentClients)}}</p>
                </div>

                <!-- Card 3 -->
                <div class="bg-[#8BC640] font-bold pt-5 px-5 lg:px-10 pb-2 flex flex-col justify-between">
                    <h3 class="block text-3xl font-bold text-white uppercase ">
                        Programma Open
                    </h3>
                    <p class="block self-end text-4xl text-white font-black mt-2">{{formatCurrency(agencyStatus.incentOpen)}}</p>
                </div>

            </div>

            <section>
                <section 
                    class="transition-all duration-300 ease-in-out overflow-hidden"
                    :class="showData ? 'max-h-[9999px]' : 'max-h-0'"
                >
                    <section class="grid grid-cols-1 lg:grid-cols-3">

                        <!-- Card 1 -->
                        <div class="bg-[#00ADEF] font-bold pt-5 px-5 lg:px-10 pb-2 mt-10 flex lg:hidden flex-col justify-between">
                            <h3 class="block text-3xl font-bold text-white uppercase ">
                                Investimento e programma per te
                            </h3>
                            <p class="block self-end text-4xl text-white font-black mt-2" :class="{'desaturate': !payable}">{{formatCurrency(agencyStatus.incentPPT)}}</p>
                        </div>

                        <!-- Column 1 -->
                        <div
                            class=" font-bold py-5 flex flex-col mt-3 border-[#1476BC] border-2 border-t-0 px-5 lg:px-10 ">
                            <div class="min-h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                                <h3 class="block text-3xl font-bold text-[#00ADEF] uppercase">
                                    Investimento e programma per te
                                </h3>
                            </div>
                            <div class="flex flex-col pr-16 lg:pr-28">
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Premi</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#00ADEF]">
                                        <p>{{formatCurrency(agencyStatus.premiPPT)}}</p>
                                        <p>X</p>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Aliquota</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#00ADEF]">
                                        <p>0,8% <span class="font-light">(0,4%)*</span></p>
                                        <p> = </p>
                                    </div>
                                </div>
                            </div>
                            <div class="h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                            </div>
                            <div class="">
                                <p class="text-3xl italic text-[#1476BC] font-light">Incentivo</p>
                                <p :class="{'desaturate': !payable}" class="text-4xl text-[#00ADEF] font-black">{{formatCurrency(agencyStatus.incentPPT)}}</p>
                            </div>
                            <div class="relative flex w-full lg:mr-5 mt-5 justify-center items-center h-[150px] md:h-[200px] 2xl:h-[100px] border-[#00ADEF] border-[3px] rounded-xl">
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="top: -3px; height: 3px"></div>
                                <p class="text-xl italic font-light text-[#1476BC] px-5 py-5 text-center relative">
                                    *Ai premi derivanti dal reinvestimento di somme riscattate da polizze emesse tra 5 e 10 anni fa si applica l’aliquota dello 0,4%
                                </p>
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="bottom: -3px; height: 3px"></div>
                            </div>
                        </div>

                        <!-- Card 2 -->
                        <div class="bg-[#90288F] font-bold pt-5 px-5 lg:px-10 pb-2 mt-10 flex lg:hidden flex-col justify-between border-[#1476BC] border-x-2">
                            <h3 class="block text-3xl font-bold text-white uppercase ">
                                Investimento nuovi clienti vita
                            </h3>
                            <p class="block self-end text-4xl text-white font-black mt-2" :class="{'desaturate': !payable}">{{formatCurrency(agencyStatus.incentClients)}}</p>
                        </div>

                        <!-- Column 2 -->
                        <div
                            class=" font-bold py-5 flex flex-col mt-3 border-[#1476BC] border-2 lg:border-x-0 border-t-0 px-5 lg:px-10  ">
                            <div class="min-h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                                <h3 class="block text-3xl font-bold text-[#90288F] uppercase ">
                                    Investimento nuovi clienti vita
                                </h3>
                            </div>
                            <div class="flex flex-col pr-16 lg:pr-28">
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Premi</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#90288F]">
                                        <p>{{formatCurrency(agencyStatus.premiClients)}}</p>
                                        <p>X</p>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Aliquota</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#90288F]">
                                        <div class="flex space-x-3">
                                            <p :class="{'desaturate': agencyStatus.nuoviClienti >= maxClientiLvl}">1,2%</p>
                                            <div class="w-1 min-h-full bg-[#90288F] desaturate"></div>
                                            <p :class="{'desaturate': agencyStatus.nuoviClienti < maxClientiLvl}">1,5%</p>
                                        </div>
                                        <p> = </p>
                                    </div>
                                </div>
                            </div>
                            <div class="h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                                <p class="text-3xl italic text-[#1476BC] font-light">Nuovi Clienti</p>
                                <div class="flex space-x-5">
                                    <div v-for="index in Math.min(agencyStatus.nuoviClienti || 0, maxClientiLvl)" :key="index"
                                         class="rounded-full w-10 h-10 border-4 border-[#1476BC] p-1.5 flex justify-center items-center">
                                        <div class="w-full h-full bg-[#90288F] rounded-full">
                                        </div>
                                    </div>
                                    <div v-for="index in Math.max(maxClientiLvl - (agencyStatus.nuoviClienti || 0), 0)" :key="index"
                                         class="rounded-full w-10 h-10 border-4 border-[#1476BC] p-1.5 flex justify-center items-center">
                                    </div>
                                </div>
                            </div>
                            <div class="">
                                <p class="text-3xl italic text-[#1476BC] font-light">Incentivo</p>
                                <p :class="{'desaturate': !payable}" class="text-4xl text-[#90288F] font-black"> {{formatCurrency(agencyStatus.incentClients)}}</p>
                            </div>
                            <div class="relative flex w-full lg:mr-5 mt-5 justify-center items-center h-[150px] md:h-[200px] 2xl:h-[100px] border-[#90288F] border-[3px] rounded-xl">
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="top: -3px; height: 3px"></div>
                                <p class="text-xl italic font-light text-[#1476BC] px-5 py-5 text-center relative">
                                    I dati relativi ai nuovi Clienti vengono aggiornati mensilmente.
                                </p>
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="bottom: -3px; height: 3px"></div>
                            </div>
                        </div>

                        <!-- Card 3 -->
                        <div class="bg-[#8BC640] font-bold pt-5 px-5 lg:px-10 pb-2 mt-10 flex lg:hidden flex-col justify-between">
                            <h3 class="block text-3xl font-bold text-white uppercase ">
                                Programma Open
                            </h3>
                            <p class="block self-end text-4xl text-white font-black mt-2">{{formatCurrency(agencyStatus.incentOpen)}}</p>
                        </div>

                        <!-- Column 3 -->
                        <div
                            class=" font-bold py-5 flex flex-col mt-3 border-[#1476BC] border-2 border-t-0 px-5 lg:px-10  ">
                            <div class="min-h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                                <h3 class="block text-3xl  w-4/5 font-bold text-[#8BC640] uppercase ">
                                    Programma Open
                                </h3>
                            </div>
                            <div class="flex flex-col pr-16 lg:pr-28">
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Pezzi</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#8BC640]">
                                        <p>{{agencyStatus.pezziOpen}}</p>
                                        <p>X</p>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-3xl italic text-[#1476BC] font-light">Importo</p>
                                    <div class="flex justify-between pb-10 text-3xl font-black text-[#8BC640]">
                                        <p>25,82 €</p>
                                        <p> = </p>
                                    </div>
                                </div>
                            </div>
                            <div class="h-[100px] border-[#1476BC] border-b-[5px] border-dotted pb-5 mb-5 ">
                            </div>
                            <div class="">
                                <p class="text-3xl italic text-[#1476BC] font-light">Incentivo</p>
                                <p class="text-4xl text-[#8BC640] font-black">{{formatCurrency(agencyStatus.incentOpen)}}</p>
                            </div>
                            <div class="relative flex w-full lg:mr-5 mt-5 justify-center items-center h-[150px] md:h-[200px] 2xl:h-[100px] border-[#8BC640] border-[3px] rounded-xl">
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="top: -3px; height: 3px"></div>
                                <p class="text-xl italic font-light text-[#1476BC] px-5 py-5 text-center relative">
                                    I dati relativi ai FPA saranno disponibili solo
                                    al termine dell'incentivazione.
                                </p>
                                <div class="absolute bg-[#CFEDFB] w-10/12 left-1/2 transform -translate-x-1/2" style="bottom: -3px; height: 3px"></div>
                            </div>
                        </div>

                    </section>

                    <section :class="{
                        'lg:hidden block': !showData,
                    }" class=" border-b-2 border-x-2 border-[#1476BC] p-10 pb-5 text-white bg-[#70B1DB] w-full
                        text-4xl  space-y-5">
                        <h3 class="italic font-black uppercase"> BONUS OTP </h3>
                        <section class="grid grid-cols-1 lg:grid-cols-3">
                            <div class="space-y-5 max-w-[70%]">
                                <p class="text-[#CEEDFC] italic"> Polizze valide</p>
                                <div class="flex justify-between font-black">
                                    <p class="text-5xl">{{agencyStatus.otp}}</p>
                                    <p class="text-5xl">X</p>
                                </div>
                            </div>
                            <div class="space-y-5 flex flex-col items-start lg:items-center max-w-[70%]">
                                <p class="text-[#CEEDFC] italic">Importo</p>
                                <div class="flex justify-start lg:justify-start font-black w-full">
                                    <p class="block text-5xl lg:mx-auto"><template v-if="!agencyStatus.premiOtp">0</template><template v-if="agencyStatus.premiOtp">15 €</template></p>
                                    <div class="text-5xl ml-auto"> = </div>
                                </div>
                            </div>
                            <div class="flex justify-start lg:justify-center font-black w-full">
                                <div class="space-y-5">
                                    <p class="text-[#CEEDFC] italic">Bonus totale</p>
                                    <p class="text-5xl">{{formatCurrency(agencyStatus.incentOtp)}}</p>
                                </div>
                            </div>
                        </section>
                        <p class="text-2xl italic font-light">
                            I dati relativi al Bonus OTP saranno disponibili solo al termine
                            dell'incentivazione.
                        </p>
                    </section>

                    <section
                        class=" border-x-2 border-[#1476BC] p-10 pb-5 text-xl italic text-[#1476BC] space-y-5 flex flex-col items-center w-full">
                        <div class="max-w-[700px] relative">

                            <div class="block xl:hidden mx-auto mb-5 bg-white border-2 border-[#1476BC] p-4 rounded-lg shadow-lg w-[200px]">
                                <div class="text-center font-black color-[#1476BC] text-2xl leading-tight not-italic">RISULTATO FINALE</div>
                                <div class="text-center color-[#1f71b8] uppercase leading-tight text-sm not-italic opacity-80">
                                    Elaborato sui dati di Direzione <br> e al netto di eventuali esclusioni <br> o rimodulazioni.
                                </div>
                            </div>

                            <div class="flex justify-between mb-5">
                                <h3 class=" uppercase text-5xl ">INCENTIVO TOTALE</h3>
                                <h3 class="font-black uppercase text-5xl not-italic">{{payable ? formatCurrency(agencyStatus.incentTotal) : '0,00 €'}}</h3>
                            </div>

                            <p class="italic text-xl font-semibold text-center">
                                L’incentivazione sarà liquidata a condizione che l’importo maturato dall’Agenzia
                                (per i prodotti di investimento e per Programma Per Te) sia pari o superiore a 750 euro.
                            </p>

                            <div class="hidden xl:block absolute left-full top-0 ml-4 bg-white border-2 border-[#1476BC] p-4 rounded-lg shadow-lg w-[200px]">
                                <div class="text-center font-black color-[#1476BC] text-2xl leading-tight not-italic">RISULTATO FINALE</div>
                                <div class="text-center color-[#1f71b8] uppercase leading-tight text-sm not-italic opacity-80">
                                    Elaborato sui dati di Direzione <br> e al netto di eventuali esclusioni <br> o rimodulazioni.
                                </div>
                            </div>
                        </div>
                    </section>
                </section>
                <div :class="{
                    'lg:mt-5': !showData,
                }" class="relative flex flex-col justify-start items-center ">
                    <!-- Bottom border with notch -->
                    <div class="border-b-2 border-x-2 border-[#1476BC] rounded-b-xl relative h-[10px] w-full"></div>
                    <!-- Notch / Button -->
                    <div 
                        class="w-44 h-12 border-[2px] bg-[#CFEDFB] border-[#1476BC] border-t-0 rounded-b-xl transform -translate-y-[2px] flex items-start justify-center pt-1 cursor-pointer"
                        @click="toggleData"
                    >
                        <PhCaretDoubleUp 
                            v-if="showData" 
                            class="text-[#1476BC] transition-transform duration-300" 
                            :size="32" 
                        />
                        <PhCaretDoubleDown 
                            v-if="!showData" 
                            class="text-[#1476BC] transition-transform duration-300" 
                            :size="32" 
                        />
                    </div>
                </div>
            </section>

        </section>
    </div>
</template>

<style scoped>
.racc-vinc-2025 {
    font-family: 'Sofia Sans Extra Condensed', sans-serif;
    margin: -25px 0;
}
.desaturate {
    opacity: 0.3;
}
</style>
