<script setup>

    import { ref, unref, watch } from 'vue'
    import { useRoute, useRouter } from "vue-router";
    import { getRouteChildrenPaths } from "@/_common/router/RouterHelpers.js";
    import Menu from "@/_common/components/Menu.vue";
    import { useUserStore } from "@/_common/stores/user.js";

    const authData = useUserStore();

    const years = function () {
        let currentYear = new Date().getFullYear(), years = [];
        let startYear = 2010;
        while (startYear <= currentYear) {
            years.push(startYear++);
        }
        return years.reverse();
    };

    const route = useRoute();
    const router = useRouter();

    let selectedYear = ref(route.params?.year ?? new Date().getFullYear());
    let selectedAgencyId = ref(route.params?.agencyId ?? null);
    if (authData.UTYPE === 'AGENTE') {
        selectedAgencyId.value = authData.AGENZIA;
    }

    watch(route, async (r) => {
        if (route.name === 'advancements-list') {
            selectedAgencyId.value = null;
            onChangeYear(null);
        }

        let newAgency = r?.params?.agencyId;
        if (!newAgency || newAgency == unref(selectedAgencyId)) {
            return;
        }
        selectedAgencyId.value = newAgency;
        onChangeYear(null);
    });

    let menuItems = [
        {
            route: 'advancements-list',
            label: 'Elenco agenzie',
            params: {
                year: unref(selectedYear)
            },
            visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR']
        },
        {
            route: 'advancements-rp',
            label: 'Rami Preferiti',
            disabled: selectedAgencyId.value === null,
            params: {
                year: unref(selectedYear),
                agencyId: unref(selectedAgencyId)
            },
            visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
        },
        {
            route: 'advancements-vt',
            label: 'Vita TCM',
            disabled: selectedAgencyId.value === null,
            params: {
                year: unref(selectedYear),
                agencyId: unref(selectedAgencyId)
            },
            visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
        },
        {
            route: 'advancements-vri',
            label: 'Vita Risparmio e Investimento',
            disabled: selectedAgencyId.value === null,
            params: {
                year: unref(selectedYear),
                agencyId: unref(selectedAgencyId)
            },
            visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
        }
    ];

    function onChangeYear(e)
    {
        if (e instanceof Event) {
            selectedYear.value = e.target.value;
            router.push({ name: route.name, params: { year: unref(selectedYear) }});
        }

        menuItems = [
            {
                route: 'advancements-list',
                label: 'Elenco agenzie',
                params: {
                    year: unref(selectedYear)
                },
                visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR']
            },
            {
                route: 'advancements-rp',
                label: 'Rami Preferiti',
                disabled: selectedAgencyId.value === null,
                params: {
                    year: unref(selectedYear),
                    agencyId: unref(selectedAgencyId)
                },
                visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
            },
            {
                route: 'advancements-vt',
                label: 'Vita TCM',
                disabled: selectedAgencyId.value === null,
                params: {
                    year: unref(selectedYear),
                    agencyId: unref(selectedAgencyId)
                },
                visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
            },
            {
                route: 'advancements-vri',
                label: 'Vita Risparmio e Investimento',
                disabled: selectedAgencyId.value === null,
                params: {
                    year: unref(selectedYear),
                    agencyId: unref(selectedAgencyId)
                },
                visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR', 'DISTRICTMGR', 'AGENTE']
            }
        ];
    }

</script>

<template>
    <div class="container relative">
        <div class="flex items-center gap-x-7 mb-16">
            <div class="font-semibold text-2xl hidden md:block">Avanzamenti Rappel</div>
            <div class="form-group" style="margin-bottom: 0">
                <select v-model="selectedYear" @change="onChangeYear">
                    <option v-for="year in years()" :key="year" :value="year">{{ year }}</option>
                </select>
            </div>
            <Menu id="avanzamenti-menu" :items="menuItems"/>
        </div>
    </div>
    <router-view :key="$route.path" />
</template>

<style>
    #avanzamenti-menu .menu a:hover, #avanzamenti-menu .menu a.active { background-color: #EDF5FF; color: #003366 }
</style>

