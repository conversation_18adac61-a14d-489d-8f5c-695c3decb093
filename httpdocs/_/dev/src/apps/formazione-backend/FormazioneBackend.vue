<script setup>
import Menu from "@/_common/components/Menu.vue";
import {useUserStore} from "@/_common/stores/user.js";
const authData = useUserStore();
const currentYear = (new Date()).getFullYear();

const menuItems = [
    {
        route: 'courses-list',
        label: 'Corsi'
    },
    {
        route: 'verifications',
        label: 'Caricamenti',
        visibility: ['AMMINISTRATORE', 'KA', 'FORMAZIONE', 'DISTRICTMGR', 'ASV']
    },
    {
        route: 'training-hours',
        label: 'Ore formative',
    },
    {
        route: 'locations',
        label: 'Configurazione sedi',
        visibility: ['AMMINISTRATORE', 'KA', 'FORMAZIONE']
    },
    {
        label: 'IVASS',
        visibility: ['KA', 'FORMAZIONE', 'AMMINISTRATORE'],
        children: [
            {
                route: 'ivass-table-saved',
                label: 'Tabella 2',
                params: {
                    nTable: 'table2',
                    year: currentYear
                }
            },
            {
                route: 'ivass-table-saved',
                label: 'Tabella 3',
                params: {
                    nTable: 'table3',
                    year: currentYear
                }
            }
        ]
    },
    {
        visibility: ['KA', 'AMMINISTRATORE', 'FORMAZIONE', 'AREAMGR', 'DISTRICTMGR'],
        route: 'uniqum',
        label: 'Uniqum'
    },
    {
        route: 'product-courses',
        label: 'Formazione prodotto'
    },
    {
        visibility: ['KA', 'AMMINISTRATORE', 'AREAMGR'],
        route: 'area-events',
        label: 'Eventi Territoriali'
    }
]
</script>

<template>
    <div class="container relative">
        <Menu :items="menuItems" class="mb-5"/>
    </div>
<!--    <RouterView :key="$route.path"/>-->
    <RouterView/>
</template>

<style scoped>

</style>
