<script setup>
import {useRoute} from "vue-router";
import {computed, reactive, ref, watch} from "vue";
import {Table} from "@/libs/table.js";
import {courses} from "@/apps/formazione-backend/api/courses.js";
import LoadingOverlay from "@/_common/components/Table/LoadingOverlay.vue";
import {PhPencil, PhUpload, PhUploadSimple} from "@phosphor-icons/vue";
import Sorter from "@/_common/components/Table/Sorter.vue";
import Pagination from "@/_common/components/Table/Pagination.vue";
import {Field, Form} from "vee-validate";
import {useCourseStore} from "@/_common/stores/course.js";
import UserRoleFormatter from "@/apps/formazione-backend/components/UserRoleFormatter.vue";
import {onChangeUpload} from "@/libs/upload.js";
import {myPageAgencies} from "@/apps/my-page/api/myPageAgencies.js";
import {toast} from "vue3-toastify";

const selectedCourseID = useRoute().params.courseId
const courseStore = useCourseStore()
const courseFiltersForm = ref(null)
let upload = ref(null);

const columns = [
    {
        key: 'agencyId',
        label: 'Cod. Agenzia',
    },
    {
        key: 'name',
        label: 'Nome',
    },
    {
        key: 'surname',
        label: 'Cognome',
    },
    {
        key: 'type',
        label: 'Ruolo',
    }
];

const filtered = {
    agencyId: 'text',
    name: 'text',
    surname: 'text',
}

const recipientsTable =  reactive(new Table(
    (opt) => courses.getRecipientsTable(opt, selectedCourseID, courseStore.filters),
    {pageSize: 20}));
await recipientsTable.fetchData();

const onFilterSubmit = async (values) => {
    await recipientsTable.fetchData();
};

watch(
    () => courseStore.filters,
    async (newFilters) => {
        await onFilterSubmit(newFilters);
    },
    { deep: true }
);

function onClickUpload(inputId) {
    document.getElementById(inputId).click();
}

async function refreshData(arg = {}) {
    upload.value = null;
    courseStore.filters.csv = "1"
    const res = await courses.updateRecipients(selectedCourseID)

    if (!res?.success) {
        toast.error('Errore imprevisto durante il salvataggio dei destinatari.')
        return
    }
    toast.success('Lista destinatari aggiornata.')
    await recipientsTable.fetchData();
}
</script>

<template>

    <div class="card-body">

        <div class="grid grid-cols-12 gap-x-5">

            <div class="col-span-3">

                <div class="text-center mb-3">
                    <input id="participantsCsv" type="file" ref="upload" name="file" accept=".csv" style="display: none"
                           url="/api/apps/formazione/file/recipients-csv" @change="onChangeUpload($event, null, refreshData)">
                    <button type="button" class="btn primary w-full flex justify-center" @click="onClickUpload('participantsCsv')">
                        <PhUploadSimple size="20" weight="fill" /> Importa lista
                    </button>
                </div>

                <div class="font-bold text-xl">Filtri:</div>

                <Form @submit="onFilterSubmit" ref="courseFiltersForm">

                    <div class="form-group space-y-2" :class="{'cursor-not-allowed' : courseStore.status !== 'off'}">
                        <label class="flex items-center gap-2" style="font-size: 1.125rem" :class="{'cursor-not-allowed' : courseStore.status !== 'off'}">
                            <input
                                type="checkbox"
                                v-model="courseStore.filters.agents"
                                name="agents"
                                :disabled="courseStore.status !== 'off'"
                                :true-value="'1'" :false-value="'0'"
                            /> Agenti
                        </label>
                        <label class="flex items-center gap-2" style="font-size: 1.125rem" :class="{'cursor-not-allowed' : courseStore.status !== 'off'}">
                            <input
                                type="checkbox"
                                v-model="courseStore.filters.intermediariesRegistered"
                                name="intermediariesRegistered"
                                :disabled="courseStore.status !== 'off'"
                                :true-value="'1'" :false-value="'0'"
                            /> Intermediari - E
                        </label>
                        <label class="flex items-center gap-2" style="font-size: 1.125rem" :class="{'cursor-not-allowed' : courseStore.status !== 'off'}">
                            <input
                                type="checkbox"
                                v-model="courseStore.filters.employeesRegistered"
                                name="employeesRegistered"
                                :disabled="courseStore.status !== 'off'"
                                :true-value="'1'" :false-value="'0'"
                            /> Dipendenti - E
                        </label>
                        <label class="flex items-center gap-2" style="font-size: 1.125rem" :class="{'cursor-not-allowed' : courseStore.status !== 'off'}">
                            <input
                                type="checkbox"
                                v-model="courseStore.filters.employeesUnregistered"
                                name="employeesUnregistered"
                                :disabled="courseStore.status !== 'off'"
                                :true-value="'1'" :false-value="'0'"
                            /> Dipendenti - NON E
                        </label>
                    </div>

                </Form>
            </div>

            <div class="col-span-9">
                <LoadingOverlay :loading="recipientsTable.loading" />
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                        <tr>
                            <th v-for="column in columns" :key="column.key">
                                <div class="th-label cursor-pointer" @click="() => recipientsTable.toggleSort(column.key)">
                                    {{ column.label }}
                                    <Sorter :ordering="recipientsTable.sorters[column.key]"/>
                                </div>
                                <div>
                                    <input class="w-full" v-if="filtered?.[column.key]" :type="filtered[column.key]"
                                           :value="recipientsTable.filters[column.key]?.value" @input="e => recipientsTable.applyFilters({
                                            [column.key]: {
                                                operation: 'LIKE',
                                                value: e.target.value,
                                            }
                                        })" />
                                </div>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="row in recipientsTable.rows" :key="row.id">
                            <td v-for="column in columns" :key="column.key">
                                <template v-if="column.key === 'type'">
                                    <UserRoleFormatter :role="row.role" :type="row.type" />
                                </template>
                                <template v-else>{{row[column.key]}}</template>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-center items-center gap-x-2 py-5" v-if="recipientsTable && recipientsTable.pageQnt > 1">
                    <Pagination :table="recipientsTable" />
                </div>

            </div>

        </div>


    </div>


    
</template>

<style scoped>

</style>