<script setup>
import {useCourseStore} from "@/_common/stores/course.js";
import CourseStatus from "@/apps/formazione-backend/components/CourseStatus.vue";
import {ErrorMessage, Field, Form} from "vee-validate";
import {getYearsArray} from "@/_common/api/helpers/formazione.js";
import {courses} from "@/apps/formazione-backend/api/courses.js";
import {PhFloppyDisk} from "@phosphor-icons/vue";
import {toast} from "vue3-toastify";
import {products} from "@/apps/formazione-backend/api/products.js";

const emit = defineEmits(['courseTitleUpdated'])
const years = getYearsArray()
let course = useCourseStore()
const courseTypes = courses.courseTypesSelect()
const courseModes = courses.courseModesSelect()
const courseSuppliers = courses.courseSupplierSelect()
const productsArray = await products.getAllActive()

async function onCourseSubmit(values) {
    const res = await courses.update(values.id, values);
    if (!res?.success) {
        return toast.error('Si è verificato un errore.');
    }

    // Emetto un evento per aggiornare il titolo del corso che sta nella card
    if (values.title !== course.title) {
        emit('courseTitleUpdated', values.title)
    }

    // Aggiorno il corso nello store
    course.$patch(values)
    toast.success('Corso aggiornato.');
}
</script>

<template>
    <div class="p-5 max-w-3xl mx-auto">

        <h1 class="text-2xl mb-5">Info corso</h1>

        <div class="form-group" v-if="course.groupamaType === 'direz'">
            <label for="status">Stato del corso:</label>
            <CourseStatus :current-status="course.status" :course-id="course.id" />
        </div>

        <Form @submit="onCourseSubmit" ref="courseForm" v-slot="{ errors }" :initial-values="course.$state">

            <div class="form-group">
                <label for="product">Prodotto:</label>
                <Field as="select" name="product_id" rules="required" :disabled="course.groupamaType === 'e-learning'">
                    <option v-for="product in productsArray" :value="product.id">{{product.name}}</option>
                </Field>
            </div>

            <div class="form-group">
                <label for="year">Anno:</label>
                <Field as="select" name="year" rules="required" >
                    <option v-for="year in years" :value="year">{{year}}</option>
                </Field>
                <div class="form-error">
                    <ErrorMessage name="year" />
                </div>
            </div>

            <div class="form-group" v-if="course.groupamaType !== 'e-learning'">
                <label>Titolo:</label>
                <Field type="text" name="title" rules="required" :class="{ 'has-error': errors.title }"/>
                <div class="form-error">
                    <ErrorMessage name="title" />
                </div>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>Sottotitolo:</label>
                <Field type="text" name="subtitle"/>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>TAG:</label>
                <Field type="text" name="tag"/>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'" >
                <label>Durata corso: (giorni)</label>
                <Field type="text" name="data.duration"/>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>Ore formative:</label>
                <Field type="number" name="credits" rules="min_value:0" :class="{ 'has-error': errors.credits }"/>
                <div class="form-error">
                    <ErrorMessage name="credits" />
                </div>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>Numero protocollo:</label>
                <Field type="text" name="code"/>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>Limite agenzia:</label>
                <Field type="checkbox" name="data.limitIsActive" v-model="course.data.limitIsActive" :value="true" :unchecked-value="false" />
                <Field type="number" name="data.agencyLimit" v-if="course.data.limitIsActive" rules="min_value:1" :class="{ 'has-error': errors['data.agencyLimit'] }" />
                <div class="form-error">
                    <ErrorMessage name="data.agencyLimit" />
                </div>
            </div>

            <div class="form-group">
                <label for="tipo">Tipologia</label>
                <Field as="select" name="tipo" rules="required" :class="{ 'has-error': errors.tipo }" >
                    <option v-for="option in courseTypes" :value="option.value">{{option.label}}</option>
                </Field>
                <div class="form-error">
                    <ErrorMessage name="tipo" />
                </div>
            </div>

            <div class="form-group">
                <label for="modalita">Modalità</label>
                <Field as="select" name="modalita" rules="required" :class="{ 'has-error': errors.modalita }" >
                    <option v-for="option in courseModes" :value="option.value">{{option.label}}</option>
                </Field>
                <div class="form-error">
                    <ErrorMessage name="modalita" />
                </div>
            </div>

            <div class="form-group">
                <label for="erogato">Struttura erogante</label>
                <Field as="select" name="erogato" rules="required" :class="{ 'has-error': errors.erogato }">
                    <option v-for="option in courseSuppliers" :value="option.value">{{option.label}}</option>
                </Field>
                <div class="form-error">
                    <ErrorMessage name="erogato" />
                </div>
            </div>

            <div class="form-group" v-if="course.groupamaType !== 'e-learning'">
                <label>Corso valido per adempimento normativa:</label>
                <Field v-slot="{ gdprCheck }" name="gdpr" v-model="course.gdpr">
                    <div class="relative flex gap-x-2" v-bind="gdprCheck">
                        <div class="flex h-6 items-center">
                            <input type="checkbox" id="gdpr" name="gdpr" v-model="course.gdpr" :true-value="1" :false-value="0" />
                        </div>
                        <div class="text-sm leading-6">
                            <label for="gdpr" class="font-medium text-black">GDPR</label>
                        </div>
                    </div>
                </Field>
                <Field v-slot="{ antiCheck }" name="antiriciclaggio" v-model="course.antiriciclaggio">
                    <div class="relative flex gap-x-2" v-bind="antiCheck">
                        <div class="flex h-6 items-center">
                            <input type="checkbox" id="antiriciclaggio" name="antiriciclaggio" v-model="course.antiriciclaggio" :true-value="1" :false-value="0" />
                        </div>
                        <div class="text-sm leading-6">
                            <label for="antiriciclaggio" class="font-medium text-black">Antiriciclaggio</label>
                        </div>
                    </div>
                </Field>
            </div>

            <div class="form-group" v-if="course.groupamaType === 'direz'">
                <label>Gestione iscrizioni da parte dei DM:</label>
                <Field v-slot="{ dmAllowedCheck }" name="dmAllowed" v-model="course.dmAllowed">
                    <div class="relative flex gap-x-2" v-bind="dmAllowedCheck">
                        <div class="flex h-6 items-center">
                            <input type="checkbox" id="dmAllowed" name="dmAllowed" v-model="course.dmAllowed" :true-value="true" :false-value="false" class="h-4 w-4 rounded border-gray-300 text-indigo-600" />
                        </div>
                        <div class="text-sm leading-6">
                            <label for="dmAllowed" class="font-medium text-black">Consentita</label>
                            <!--<p class="text-gray-500">Get notified when someones posts a comment on a posting.</p>-->
                        </div>
                    </div>
                </Field>
            </div>

            <div class="flex justify-end">
                <button type="submit" class="btn success"><PhFloppyDisk size="22" />Salva</button>
            </div>


        </Form>

    </div>
</template>

<style scoped>

</style>