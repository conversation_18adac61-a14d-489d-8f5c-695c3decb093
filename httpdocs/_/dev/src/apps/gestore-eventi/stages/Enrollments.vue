<script setup>
import {events} from "@/apps/gestore-eventi/api/events.js";
import {reactive, ref, watch} from "vue";
import {useRoute, useRouter} from "vue-router";
import {stages} from "@/apps/gestore-eventi/api/stages.js";
import {enrollments} from "@/apps/gestore-eventi/api/enrollments.js";
import {Table} from "@/libs/table.js";
import {PhTrash, PhArrowLeft, PhMicrosoftExcelLogo} from "@phosphor-icons/vue";
import Sorter from "@/_common/components/Table/Sorter.vue";
import {toast} from "vue3-toastify";
import ConfirmModal from "@/_common/components/ConfirmModal.vue";
import {Combobox, ComboboxInput, ComboboxOption, ComboboxOptions} from "@headlessui/vue";
import {processError} from "@/_common/api/helpers/responseParser.js";
import Pagination from "@/_common/components/Table/Pagination.vue";
import UserInfoWidget from "@/_common/components/UserInfoWidget.vue";
import {formatDate} from "@/libs/formatter.js";
import {useUserStore} from "@/_common/stores/user.js";
//import {AutoComplete} from "primevue";
//import AutoComplete from 'primevue/autocomplete'

const columns = [
    {
        key: 'agenzia_id',
        label: 'Agenzia',
    },
    {
        key: 'nome',
        label: 'Nome',
    },
    {
        key: 'cognome',
        label: 'Cognome',
    }
];

const ERRORS = {
    116 : 'Raggiunto limite di registrazioni per questa Agenzia',
    117 : 'L\'utente risulta già registrato a questo evento'
}

const props = defineProps(['eventId', 'stageId'])

let eventTitle = ref(null)
let stagesList = ref([])
let selectedStage = ref(props.stageId)
let userRemovalConfirm = ref(null)
let userToRemove = ref({})
let selectedUser = ref(null)
let filteredUsers = ref([])
const authData = useUserStore();
const enrollmentsTable = reactive(new Table((opt) => enrollments.getAllByStage(opt, selectedStage.value), {pageSize: 20}));
await enrollmentsTable.fetchData();

getEvent()
getStages()

async function getEvent() {
    const event = await events.get(props.eventId);
    eventTitle.value = event.title
}

async function getStages() {
    stagesList.value = (await stages.getAllByEventId(props.eventId)).items;
}

async function onUserRemovalClick(user) {
    userToRemove.value = user;
    userRemovalConfirm.value.openConfirm()
}

async function changeSelectedStage(stageId) {
    selectedStage.value = stageId
    await enrollmentsTable.fetchData();
}

async function deleteUser(isConfirmed) {
    if (isConfirmed) {
        const res = await enrollments.deleteEnrollment(userToRemove.value.id);
        if ( ! res?.success ) {
            return;
        }
        await enrollmentsTable.fetchData();
        await getStages()
        toast.success('Utente rimosso.')
    }
}

async function userSearch(query) {
    const resp = await enrollments.searchUsers(query)
    filteredUsers.value = resp.data;
}

function onUserSearch(value) {
    if (value.length < 3) {
        filteredUsers.value = []
        return
    }

    userSearch(value)
}

watch(selectedUser, async (newValue) => {
    const resp = await enrollments.addUser(selectedStage.value, newValue)
    if (!resp.success) {
        toast.error(processError(resp, ERRORS), {autoClose: false})
        return
    }
    await getStages()
    await enrollmentsTable.fetchData();
    toast.success('Utente prenotato.')
})

</script>

<template>
    <ConfirmModal ref="userRemovalConfirm" :question="`Sei sicuro di voler rimuovere ${userToRemove.nome} ${userToRemove.cognome} dalla tappa?`" @confirmResult="deleteUser($event)" />
    <div class="grid grid-cols-10 gap-x-4">
        <div class="col-span-10 md:col-span-3">

            <button type="button" class="btn secondary w-full mb-3 text-lg flex justify-center" @click="$emit('go-back')"><PhArrowLeft size="22" />Torna a lista tappe</button>

            <div class="card mb-3" v-for="stage in stagesList" :class="{'highlight-card' : stage.id === Number(selectedStage)}">
                <div class="p-4">
                    <button type="button" class="highlight mb-3" @click="changeSelectedStage(stage.id)">{{stage.city}} - {{stage.name}}</button>
                    <div class="mb-3">Data: <span class="font-bold">{{ formatDate(stage.day) }}</span></div>
                    <div class="">Posti disponibili: <span class="font-bold">{{ stage.seats - stage.enrolledTotal }}/{{stage.seats}}</span></div>
                </div>
            </div>

        </div>
        <div class="col-span-10 md:col-span-7">

<!--            <AutoComplete
                v-model="query" :suggestions="filteredUsers"
                @complete="userSearch" @option-select="onUserSelect" unstyled
                pt:root="relative bg-white mb-3"
                pt:pcInputText="w-full py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 border-2 rounded-lg"
                pt:InputText="w-full py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 border-2 rounded-lg">
                <template #option="slotProps">
                    <div>{{ slotProps.option.agenzia_id }} - {{ slotProps.option.type }}</div>
                    <div>{{ slotProps.option.nome }} - {{ slotProps.option.cognome }}</div>
                </template>
            </AutoComplete>-->

            <div class="flex gap-x-3 items-center mb-3">
                <Combobox as="div" v-model="selectedUser" class="relative bg-white grow">

                    <ComboboxInput
                        class="w-full py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 border-2 rounded-lg"
                        placeholder="Digita cognome o cod. Agenzia per aggiungere un partecipante"
                        @change="onUserSearch($event.target.value)"/>

                    <transition
                        enter-active-class="transition duration-100 ease-out"
                        enter-from-class="transform scale-95 opacity-0"
                        enter-to-class="transform scale-100 opacity-100"
                        leave-active-class="transition duration-75 ease-out"
                        leave-from-class="transform scale-100 opacity-100"
                        leave-to-class="transform scale-95 opacity-0"
                    >

                        <ComboboxOptions class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white p-2 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-10">

                            <div v-if="filteredUsers?.length === 0" class="relative cursor-default select-none px-3 py-1.5 text-gray-700">
                                Nessun risultato
                            </div>

                            <ComboboxOption
                                as="div"
                                v-for="user in filteredUsers"
                                :key="user"
                                :value="user"
                                class="option px-3 py-1.5 mb-1 ui-active:bg-blue-500 ui-active:text-white ui-active:rounded-md border-b border-b-gray-300"
                            >
                                <div>{{ user.nome }} {{user.cognome}}</div>
                                <div class="flex text-sm">
                                    <div class="mr-1 font-bold">{{ user.type }}</div>
                                    <div v-if="user.type === 'AGENTE' || user.type === 'INTERMEDIARIO'">- {{ user.agenzia_id }}</div>
                                </div>
                            </ComboboxOption>

                        </ComboboxOptions>

                    </transition>

                </Combobox>
                <div class="" v-if="['KA', 'AMMINISTRATORE', 'FORMAZIONE'].includes(authData.UTYPE)">
                    <a :href="'/api/apps/event-manager/stages/summary/' + props.stageId" class="btn service btn-icon"><PhMicrosoftExcelLogo size="22" /> Scarica Excel</a>
                </div>
            </div>

            <div class="card">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                        <tr>
                            <th style="width: 50px; text-align: center"></th>
                            <th class="th-label cursor-pointer"
                                @click="() => enrollmentsTable.toggleSort(column.key)"
                                v-for="column in columns" :key="column.key">
                                {{ column.label }}
                                <Sorter :ordering="enrollmentsTable.sorters[column.key]"/>
                            </th>
                            <th>
                                Azioni
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(row, index) in enrollmentsTable.rows" :key="row.id">
                            <td>
                                <UserInfoWidget :user-data="{id: row['id'], email: row['email'], telefono: row['cellulare']}" />
                            </td>
                            <td v-for="column in columns" :key="column.key">
                                <template v-if="column.key === 'agenzia_id'">
                                    {{ row['agenzia_id'] }} - {{ row['localita'] }}
                                </template>
                                <span class="font-bold" v-else-if="column.key === 'nome'">{{row[column.key]}}</span>
                                <span class="font-bold" v-else-if="column.key === 'cognome'">{{row[column.key]}}</span>
                                <template v-else>
                                    {{ row[column.key] }}
                                </template>
                            </td>
                            <td>
                                <button type="button" class="btn secondary btn-icon" v-tooltip="'Rimuovi partecipante'" @click="onUserRemovalClick(row)"><PhTrash size="20"/></button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex justify-center items-center gap-x-2 py-4 border-t" v-if="enrollmentsTable && enrollmentsTable.pageQnt > 1">
                    <Pagination :table="enrollmentsTable" />
                </div>
            </div>

        </div>
    </div>
</template>

<style scoped>
.card {
    height: auto;
    @apply border-2 shadow;
}
.highlight-card {
    @apply border-2 border-blue-500 shadow-lg;
}
.highlight {
    font-weight: 700;
    color: #0b3cb8;
}
.highlight:hover {
    color: #072c88;
    text-decoration: underline;
}
</style>