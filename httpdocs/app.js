(function(ng, module) {

    ng.module('xng.ui.nav').constant('xng.ui.nav.CONFIG', {
        api: '/api/auth/navbar', // customize with your URL
        webStorage: false // can be: false | session | local
    });

    module.config(['$compileProvider', 'xng.$authProvider', 'xng.$dataProvider', 'flowFactoryProvider', function($compileProvider, $authProvider, $dataProvider, flowFactoryProvider) {
        $compileProvider.debugInfoEnabled(true);

        $authProvider.setApi({
            data: '/api/auth/data',
            login: '/api/auth/login',
            logout: '/api/auth/logout'
        });

        flowFactoryProvider.factory = fustyFlowFactory; // needed for IE9
        flowFactoryProvider.defaults = {
            //target: '/api/upload/',
            permanentErrors: [500, 501],
            maxChunkRetries: 1,
            chunkRetryInterval: 5000,
            simultaneousUploads: 1
        };
    }]);

    module.run(['$state', '$rootScope', '$http', 'xng.$auth', '$window', '$location', 'editableOptions', 'editableThemes', 'xng.ui.$growl', function($state, $rootScope, $http, $auth, $window, $location, xeditableOpts, xeditableThemes, $growl) {
        // authentication
        $rootScope.$auth = false;
        $rootScope.$on('xng.auth:unauthorized', function() {
            $rootScope.$auth = false;
        });
        $rootScope.$on('xng.auth:init', function(event, authData) {
            $rootScope.$auth = (authData) ? true : false;
            if (!authData) return;

            $rootScope.$authData = authData;
            $http.get('/api/auth/servicesTokens').then(function(response) {
                $rootScope.authTokens = response.data.data;
            });
            // password check
            /*
            if(authData && authData.PWD_RESET) {
                $state.go('account');
                if(authData.PWD_RESET == 'EMPTY') $growl.error('ATTENZIONE: è necessario aggiornare la propria password!', { icon: 'icon-warning', ttl: 30000 });
                if(authData.PWD_RESET == 'EXPIRED') $growl.error('ATTENZIONE: sono passati 6 mesi, è necessario aggiornare la propria password!', { icon: 'icon-warning', ttl: 30000 });
            }
            */

            // GHOST switch
            $rootScope.UI_GHOST = (/^(KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR|FOR|ASV|FORMAZIONE)$/.test(authData.UTYPE));
            $rootScope.UI_GHOST_PANEL = false;
            $rootScope.toggleGhost = function() {
                $rootScope.UI_GHOST_PANEL = !$rootScope.UI_GHOST_PANEL;
            }

        });
        $rootScope.$on('xng.auth:logout', function() {
            window.location.href = '/';
        });
        $auth.init();
        xeditableOpts.theme = '';
        xeditableThemes['default'].submitTpl = '<button type="submit" class="x-btn x-green"><i class="icon-checkmark"></i></button>';
        xeditableThemes['default'].cancelTpl = '<button type="button" ng-click="$form.$cancel()" class="x-btn x-red"><i class="icon-close"></i></button>';


        $rootScope.resolution = screen.width + 'x' + screen.height;
        $rootScope.staticHost = $window.staticHost;
        $rootScope.$on('$locationChangeSuccess', function() {
            var url = $location.url();
            if (url.match(/^\/apps/)) {
                $rootScope.cssContext = 'area-gest';
            } else if (url.match(/^\/download/)) {
                $rootScope.cssContext = 'area-download';
            } else if (url.match(/^\/formazione/)) {
                $rootScope.cssContext = 'area-formaz';
            } else if (url.match(/^\/gare/)) {
                $rootScope.cssContext = 'area-gare';
            } else if (url.match(/^\/help-desk/)) {
                $rootScope.cssContext = 'area-helpdesk';
            } else if (url.match(/^\/incentivazioni/)) {
                $rootScope.cssContext = 'area-incentivazioni';
            } else if (url.match(/^\/marketing/)) {
                $rootScope.cssContext = 'area-marketing';
            } else if (url.match(/^\/news/)) {
                $rootScope.cssContext = 'area-news';
            } else if (url.match(/^\/iniz-comm/)) {
                $rootScope.cssContext = 'area-iniz-comm';
            } else {
                $rootScope.cssContext = 'dashboard';
            }
        });
        $state.transitionTo('dashboard');
    }]);

    module.controller('app.LoginCtrl', ['$scope', '$http', function($scope, $http) {
        $scope.pwdForm = {
            active: false,
            email: null,
            running: false,
            error: null,
            ok: false
        };
        $scope.pwdRescue = function() {
            $scope.pwdForm.running = true;
            $scope.pwdForm.error = null;
            $http({ method: 'POST', url: '/api/auth/password-rescue', data: { email: $scope.pwdForm.email } })
                .success(function() {
                    $scope.pwdForm.running = false;
                    $scope.pwdForm.ok = true;
                })
                .error(function(data) {
                    $scope.pwdForm.running = false;
                    $scope.pwdForm.error = true;
                    switch (data.errCode) {
                        case 'EMAIL_REGEX': $scope.pwdForm.error = 'La email inserita non è un formato valido!'; break;
                        case 'EMAIL_MISMATCH': $scope.pwdForm.error = 'La email inserita non risulta associata a nessun utente!'; break;
                        case 'EXCEPTION': $scope.pwdForm.error = 'Errore imprevisto'; break;
                    }
                });
        };
    }]);

    module.controller('app.GhostCtrl', ['$scope', '$http', 'xng.$data', 'xng.ui.$growl', function($scope, $http, $data, $growl) {
        var AdminRepository = $data.repository('User');
        var UsersRepository = $data.repository('Ghost');
        $scope.ghost = {};
        $scope.roles = ['AMMINISTRATORE', 'DIREZ', 'AREAMGR', 'DISTRICTMGR', 'FOR', 'ASV', 'FORMAZIONE', 'UNIVERSE', 'PROMOTICA', 'OTHERS', 'AREAMGR_COLLAB', 'BANCHE', 'IVASS', 'FORMAZ_SIWEB', 'MYPAGE'];
        $scope.adminArray = $scope.usersArray = [];
        $scope.loading = false;

        $scope.doGhost = function(userID) {
            $scope.loading = true;
            $http.post('/api/auth/loginGhost', { id: userID }).then(function(response) {
                if (response.data.success) window.location.href = '/';
                else {
                    $growl.error('Accesso GHOST proibito!', { icon: 'icon-warning', ttl: 3000 });
                    $scope.loading = false;
                }
            });
        };

        // KA super-GHOST selector
        $scope.filterRoles = function() {
            AdminRepository.fetchAll(1, null, 'cognome.ASC', 'active,EQ,1|type,EQ,' + $scope.ghost.type, 'basic').then(function(data) {
                $scope.adminArray = data;
            });
        };

        // GROUPAMA GHOST selector
        $scope.search = function(query) {
            if (query.length < 3) return;
            UsersRepository.fetchAll(1, null, null, query).then(function(data) {
                $scope.usersArray = data;
            });
        };
    }]);


})(angular, angular.module('app', [
	'ngAnimate', 'ngMessages', 'ngSanitize', 'ui.router', 'ui.select',
	'flow', 'xeditable', 'checklist-model', // 'ngCkeditor',
	'xng', 'xng.auth', 'xng.data', 'xng.storage', 'xng.ui', 'xng.ui.form', 'xng.ui.growl', 'xng.ui.modal', 'xng.ui.nav', 'xng.ui.tabs',
	'app.config', 'app.dashboard', 'app.account', 'app.privacy',
	// main sections
	'app.download', 'app.formazione', 'app.gare', 'app.help-desk', 'app.incentivazioni', 'app.marketing', 'app.news', 'app.iniziative',
	// control panel
	'app.admin.agenzie', 'app.admin.iniziative', 'app.admin.news', 'app.admin.quarantena', 'app.admin.rassegnastampa', 'app.admin.users',
	// apps
	'app.apps', 'app.apps.anagrafica', 'app.apps.contributi', 'app.apps.reportistica', 'app.apps.santino', 'app.apps.tassozero', 'app.apps.welcomeback', 'app.apps.pianiagenzia2', 'app.apps.ivass', 'app.apps.avanzamenti', 'app.apps.neo', 'app.apps.webinar', 'app.apps.gdpr', 'app.apps.antiriciclaggio', 'app.apps.accordo2', 'app.apps.agit',
    // MyPlace banner bridge
    'app.myplace',
    'app.incentive'
]));


(function(ng, module) {
    // @FIXME this APP is required to create shared Repositories & Stores
    // This can NOT be done inside main app.config(), because is the last module to be configured, after all sub-modules that requires (and will fail) these Repositories & Stores
    module.config(['xng.$dataProvider', function($dataProvider) {
        $dataProvider
            // GEO
            .repository('geo.Area', { pkey: 'id', url: '/api/rest-api/geo.Aree' })
            .repository('geo.District', { pkey: 'id', url: '/api/rest-api/geo.Districts' })
            .repository('geo.Regione', { pkey: 'id', url: '/api/rest-api/geo.Regioni' })
            .repository('geo.Provincia', { pkey: 'id', url: '/api/rest-api/geo.Province' })
            .repository('geo.Comune', { pkey: 'id', url: '/api/rest-api/geo.Comuni' })
            // others
            .repository('Agenzia', { pkey: 'id', url: '/api/rest-api/Agenzie' })
            .repository('User', { pkey: 'id', url: '/api/users' })
            .repository('Ghost', { pkey: 'id', url: '/api/users/ghosts' })
            .repository('News', { pkey: 'id', url: '/api/rest-api/cms.News' })
            .repository('incentivazioni.Incentivazione', { pkey: 'id', url: '/api/incentivazioni' })
            ;
    }]);

    // this will be included in future xng release
    module.filter('dynFilter', ['$interpolate', function($interpolate) {
        return function(item, name) {
            if (!name) return item;
            var result = $interpolate('{{value | ' + arguments[1] + '}}');
            return result({ value: arguments[0] });
        };
    }]);

    module.factory('$dashboardLocalStorage', ['$window', function($window) {
        return {
            set: function(key, value) {
                $window.localStorage[key] = value;
            },
            get: function(key, defaultValue) {
                return $window.localStorage[key] || defaultValue;
            },
            setObject: function(key, value) {
                $window.localStorage[key] = JSON.stringify(value);
            },
            getObject: function(key) {
                return JSON.parse($window.localStorage[key] || '{}');
            },
            clear: function () {
                $window.localStorage.clear();
            }
        }
    }]);

})(angular, angular.module('app.config', []));
