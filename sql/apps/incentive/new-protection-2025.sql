DROP TABLE IF EXISTS `inc_newpro_2025_static`;
CREATE TABLE `inc_newpro_2025_static` (
    `agenzia_id` char(4) NOT NULL,
    `active` tinyint(1) NOT NULL,
    `fascia` ENUM('A','B','C','D','E', 'F') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_newpro_2025_static`
    ADD PRIMARY KEY (`agenzia_id`);

ALTER TABLE `inc_newpro_2025_static`
    ADD CONSTRAINT `inc_newpro_2025_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP TABLE IF EXISTS `inc_newpro_2025_data`;
CREATE TABLE `inc_newpro_2025_data` (
    `agenzia_id` char(4) NOT NULL,
    `polizza` varchar(20) NOT NULL,
    `codiceProdotto` varchar(6) NOT NULL,
    `premio` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_newpro_2025_data`
    ADD PRIMARY KEY (`agenzia_id`, `polizza`, `codiceProdotto`);

ALTER TABLE `inc_newpro_2025_data`
    ADD CONSTRAINT `inc_newpro_2025_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP TABLE IF EXISTS `inc_newpro_2025_status`;
CREATE TABLE `inc_newpro_2025_status` (
    `agenzia_id` char(4) NOT NULL,
    `pezziInfortuni` smallint UNSIGNED NOT NULL,
    `pezziSalute` smallint UNSIGNED NOT NULL,
    `premioInfortuni` DECIMAL(10,2) NOT NULL,
    `premioSalute` DECIMAL(10,2) NOT NULL,
    `incentInfortuni` DECIMAL(10,2) NOT NULL,
    `incentSalute` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_newpro_2025_status`
    ADD PRIMARY KEY (`agenzia_id`);

ALTER TABLE `inc_newpro_2025_status`
    ADD CONSTRAINT `inc_newpro_2025_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS `vw_inc_newpro_2025_status`;
CREATE VIEW `vw_inc_newpro_2025_status` AS
SELECT
    s.agenzia_id, sc.fascia, a.localita, a.nome, a.area, ga.nome as areaName, a.district, gd.nome as districtName,
    s.pezziInfortuni, s.pezziSalute, s.premioInfortuni, s.premioSalute, s.incentInfortuni, s.incentSalute, (s.incentInfortuni + s.incentSalute) AS incentTot
FROM `inc_newpro_2025_status` s
join inc_newpro_2025_static sc on sc.agenzia_id = s.agenzia_id
join agenzie a on a.id = s.agenzia_id
join geo_aree ga on a.area = ga.id
join geo_districts gd on a.district = gd.id;

DROP VIEW IF EXISTS `vw_inc_newpro_2025_data`;
CREATE VIEW `vw_inc_newpro_2025_data` AS
SELECT pc.*, d.premio FROM inc_newpro_2025_data d
LEFT JOIN pc_data pc ON
    pc.agenzia_id = d.agenzia_id AND
    pc.polizza = d.polizza AND
    pc.codiceProdotto = d.codiceProdotto;
