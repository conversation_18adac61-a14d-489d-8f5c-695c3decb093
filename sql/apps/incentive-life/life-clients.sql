DROP TABLE IF EXISTS life_clients;
CREATE TABLE `life_clients` (
    `id` MEDIUMINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `CF_PIVA` VARCHAR(64) NOT NULL,
    `flag_tcm_ltc` tinyint(1) NOT NULL,
    `flag_risparmio` tinyint(1) NOT NULL,
    `flag_cliente_vita` tinyint(1) NOT NULL,
    `flag_cliente_fpa` tinyint(1) NOT NULL,
    `flag_auto` tinyint(1) NOT NULL,
    `flag_persona` tinyint(1) NOT NULL,
    `flag_casa_fam` tinyint(1) NOT NULL,
    `flag_attivita` tinyint(1) NOT NULL,
    `flag_altro` tinyint(1) NOT NULL,
    `flag_risparmio_202306` tinyint(1) NOT NULL,
    `flag_cli_ner_202307` tinyint(1) NOT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_CF_PIVA` (`CF_PIVA`),
    INDEX `idx_risparmio_new` (`flag_risparmio_202306`, `flag_cli_ner_202307`),
    INDEX `idx_flags` (`flag_tcm_ltc`, `flag_risparmio`, `flag_cliente_vita`, `flag_cliente_fpa`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP VIEW IF EXISTS vw_inc_raccolta_2025_clients;
CREATE VIEW vw_inc_raccolta_2025_clients AS
SELECT
    CF_PIVA AS CF_PIVA,
    SUM(flag_tcm_ltc) AS 'flag_tcm_ltc',
    SUM(flag_risparmio) AS 'flag_risparmio',
    SUM(flag_cliente_vita) AS 'flag_cliente_vita',
    SUM(flag_cliente_fpa) AS 'flag_cliente_fpa',
    SUM(flag_risparmio_202306) AS 'flag_risparmio_202306',
    SUM(flag_cli_ner_202307) AS 'flag_cli_ner_202307'
FROM life_clients
GROUP BY CF_PIVA;