drop table if exists inc_raccolta_2025_static;
CREATE TABLE `inc_raccolta_2025_static` (
                                            `agenzia_id` VARCHAR(4) NOT NULL,
                                            `active` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_static`
    ADD PRIMARY KEY (`agenzia_id`),
    ADD KEY `agenzia_id` (`agenzia_id`);

ALTER TABLE `inc_raccolta_2025_static`
    ADD CONSTRAINT `inc_raccolta_2025_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP TABLE IF EXISTS inc_raccolta_2025_data;
CREATE TABLE `inc_raccolta_2025_data` (
    `agenzia_id` VARCHAR(4) NOT NULL,
    `polizza` varchar(20) NOT NULL,
    `codice<PERSON>rodotto` varchar(6) NOT NULL,
    `CF_PIVA` varchar(64) NOT NULL,
    `tipoPremioQuietanza` VARCHAR(50) NOT NULL,
    `dataContabile` DATE NOT NULL,
    `dataIncasso_check` DATE NOT NULL,
    `premio` DECIMAL(10,2) NOT NULL,
    `esclusa` tinyint(1) NOT NULL DEFAULT 0,
    `premioRidotto` tinyint(1) NOT NULL DEFAULT 0,
    `aliqRidotta` tinyint(1) NOT NULL DEFAULT 0,
    `parziale` ENUM('04', '08', '-') NOT NULL DEFAULT '-'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_data`
    ADD PRIMARY KEY (`agenzia_id`, `polizza`, `codiceProdotto`, `CF_PIVA`, `tipoPremioQuietanza`, `dataContabile`, `dataIncasso_check`, `parziale`),
    ADD KEY `fk_agenzie` (`agenzia_id`);

ALTER TABLE `inc_raccolta_2025_data`
    ADD CONSTRAINT `fk_inc_raccolta_2025_data_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP TABLE IF EXISTS `inc_raccolta_2025_data_fpa`;
CREATE TABLE `inc_raccolta_2025_data_fpa` (
    `agenzia_id` VARCHAR(4) NOT NULL,
    `numModuloAdesione` varchar(10) NOT NULL,
    `pezzi` smallint UNSIGNED NOT NULL,
    `premio` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_data_fpa`
    ADD PRIMARY KEY (`agenzia_id`, `numModuloAdesione`),
    ADD KEY `fk_agenzie` (`agenzia_id`);

ALTER TABLE `inc_raccolta_2025_data_fpa`
    ADD CONSTRAINT `fk_inc_raccolta_2025_data_fpa_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP TABLE IF EXISTS `inc_raccolta_2025_data_otp`;
CREATE TABLE `inc_raccolta_2025_data_otp` (
    `polizza` varchar(20) NOT NULL,
    `codiceProdotto` varchar(6) NOT NULL,
    `dataDecorrenza` DATE NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_data_otp`
    ADD PRIMARY KEY (`polizza`, `codiceProdotto`);

DROP TABLE IF EXISTS inc_raccolta_2025_data_exclusion_reduction;
CREATE TABLE `inc_raccolta_2025_data_exclusion_reduction`(
    `agenzia_id` VARCHAR(4) NOT NULL,
    `tpDoc` varchar(3) NOT NULL,
    `polizza` varchar(20) NOT NULL,
    `codiceProdotto` varchar(6) NOT NULL,
    `premio04` DECIMAL(10,2) NOT NULL,
    `premio08` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_data_exclusion_reduction`
    ADD PRIMARY KEY (`agenzia_id`, `polizza`, `codiceProdotto`, `tpDoc`),
    ADD KEY `fk_agenzie` (`agenzia_id`);

ALTER TABLE `inc_raccolta_2025_data_exclusion_reduction`
    ADD CONSTRAINT `fk_inc_raccolta_2025_data_exclusion_reduction_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

drop table if exists inc_raccolta_2025_status;
CREATE TABLE `inc_raccolta_2025_status` (
    `agenzia_id` VARCHAR(4) NOT NULL,
    `premiPPT` DECIMAL(10,2) NOT NULL,
    `premiClients` DECIMAL(10,2) NOT NULL,
    `pezziOpen` smallint UNSIGNED NOT NULL,
    `nuoviClienti` smallint UNSIGNED NOT NULL,
    `incentPPT` DECIMAL(10,2) NOT NULL,
    `incentClients` DECIMAL(10,2) NOT NULL,
    `incentOpen` DECIMAL(10,2) NOT NULL,
    `otp` smallint UNSIGNED NOT NULL,
    `premiOtp` DECIMAL(10,2) NOT NULL,
    `incentOtp` DECIMAL(10,2) NOT NULL,
    `incentTotal` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_raccolta_2025_status`
    ADD PRIMARY KEY (`agenzia_id`),
    ADD KEY `agenzia_id` (`agenzia_id`);

ALTER TABLE `inc_raccolta_2025_status`
    ADD CONSTRAINT `inc_raccolta_2025_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS `vw_inc_raccolta_2025_data`;
CREATE VIEW `vw_inc_raccolta_2025_data` AS
SELECT DISTINCT
    d.agenzia_id, d.polizza, d.codiceProdotto, 
    l.nomeProdotto, 
    d.CF_PIVA,
    d.dataContabile, 
    l.dataScadenza, 
    l.dataEffetto, 
    l.dataEmissione, 
    l.dataIncasso,
    d.tipoPremioQuietanza, 
    l.tipoPremioPolizza, 
    d.premio
FROM inc_raccolta_2025_data d
LEFT JOIN life_data l ON 
    l.agenzia_id = d.agenzia_id AND 
    l.polizza = d.polizza AND 
    l.codiceProdotto = d.codiceProdotto AND
    l.tipoPremioQuietanza = d.tipoPremioQuietanza AND
    l.dataContabile = d.dataContabile;

DROP VIEW IF EXISTS `vw_inc_raccolta_2025_status`;
CREATE VIEW `vw_inc_raccolta_2025_status` AS
SELECT
    s.agenzia_id, a.localita, a.nome, a.area, ga.nome as areaName, a.district, gd.nome as districtName,
    s.premiPPT, s.premiClients, s.pezziOpen, s.nuoviClienti, s.incentPPT, s.incentClients, s.incentOpen, s.otp, s.premiOtp, s.incentOtp, s.incentTotal
FROM `inc_raccolta_2025_status` s
join agenzie a on a.id = s.agenzia_id
join geo_aree ga on a.area = ga.id
join geo_districts gd on a.district = gd.id;
